// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronVoiceBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronVoiceBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONVOICEBRIDGE_API UClass* Z_Construct_UClass_UAuracronVoiceBridge();
AURACRONVOICEBRIDGE_API UClass* Z_Construct_UClass_UAuracronVoiceBridge_NoRegister();
AURACRONVOICEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType();
AURACRONVOICEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality();
AURACRONVOICEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState();
AURACRONVOICEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature();
AURACRONVOICEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature();
AURACRONVOICEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature();
AURACRONVOICEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature();
AURACRONVOICEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronVoiceChannel();
AURACRONVOICEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronVoiceConfiguration();
AURACRONVOICEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronVoiceParticipant();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
UPackage* Z_Construct_UPackage__Script_AuracronVoiceBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronVoiceChannelType *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronVoiceChannelType;
static UEnum* EAuracronVoiceChannelType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronVoiceChannelType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronVoiceChannelType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType, (UObject*)Z_Construct_UPackage__Script_AuracronVoiceBridge(), TEXT("EAuracronVoiceChannelType"));
	}
	return Z_Registration_Info_UEnum_EAuracronVoiceChannelType.OuterSingleton;
}
template<> AURACRONVOICEBRIDGE_API UEnum* StaticEnum<EAuracronVoiceChannelType>()
{
	return EAuracronVoiceChannelType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Broadcast.DisplayName", "Broadcast" },
		{ "Broadcast.Name", "EAuracronVoiceChannelType::Broadcast" },
		{ "Coach.DisplayName", "Coach" },
		{ "Coach.Name", "EAuracronVoiceChannelType::Coach" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de canal de voz\n */" },
#endif
		{ "Emergency.DisplayName", "Emergency" },
		{ "Emergency.Name", "EAuracronVoiceChannelType::Emergency" },
		{ "Global.DisplayName", "Global Chat" },
		{ "Global.Name", "EAuracronVoiceChannelType::Global" },
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronVoiceChannelType::None" },
		{ "Party.DisplayName", "Party Chat" },
		{ "Party.Name", "EAuracronVoiceChannelType::Party" },
		{ "Proximity.DisplayName", "Proximity Chat" },
		{ "Proximity.Name", "EAuracronVoiceChannelType::Proximity" },
		{ "Team.DisplayName", "Team Chat" },
		{ "Team.Name", "EAuracronVoiceChannelType::Team" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de canal de voz" },
#endif
		{ "Whisper.DisplayName", "Whisper" },
		{ "Whisper.Name", "EAuracronVoiceChannelType::Whisper" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronVoiceChannelType::None", (int64)EAuracronVoiceChannelType::None },
		{ "EAuracronVoiceChannelType::Team", (int64)EAuracronVoiceChannelType::Team },
		{ "EAuracronVoiceChannelType::Proximity", (int64)EAuracronVoiceChannelType::Proximity },
		{ "EAuracronVoiceChannelType::Global", (int64)EAuracronVoiceChannelType::Global },
		{ "EAuracronVoiceChannelType::Party", (int64)EAuracronVoiceChannelType::Party },
		{ "EAuracronVoiceChannelType::Whisper", (int64)EAuracronVoiceChannelType::Whisper },
		{ "EAuracronVoiceChannelType::Broadcast", (int64)EAuracronVoiceChannelType::Broadcast },
		{ "EAuracronVoiceChannelType::Emergency", (int64)EAuracronVoiceChannelType::Emergency },
		{ "EAuracronVoiceChannelType::Coach", (int64)EAuracronVoiceChannelType::Coach },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronVoiceBridge,
	nullptr,
	"EAuracronVoiceChannelType",
	"EAuracronVoiceChannelType",
	Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType()
{
	if (!Z_Registration_Info_UEnum_EAuracronVoiceChannelType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronVoiceChannelType.InnerSingleton, Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronVoiceChannelType.InnerSingleton;
}
// ********** End Enum EAuracronVoiceChannelType ***************************************************

// ********** Begin Enum EAuracronVoiceQuality *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronVoiceQuality;
static UEnum* EAuracronVoiceQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronVoiceQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronVoiceQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality, (UObject*)Z_Construct_UPackage__Script_AuracronVoiceBridge(), TEXT("EAuracronVoiceQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronVoiceQuality.OuterSingleton;
}
template<> AURACRONVOICEBRIDGE_API UEnum* StaticEnum<EAuracronVoiceQuality>()
{
	return EAuracronVoiceQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para qualidade de voz\n */" },
#endif
		{ "High.DisplayName", "High Quality (24kHz)" },
		{ "High.Name", "EAuracronVoiceQuality::High" },
		{ "Low.DisplayName", "Low Quality (8kHz)" },
		{ "Low.Name", "EAuracronVoiceQuality::Low" },
		{ "Medium.DisplayName", "Medium Quality (16kHz)" },
		{ "Medium.Name", "EAuracronVoiceQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para qualidade de voz" },
#endif
		{ "Ultra.DisplayName", "Ultra Quality (48kHz)" },
		{ "Ultra.Name", "EAuracronVoiceQuality::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronVoiceQuality::Low", (int64)EAuracronVoiceQuality::Low },
		{ "EAuracronVoiceQuality::Medium", (int64)EAuracronVoiceQuality::Medium },
		{ "EAuracronVoiceQuality::High", (int64)EAuracronVoiceQuality::High },
		{ "EAuracronVoiceQuality::Ultra", (int64)EAuracronVoiceQuality::Ultra },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronVoiceBridge,
	nullptr,
	"EAuracronVoiceQuality",
	"EAuracronVoiceQuality",
	Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronVoiceQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronVoiceQuality.InnerSingleton, Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronVoiceQuality.InnerSingleton;
}
// ********** End Enum EAuracronVoiceQuality *******************************************************

// ********** Begin Enum EAuracronVoiceState *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronVoiceState;
static UEnum* EAuracronVoiceState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronVoiceState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronVoiceState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState, (UObject*)Z_Construct_UPackage__Script_AuracronVoiceBridge(), TEXT("EAuracronVoiceState"));
	}
	return Z_Registration_Info_UEnum_EAuracronVoiceState.OuterSingleton;
}
template<> AURACRONVOICEBRIDGE_API UEnum* StaticEnum<EAuracronVoiceState>()
{
	return EAuracronVoiceState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estado de voz\n */" },
#endif
		{ "Connected.DisplayName", "Connected" },
		{ "Connected.Name", "EAuracronVoiceState::Connected" },
		{ "Connecting.DisplayName", "Connecting" },
		{ "Connecting.Name", "EAuracronVoiceState::Connecting" },
		{ "Deafened.DisplayName", "Deafened" },
		{ "Deafened.Name", "EAuracronVoiceState::Deafened" },
		{ "Disconnected.DisplayName", "Disconnected" },
		{ "Disconnected.Name", "EAuracronVoiceState::Disconnected" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronVoiceState::Error" },
		{ "Listening.DisplayName", "Listening" },
		{ "Listening.Name", "EAuracronVoiceState::Listening" },
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
		{ "Muted.DisplayName", "Muted" },
		{ "Muted.Name", "EAuracronVoiceState::Muted" },
		{ "Speaking.DisplayName", "Speaking" },
		{ "Speaking.Name", "EAuracronVoiceState::Speaking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estado de voz" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronVoiceState::Disconnected", (int64)EAuracronVoiceState::Disconnected },
		{ "EAuracronVoiceState::Connecting", (int64)EAuracronVoiceState::Connecting },
		{ "EAuracronVoiceState::Connected", (int64)EAuracronVoiceState::Connected },
		{ "EAuracronVoiceState::Speaking", (int64)EAuracronVoiceState::Speaking },
		{ "EAuracronVoiceState::Listening", (int64)EAuracronVoiceState::Listening },
		{ "EAuracronVoiceState::Muted", (int64)EAuracronVoiceState::Muted },
		{ "EAuracronVoiceState::Deafened", (int64)EAuracronVoiceState::Deafened },
		{ "EAuracronVoiceState::Error", (int64)EAuracronVoiceState::Error },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronVoiceBridge,
	nullptr,
	"EAuracronVoiceState",
	"EAuracronVoiceState",
	Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState()
{
	if (!Z_Registration_Info_UEnum_EAuracronVoiceState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronVoiceState.InnerSingleton, Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronVoiceState.InnerSingleton;
}
// ********** End Enum EAuracronVoiceState *********************************************************

// ********** Begin ScriptStruct FAuracronVoiceConfiguration ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronVoiceConfiguration;
class UScriptStruct* FAuracronVoiceConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVoiceConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronVoiceConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronVoiceConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronVoiceBridge(), TEXT("AuracronVoiceConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVoiceConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de voz\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVoiceChat_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar voice chat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar voice chat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoiceQuality_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Qualidade de voz */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Qualidade de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputVolume_MetaData[] = {
		{ "Category", "Voice Configuration" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume de entrada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume de entrada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputVolume_MetaData[] = {
		{ "Category", "Voice Configuration" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume de sa\xc3\x83\xc2\xad""da */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume de sa\xc3\x83\xc2\xad""da" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePushToTalk_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar push-to-talk */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar push-to-talk" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVoiceActivityDetection_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de atividade de voz */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de atividade de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoiceActivityThreshold_MetaData[] = {
		{ "Category", "Voice Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Threshold de atividade de voz */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Threshold de atividade de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNoiseSuppression_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar supress\xc3\x83\xc2\xa3o de ru\xc3\x83\xc2\xad""do */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar supress\xc3\x83\xc2\xa3o de ru\xc3\x83\xc2\xad""do" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseSuppressionIntensity_MetaData[] = {
		{ "Category", "Voice Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da supress\xc3\x83\xc2\xa3o de ru\xc3\x83\xc2\xad""do */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da supress\xc3\x83\xc2\xa3o de ru\xc3\x83\xc2\xad""do" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseEchoCancellation_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar cancelamento de eco */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar cancelamento de eco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAudioCompression_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar compress\xc3\x83\xc2\xa3o de \xc3\x83\xc2\xa1udio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar compress\xc3\x83\xc2\xa3o de \xc3\x83\xc2\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BitRate_MetaData[] = {
		{ "Category", "Voice Configuration" },
		{ "ClampMax", "320" },
		{ "ClampMin", "8" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Taxa de bits */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Taxa de bits" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUse3DVoice_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar \xc3\x83\xc2\xa1udio 3D para proximity chat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar \xc3\x83\xc2\xa1udio 3D para proximity chat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProximityDistance_MetaData[] = {
		{ "Category", "Voice Configuration" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\x83\xc2\xa2ncia m\xc3\x83\xc2\xa1xima para proximity chat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\x83\xc2\xa2ncia m\xc3\x83\xc2\xa1xima para proximity chat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDistanceAttenuation_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar atenua\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o por dist\xc3\x83\xc2\xa2ncia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar atenua\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o por dist\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVoiceFilters_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar filtros de voz */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar filtros de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVoiceModulation_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar modula\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de voz */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar modula\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoiceModulationType_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de modula\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de modula\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVoiceRecording_MetaData[] = {
		{ "Category", "Voice Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar grava\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de voz */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar grava\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRecordingDuration_MetaData[] = {
		{ "Category", "Voice Configuration" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o m\xc3\x83\xc2\xa1xima de grava\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o m\xc3\x83\xc2\xa1xima de grava\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bUseVoiceChat_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVoiceChat;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VoiceQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VoiceQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InputVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OutputVolume;
	static void NewProp_bUsePushToTalk_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePushToTalk;
	static void NewProp_bUseVoiceActivityDetection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVoiceActivityDetection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VoiceActivityThreshold;
	static void NewProp_bUseNoiseSuppression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNoiseSuppression;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseSuppressionIntensity;
	static void NewProp_bUseEchoCancellation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseEchoCancellation;
	static void NewProp_bUseAudioCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAudioCompression;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BitRate;
	static void NewProp_bUse3DVoice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUse3DVoice;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProximityDistance;
	static void NewProp_bUseDistanceAttenuation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDistanceAttenuation;
	static void NewProp_bUseVoiceFilters_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVoiceFilters;
	static void NewProp_bUseVoiceModulation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVoiceModulation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VoiceModulationType;
	static void NewProp_bUseVoiceRecording_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVoiceRecording;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRecordingDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronVoiceConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceChat_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUseVoiceChat = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceChat = { "bUseVoiceChat", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceChat_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVoiceChat_MetaData), NewProp_bUseVoiceChat_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_VoiceQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_VoiceQuality = { "VoiceQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceConfiguration, VoiceQuality), Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoiceQuality_MetaData), NewProp_VoiceQuality_MetaData) }; // 3140920330
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_InputVolume = { "InputVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceConfiguration, InputVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputVolume_MetaData), NewProp_InputVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_OutputVolume = { "OutputVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceConfiguration, OutputVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputVolume_MetaData), NewProp_OutputVolume_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUsePushToTalk_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUsePushToTalk = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUsePushToTalk = { "bUsePushToTalk", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUsePushToTalk_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePushToTalk_MetaData), NewProp_bUsePushToTalk_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceActivityDetection_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUseVoiceActivityDetection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceActivityDetection = { "bUseVoiceActivityDetection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceActivityDetection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVoiceActivityDetection_MetaData), NewProp_bUseVoiceActivityDetection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_VoiceActivityThreshold = { "VoiceActivityThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceConfiguration, VoiceActivityThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoiceActivityThreshold_MetaData), NewProp_VoiceActivityThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseNoiseSuppression_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUseNoiseSuppression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseNoiseSuppression = { "bUseNoiseSuppression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseNoiseSuppression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNoiseSuppression_MetaData), NewProp_bUseNoiseSuppression_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_NoiseSuppressionIntensity = { "NoiseSuppressionIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceConfiguration, NoiseSuppressionIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseSuppressionIntensity_MetaData), NewProp_NoiseSuppressionIntensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseEchoCancellation_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUseEchoCancellation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseEchoCancellation = { "bUseEchoCancellation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseEchoCancellation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseEchoCancellation_MetaData), NewProp_bUseEchoCancellation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseAudioCompression_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUseAudioCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseAudioCompression = { "bUseAudioCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseAudioCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAudioCompression_MetaData), NewProp_bUseAudioCompression_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_BitRate = { "BitRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceConfiguration, BitRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BitRate_MetaData), NewProp_BitRate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUse3DVoice_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUse3DVoice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUse3DVoice = { "bUse3DVoice", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUse3DVoice_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUse3DVoice_MetaData), NewProp_bUse3DVoice_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_ProximityDistance = { "ProximityDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceConfiguration, ProximityDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProximityDistance_MetaData), NewProp_ProximityDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseDistanceAttenuation_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUseDistanceAttenuation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseDistanceAttenuation = { "bUseDistanceAttenuation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseDistanceAttenuation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDistanceAttenuation_MetaData), NewProp_bUseDistanceAttenuation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceFilters_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUseVoiceFilters = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceFilters = { "bUseVoiceFilters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceFilters_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVoiceFilters_MetaData), NewProp_bUseVoiceFilters_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceModulation_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUseVoiceModulation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceModulation = { "bUseVoiceModulation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceModulation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVoiceModulation_MetaData), NewProp_bUseVoiceModulation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_VoiceModulationType = { "VoiceModulationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceConfiguration, VoiceModulationType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoiceModulationType_MetaData), NewProp_VoiceModulationType_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceRecording_SetBit(void* Obj)
{
	((FAuracronVoiceConfiguration*)Obj)->bUseVoiceRecording = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceRecording = { "bUseVoiceRecording", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceConfiguration), &Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceRecording_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVoiceRecording_MetaData), NewProp_bUseVoiceRecording_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_MaxRecordingDuration = { "MaxRecordingDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceConfiguration, MaxRecordingDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRecordingDuration_MetaData), NewProp_MaxRecordingDuration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceChat,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_VoiceQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_VoiceQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_InputVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_OutputVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUsePushToTalk,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceActivityDetection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_VoiceActivityThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseNoiseSuppression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_NoiseSuppressionIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseEchoCancellation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseAudioCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_BitRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUse3DVoice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_ProximityDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseDistanceAttenuation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceFilters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceModulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_VoiceModulationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_bUseVoiceRecording,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewProp_MaxRecordingDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVoiceBridge,
	nullptr,
	&NewStructOps,
	"AuracronVoiceConfiguration",
	Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::PropPointers),
	sizeof(FAuracronVoiceConfiguration),
	alignof(FAuracronVoiceConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronVoiceConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVoiceConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronVoiceConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVoiceConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronVoiceConfiguration *****************************************

// ********** Begin ScriptStruct FAuracronVoiceChannel *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronVoiceChannel;
class UScriptStruct* FAuracronVoiceChannel::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVoiceChannel.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronVoiceChannel.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronVoiceChannel, (UObject*)Z_Construct_UPackage__Script_AuracronVoiceBridge(), TEXT("AuracronVoiceChannel"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVoiceChannel.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para canal de voz\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para canal de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelID_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do canal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelName_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do canal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelType_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do canal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayersInChannel_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Jogadores no canal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jogadores no canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Canal est\xc3\x83\xc2\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Canal est\xc3\x83\xc2\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPrivate_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Canal \xc3\x83\xc2\xa9 privado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Canal \xc3\x83\xc2\xa9 privado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresPermission_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Requer permiss\xc3\x83\xc2\xa3o para entrar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requer permiss\xc3\x83\xc2\xa3o para entrar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelVolume_MetaData[] = {
		{ "Category", "Voice Channel" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume do canal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume do canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsMuted_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Canal est\xc3\x83\xc2\xa1 mutado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Canal est\xc3\x83\xc2\xa1 mutado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseChannelFilters_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar filtros de canal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar filtros de canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AppliedFilters_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Filtros aplicados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Filtros aplicados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de cria\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de cria\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastActivity_MetaData[] = {
		{ "Category", "Voice Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc5\xa1ltima atividade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc5\xa1ltima atividade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ChannelType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ChannelType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayersInChannel_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PlayersInChannel;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bIsPrivate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPrivate;
	static void NewProp_bRequiresPermission_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresPermission;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ChannelVolume;
	static void NewProp_bIsMuted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMuted;
	static void NewProp_bUseChannelFilters_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseChannelFilters;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AppliedFilters_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AppliedFilters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastActivity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronVoiceChannel>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_ChannelID = { "ChannelID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceChannel, ChannelID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelID_MetaData), NewProp_ChannelID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_ChannelName = { "ChannelName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceChannel, ChannelName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelName_MetaData), NewProp_ChannelName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_ChannelType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_ChannelType = { "ChannelType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceChannel, ChannelType), Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceChannelType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelType_MetaData), NewProp_ChannelType_MetaData) }; // 1910304459
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_PlayersInChannel_Inner = { "PlayersInChannel", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_PlayersInChannel = { "PlayersInChannel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceChannel, PlayersInChannel), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayersInChannel_MetaData), NewProp_PlayersInChannel_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronVoiceChannel*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceChannel), &Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsPrivate_SetBit(void* Obj)
{
	((FAuracronVoiceChannel*)Obj)->bIsPrivate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsPrivate = { "bIsPrivate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceChannel), &Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsPrivate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPrivate_MetaData), NewProp_bIsPrivate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bRequiresPermission_SetBit(void* Obj)
{
	((FAuracronVoiceChannel*)Obj)->bRequiresPermission = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bRequiresPermission = { "bRequiresPermission", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceChannel), &Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bRequiresPermission_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresPermission_MetaData), NewProp_bRequiresPermission_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_ChannelVolume = { "ChannelVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceChannel, ChannelVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelVolume_MetaData), NewProp_ChannelVolume_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsMuted_SetBit(void* Obj)
{
	((FAuracronVoiceChannel*)Obj)->bIsMuted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsMuted = { "bIsMuted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceChannel), &Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsMuted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsMuted_MetaData), NewProp_bIsMuted_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bUseChannelFilters_SetBit(void* Obj)
{
	((FAuracronVoiceChannel*)Obj)->bUseChannelFilters = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bUseChannelFilters = { "bUseChannelFilters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceChannel), &Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bUseChannelFilters_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseChannelFilters_MetaData), NewProp_bUseChannelFilters_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_AppliedFilters_Inner = { "AppliedFilters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_AppliedFilters = { "AppliedFilters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceChannel, AppliedFilters), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AppliedFilters_MetaData), NewProp_AppliedFilters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceChannel, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_LastActivity = { "LastActivity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceChannel, LastActivity), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastActivity_MetaData), NewProp_LastActivity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_ChannelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_ChannelName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_ChannelType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_ChannelType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_PlayersInChannel_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_PlayersInChannel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsPrivate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bRequiresPermission,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_ChannelVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bIsMuted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_bUseChannelFilters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_AppliedFilters_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_AppliedFilters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewProp_LastActivity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVoiceBridge,
	nullptr,
	&NewStructOps,
	"AuracronVoiceChannel",
	Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::PropPointers),
	sizeof(FAuracronVoiceChannel),
	alignof(FAuracronVoiceChannel),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronVoiceChannel()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVoiceChannel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronVoiceChannel.InnerSingleton, Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVoiceChannel.InnerSingleton;
}
// ********** End ScriptStruct FAuracronVoiceChannel ***********************************************

// ********** Begin ScriptStruct FAuracronVoiceParticipant *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronVoiceParticipant;
class UScriptStruct* FAuracronVoiceParticipant::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVoiceParticipant.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronVoiceParticipant.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronVoiceParticipant, (UObject*)Z_Construct_UPackage__Script_AuracronVoiceBridge(), TEXT("AuracronVoiceParticipant"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVoiceParticipant.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para participante de voz\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para participante de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerName_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoiceState_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado de voz atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado de voz atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSpeaking_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Est\xc3\x83\xc2\xa1 falando */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Est\xc3\x83\xc2\xa1 falando" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsMuted_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Est\xc3\x83\xc2\xa1 mutado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Est\xc3\x83\xc2\xa1 mutado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDeafened_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Est\xc3\x83\xc2\xa1 surdo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Est\xc3\x83\xc2\xa1 surdo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParticipantVolume_MetaData[] = {
		{ "Category", "Voice Participant" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume do participante */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume do participante" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionQuality_MetaData[] = {
		{ "Category", "Voice Participant" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Qualidade da conex\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Qualidade da conex\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoiceLatency_MetaData[] = {
		{ "Category", "Voice Participant" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lat\xc3\x83\xc2\xaancia de voz */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lat\xc3\x83\xc2\xaancia de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position3D_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o 3D para proximity chat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o 3D para proximity chat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUse3DPosition_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o 3D */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveChannels_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Canais ativos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Canais ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoicePermissions_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permiss\xc3\x83\xc2\xb5""es de voz */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permiss\xc3\x83\xc2\xb5""es de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectedTime_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo conectado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo conectado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastSpokeTime_MetaData[] = {
		{ "Category", "Voice Participant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc5\xa1ltima vez que falou */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc5\xa1ltima vez que falou" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VoiceState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VoiceState;
	static void NewProp_bIsSpeaking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSpeaking;
	static void NewProp_bIsMuted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMuted;
	static void NewProp_bIsDeafened_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDeafened;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ParticipantVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConnectionQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VoiceLatency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position3D;
	static void NewProp_bUse3DPosition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUse3DPosition;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveChannels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveChannels;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VoicePermissions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VoicePermissions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectedTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastSpokeTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronVoiceParticipant>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_PlayerName = { "PlayerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, PlayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerName_MetaData), NewProp_PlayerName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_VoiceState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_VoiceState = { "VoiceState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, VoiceState), Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoiceState_MetaData), NewProp_VoiceState_MetaData) }; // 124221615
void Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsSpeaking_SetBit(void* Obj)
{
	((FAuracronVoiceParticipant*)Obj)->bIsSpeaking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsSpeaking = { "bIsSpeaking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceParticipant), &Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsSpeaking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSpeaking_MetaData), NewProp_bIsSpeaking_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsMuted_SetBit(void* Obj)
{
	((FAuracronVoiceParticipant*)Obj)->bIsMuted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsMuted = { "bIsMuted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceParticipant), &Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsMuted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsMuted_MetaData), NewProp_bIsMuted_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsDeafened_SetBit(void* Obj)
{
	((FAuracronVoiceParticipant*)Obj)->bIsDeafened = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsDeafened = { "bIsDeafened", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceParticipant), &Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsDeafened_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDeafened_MetaData), NewProp_bIsDeafened_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_ParticipantVolume = { "ParticipantVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, ParticipantVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParticipantVolume_MetaData), NewProp_ParticipantVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_ConnectionQuality = { "ConnectionQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, ConnectionQuality), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionQuality_MetaData), NewProp_ConnectionQuality_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_VoiceLatency = { "VoiceLatency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, VoiceLatency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoiceLatency_MetaData), NewProp_VoiceLatency_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_Position3D = { "Position3D", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, Position3D), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position3D_MetaData), NewProp_Position3D_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bUse3DPosition_SetBit(void* Obj)
{
	((FAuracronVoiceParticipant*)Obj)->bUse3DPosition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bUse3DPosition = { "bUse3DPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVoiceParticipant), &Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bUse3DPosition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUse3DPosition_MetaData), NewProp_bUse3DPosition_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_ActiveChannels_Inner = { "ActiveChannels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_ActiveChannels = { "ActiveChannels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, ActiveChannels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveChannels_MetaData), NewProp_ActiveChannels_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_VoicePermissions_Inner = { "VoicePermissions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_VoicePermissions = { "VoicePermissions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, VoicePermissions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoicePermissions_MetaData), NewProp_VoicePermissions_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_ConnectedTime = { "ConnectedTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, ConnectedTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectedTime_MetaData), NewProp_ConnectedTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_LastSpokeTime = { "LastSpokeTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVoiceParticipant, LastSpokeTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastSpokeTime_MetaData), NewProp_LastSpokeTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_PlayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_VoiceState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_VoiceState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsSpeaking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsMuted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bIsDeafened,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_ParticipantVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_ConnectionQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_VoiceLatency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_Position3D,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_bUse3DPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_ActiveChannels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_ActiveChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_VoicePermissions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_VoicePermissions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_ConnectedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewProp_LastSpokeTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVoiceBridge,
	nullptr,
	&NewStructOps,
	"AuracronVoiceParticipant",
	Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::PropPointers),
	sizeof(FAuracronVoiceParticipant),
	alignof(FAuracronVoiceParticipant),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronVoiceParticipant()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVoiceParticipant.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronVoiceParticipant.InnerSingleton, Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVoiceParticipant.InnerSingleton;
}
// ********** End ScriptStruct FAuracronVoiceParticipant *******************************************

// ********** Begin Delegate FOnParticipantJoined **************************************************
struct Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics
{
	struct AuracronVoiceBridge_eventOnParticipantJoined_Parms
	{
		FString ChannelID;
		FAuracronVoiceParticipant Participant;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando participante entra no canal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando participante entra no canal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Participant;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::NewProp_ChannelID = { "ChannelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventOnParticipantJoined_Parms, ChannelID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::NewProp_Participant = { "Participant", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventOnParticipantJoined_Parms, Participant), Z_Construct_UScriptStruct_FAuracronVoiceParticipant, METADATA_PARAMS(0, nullptr) }; // 4234025503
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::NewProp_ChannelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::NewProp_Participant,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "OnParticipantJoined__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::AuracronVoiceBridge_eventOnParticipantJoined_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::AuracronVoiceBridge_eventOnParticipantJoined_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronVoiceBridge::FOnParticipantJoined_DelegateWrapper(const FMulticastScriptDelegate& OnParticipantJoined, const FString& ChannelID, FAuracronVoiceParticipant Participant)
{
	struct AuracronVoiceBridge_eventOnParticipantJoined_Parms
	{
		FString ChannelID;
		FAuracronVoiceParticipant Participant;
	};
	AuracronVoiceBridge_eventOnParticipantJoined_Parms Parms;
	Parms.ChannelID=ChannelID;
	Parms.Participant=Participant;
	OnParticipantJoined.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnParticipantJoined ****************************************************

// ********** Begin Delegate FOnParticipantLeft ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics
{
	struct AuracronVoiceBridge_eventOnParticipantLeft_Parms
	{
		FString ChannelID;
		FString PlayerID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando participante sai do canal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando participante sai do canal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::NewProp_ChannelID = { "ChannelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventOnParticipantLeft_Parms, ChannelID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventOnParticipantLeft_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::NewProp_ChannelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::NewProp_PlayerID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "OnParticipantLeft__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::AuracronVoiceBridge_eventOnParticipantLeft_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::AuracronVoiceBridge_eventOnParticipantLeft_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronVoiceBridge::FOnParticipantLeft_DelegateWrapper(const FMulticastScriptDelegate& OnParticipantLeft, const FString& ChannelID, const FString& PlayerID)
{
	struct AuracronVoiceBridge_eventOnParticipantLeft_Parms
	{
		FString ChannelID;
		FString PlayerID;
	};
	AuracronVoiceBridge_eventOnParticipantLeft_Parms Parms;
	Parms.ChannelID=ChannelID;
	Parms.PlayerID=PlayerID;
	OnParticipantLeft.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnParticipantLeft ******************************************************

// ********** Begin Delegate FOnParticipantStartedSpeaking *****************************************
struct Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics
{
	struct AuracronVoiceBridge_eventOnParticipantStartedSpeaking_Parms
	{
		FString PlayerID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando participante come\xc3\x83\xc2\xa7""a a falar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando participante come\xc3\x83\xc2\xa7""a a falar" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventOnParticipantStartedSpeaking_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::NewProp_PlayerID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "OnParticipantStartedSpeaking__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::AuracronVoiceBridge_eventOnParticipantStartedSpeaking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::AuracronVoiceBridge_eventOnParticipantStartedSpeaking_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronVoiceBridge::FOnParticipantStartedSpeaking_DelegateWrapper(const FMulticastScriptDelegate& OnParticipantStartedSpeaking, const FString& PlayerID)
{
	struct AuracronVoiceBridge_eventOnParticipantStartedSpeaking_Parms
	{
		FString PlayerID;
	};
	AuracronVoiceBridge_eventOnParticipantStartedSpeaking_Parms Parms;
	Parms.PlayerID=PlayerID;
	OnParticipantStartedSpeaking.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnParticipantStartedSpeaking *******************************************

// ********** Begin Delegate FOnParticipantStoppedSpeaking *****************************************
struct Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics
{
	struct AuracronVoiceBridge_eventOnParticipantStoppedSpeaking_Parms
	{
		FString PlayerID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando participante para de falar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando participante para de falar" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventOnParticipantStoppedSpeaking_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::NewProp_PlayerID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "OnParticipantStoppedSpeaking__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::AuracronVoiceBridge_eventOnParticipantStoppedSpeaking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::AuracronVoiceBridge_eventOnParticipantStoppedSpeaking_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronVoiceBridge::FOnParticipantStoppedSpeaking_DelegateWrapper(const FMulticastScriptDelegate& OnParticipantStoppedSpeaking, const FString& PlayerID)
{
	struct AuracronVoiceBridge_eventOnParticipantStoppedSpeaking_Parms
	{
		FString PlayerID;
	};
	AuracronVoiceBridge_eventOnParticipantStoppedSpeaking_Parms Parms;
	Parms.PlayerID=PlayerID;
	OnParticipantStoppedSpeaking.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnParticipantStoppedSpeaking *******************************************

// ********** Begin Class UAuracronVoiceBridge Function ApplyVoiceFilter ***************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics
{
	struct AuracronVoiceBridge_eventApplyVoiceFilter_Parms
	{
		FString PlayerID;
		FString FilterType;
		float Intensity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar filtro de voz\n     */" },
#endif
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar filtro de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilterType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventApplyVoiceFilter_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::NewProp_FilterType = { "FilterType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventApplyVoiceFilter_Parms, FilterType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterType_MetaData), NewProp_FilterType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventApplyVoiceFilter_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventApplyVoiceFilter_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventApplyVoiceFilter_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::NewProp_FilterType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "ApplyVoiceFilter", Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::AuracronVoiceBridge_eventApplyVoiceFilter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::AuracronVoiceBridge_eventApplyVoiceFilter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execApplyVoiceFilter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_FilterType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyVoiceFilter(Z_Param_PlayerID,Z_Param_FilterType,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function ApplyVoiceFilter *****************************

// ********** Begin Class UAuracronVoiceBridge Function ApplyVoiceModulation ***********************
struct Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics
{
	struct AuracronVoiceBridge_eventApplyVoiceModulation_Parms
	{
		FString PlayerID;
		FString ModulationType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar modula\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de voz\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar modula\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModulationType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModulationType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventApplyVoiceModulation_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::NewProp_ModulationType = { "ModulationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventApplyVoiceModulation_Parms, ModulationType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModulationType_MetaData), NewProp_ModulationType_MetaData) };
void Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventApplyVoiceModulation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventApplyVoiceModulation_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::NewProp_ModulationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "ApplyVoiceModulation", Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::AuracronVoiceBridge_eventApplyVoiceModulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::AuracronVoiceBridge_eventApplyVoiceModulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execApplyVoiceModulation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_ModulationType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyVoiceModulation(Z_Param_PlayerID,Z_Param_ModulationType);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function ApplyVoiceModulation *************************

// ********** Begin Class UAuracronVoiceBridge Function ConnectToVoiceChat *************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics
{
	struct AuracronVoiceBridge_eventConnectToVoiceChat_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Conectar ao voice chat\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conectar ao voice chat" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventConnectToVoiceChat_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventConnectToVoiceChat_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "ConnectToVoiceChat", Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::AuracronVoiceBridge_eventConnectToVoiceChat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::AuracronVoiceBridge_eventConnectToVoiceChat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execConnectToVoiceChat)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConnectToVoiceChat();
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function ConnectToVoiceChat ***************************

// ********** Begin Class UAuracronVoiceBridge Function CreateVoiceChannel *************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics
{
	struct AuracronVoiceBridge_eventCreateVoiceChannel_Parms
	{
		FAuracronVoiceChannel ChannelConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Channels" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar canal de voz\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar canal de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChannelConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::NewProp_ChannelConfig = { "ChannelConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventCreateVoiceChannel_Parms, ChannelConfig), Z_Construct_UScriptStruct_FAuracronVoiceChannel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelConfig_MetaData), NewProp_ChannelConfig_MetaData) }; // 2004951449
void Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventCreateVoiceChannel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventCreateVoiceChannel_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::NewProp_ChannelConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "CreateVoiceChannel", Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::AuracronVoiceBridge_eventCreateVoiceChannel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::AuracronVoiceBridge_eventCreateVoiceChannel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execCreateVoiceChannel)
{
	P_GET_STRUCT_REF(FAuracronVoiceChannel,Z_Param_Out_ChannelConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateVoiceChannel(Z_Param_Out_ChannelConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function CreateVoiceChannel ***************************

// ********** Begin Class UAuracronVoiceBridge Function DeafenAudio ********************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics
{
	struct AuracronVoiceBridge_eventDeafenAudio_Parms
	{
		bool bDeafen;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ensurdecer \xc3\x83\xc2\xa1udio\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ensurdecer \xc3\x83\xc2\xa1udio" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bDeafen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDeafen;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::NewProp_bDeafen_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventDeafenAudio_Parms*)Obj)->bDeafen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::NewProp_bDeafen = { "bDeafen", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventDeafenAudio_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::NewProp_bDeafen_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventDeafenAudio_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventDeafenAudio_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::NewProp_bDeafen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "DeafenAudio", Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::AuracronVoiceBridge_eventDeafenAudio_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::AuracronVoiceBridge_eventDeafenAudio_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execDeafenAudio)
{
	P_GET_UBOOL(Z_Param_bDeafen);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DeafenAudio(Z_Param_bDeafen);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function DeafenAudio **********************************

// ********** Begin Class UAuracronVoiceBridge Function DisconnectFromVoiceChat ********************
struct Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics
{
	struct AuracronVoiceBridge_eventDisconnectFromVoiceChat_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Desconectar do voice chat\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desconectar do voice chat" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventDisconnectFromVoiceChat_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventDisconnectFromVoiceChat_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "DisconnectFromVoiceChat", Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::AuracronVoiceBridge_eventDisconnectFromVoiceChat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::AuracronVoiceBridge_eventDisconnectFromVoiceChat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execDisconnectFromVoiceChat)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DisconnectFromVoiceChat();
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function DisconnectFromVoiceChat **********************

// ********** Begin Class UAuracronVoiceBridge Function Enable3DVoice ******************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics
{
	struct AuracronVoiceBridge_eventEnable3DVoice_Parms
	{
		bool bEnable;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|3D" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Habilitar voz 3D\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilitar voz 3D" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventEnable3DVoice_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventEnable3DVoice_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventEnable3DVoice_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventEnable3DVoice_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::NewProp_bEnable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "Enable3DVoice", Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::AuracronVoiceBridge_eventEnable3DVoice_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::AuracronVoiceBridge_eventEnable3DVoice_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execEnable3DVoice)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Enable3DVoice(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function Enable3DVoice ********************************

// ********** Begin Class UAuracronVoiceBridge Function GetChannelParticipants *********************
struct Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics
{
	struct AuracronVoiceBridge_eventGetChannelParticipants_Parms
	{
		FString ChannelID;
		TArray<FAuracronVoiceParticipant> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Participants" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter participantes do canal\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter participantes do canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::NewProp_ChannelID = { "ChannelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventGetChannelParticipants_Parms, ChannelID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelID_MetaData), NewProp_ChannelID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronVoiceParticipant, METADATA_PARAMS(0, nullptr) }; // 4234025503
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventGetChannelParticipants_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4234025503
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::NewProp_ChannelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "GetChannelParticipants", Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::AuracronVoiceBridge_eventGetChannelParticipants_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::AuracronVoiceBridge_eventGetChannelParticipants_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execGetChannelParticipants)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChannelID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronVoiceParticipant>*)Z_Param__Result=P_THIS->GetChannelParticipants(Z_Param_ChannelID);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function GetChannelParticipants ***********************

// ********** Begin Class UAuracronVoiceBridge Function InitializeVoiceChat ************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics
{
	struct AuracronVoiceBridge_eventInitializeVoiceChat_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Inicializar voice chat\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar voice chat" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventInitializeVoiceChat_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventInitializeVoiceChat_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "InitializeVoiceChat", Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::AuracronVoiceBridge_eventInitializeVoiceChat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::AuracronVoiceBridge_eventInitializeVoiceChat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execInitializeVoiceChat)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeVoiceChat();
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function InitializeVoiceChat **************************

// ********** Begin Class UAuracronVoiceBridge Function IsParticipantSpeaking **********************
struct Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics
{
	struct AuracronVoiceBridge_eventIsParticipantSpeaking_Parms
	{
		FString PlayerID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Participants" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se participante est\xc3\x83\xc2\xa1 falando\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se participante est\xc3\x83\xc2\xa1 falando" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventIsParticipantSpeaking_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
void Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventIsParticipantSpeaking_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventIsParticipantSpeaking_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "IsParticipantSpeaking", Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::AuracronVoiceBridge_eventIsParticipantSpeaking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::AuracronVoiceBridge_eventIsParticipantSpeaking_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execIsParticipantSpeaking)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsParticipantSpeaking(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function IsParticipantSpeaking ************************

// ********** Begin Class UAuracronVoiceBridge Function JoinVoiceChannel ***************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics
{
	struct AuracronVoiceBridge_eventJoinVoiceChannel_Parms
	{
		FString ChannelID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Channels" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Entrar em canal\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entrar em canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::NewProp_ChannelID = { "ChannelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventJoinVoiceChannel_Parms, ChannelID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelID_MetaData), NewProp_ChannelID_MetaData) };
void Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventJoinVoiceChannel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventJoinVoiceChannel_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::NewProp_ChannelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "JoinVoiceChannel", Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::AuracronVoiceBridge_eventJoinVoiceChannel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::AuracronVoiceBridge_eventJoinVoiceChannel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execJoinVoiceChannel)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChannelID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->JoinVoiceChannel(Z_Param_ChannelID);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function JoinVoiceChannel *****************************

// ********** Begin Class UAuracronVoiceBridge Function LeaveVoiceChannel **************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics
{
	struct AuracronVoiceBridge_eventLeaveVoiceChannel_Parms
	{
		FString ChannelID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Channels" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Sair de canal\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sair de canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::NewProp_ChannelID = { "ChannelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventLeaveVoiceChannel_Parms, ChannelID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelID_MetaData), NewProp_ChannelID_MetaData) };
void Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventLeaveVoiceChannel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventLeaveVoiceChannel_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::NewProp_ChannelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "LeaveVoiceChannel", Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::AuracronVoiceBridge_eventLeaveVoiceChannel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::AuracronVoiceBridge_eventLeaveVoiceChannel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execLeaveVoiceChannel)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChannelID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LeaveVoiceChannel(Z_Param_ChannelID);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function LeaveVoiceChannel ****************************

// ********** Begin Class UAuracronVoiceBridge Function MuteMicrophone *****************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics
{
	struct AuracronVoiceBridge_eventMuteMicrophone_Parms
	{
		bool bMute;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Mutar microfone\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mutar microfone" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bMute_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMute;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::NewProp_bMute_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventMuteMicrophone_Parms*)Obj)->bMute = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::NewProp_bMute = { "bMute", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventMuteMicrophone_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::NewProp_bMute_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventMuteMicrophone_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventMuteMicrophone_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::NewProp_bMute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "MuteMicrophone", Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::AuracronVoiceBridge_eventMuteMicrophone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::AuracronVoiceBridge_eventMuteMicrophone_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execMuteMicrophone)
{
	P_GET_UBOOL(Z_Param_bMute);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MuteMicrophone(Z_Param_bMute);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function MuteMicrophone *******************************

// ********** Begin Class UAuracronVoiceBridge Function MuteParticipant ****************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics
{
	struct AuracronVoiceBridge_eventMuteParticipant_Parms
	{
		FString PlayerID;
		bool bMute;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Participants" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Mutar participante\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mutar participante" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static void NewProp_bMute_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMute;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventMuteParticipant_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
void Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::NewProp_bMute_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventMuteParticipant_Parms*)Obj)->bMute = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::NewProp_bMute = { "bMute", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventMuteParticipant_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::NewProp_bMute_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventMuteParticipant_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventMuteParticipant_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::NewProp_bMute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "MuteParticipant", Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::AuracronVoiceBridge_eventMuteParticipant_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::AuracronVoiceBridge_eventMuteParticipant_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execMuteParticipant)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_UBOOL(Z_Param_bMute);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MuteParticipant(Z_Param_PlayerID,Z_Param_bMute);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function MuteParticipant ******************************

// ********** Begin Class UAuracronVoiceBridge Function MuteVoiceChannel ***************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics
{
	struct AuracronVoiceBridge_eventMuteVoiceChannel_Parms
	{
		FString ChannelID;
		bool bMute;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Channels" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Mutar canal\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mutar canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelID;
	static void NewProp_bMute_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMute;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::NewProp_ChannelID = { "ChannelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventMuteVoiceChannel_Parms, ChannelID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelID_MetaData), NewProp_ChannelID_MetaData) };
void Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::NewProp_bMute_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventMuteVoiceChannel_Parms*)Obj)->bMute = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::NewProp_bMute = { "bMute", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventMuteVoiceChannel_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::NewProp_bMute_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventMuteVoiceChannel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventMuteVoiceChannel_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::NewProp_ChannelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::NewProp_bMute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "MuteVoiceChannel", Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::AuracronVoiceBridge_eventMuteVoiceChannel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::AuracronVoiceBridge_eventMuteVoiceChannel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execMuteVoiceChannel)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChannelID);
	P_GET_UBOOL(Z_Param_bMute);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MuteVoiceChannel(Z_Param_ChannelID,Z_Param_bMute);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function MuteVoiceChannel *****************************

// ********** Begin Class UAuracronVoiceBridge Function OnRep_VoiceState ***************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_OnRep_VoiceState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_OnRep_VoiceState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "OnRep_VoiceState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_OnRep_VoiceState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_OnRep_VoiceState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_OnRep_VoiceState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_OnRep_VoiceState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execOnRep_VoiceState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_VoiceState();
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function OnRep_VoiceState *****************************

// ********** Begin Class UAuracronVoiceBridge Function PlayVoiceRecording *************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics
{
	struct AuracronVoiceBridge_eventPlayVoiceRecording_Parms
	{
		FString RecordingID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Recording" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir grava\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir grava\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecordingID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecordingID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::NewProp_RecordingID = { "RecordingID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventPlayVoiceRecording_Parms, RecordingID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecordingID_MetaData), NewProp_RecordingID_MetaData) };
void Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventPlayVoiceRecording_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventPlayVoiceRecording_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::NewProp_RecordingID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "PlayVoiceRecording", Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::AuracronVoiceBridge_eventPlayVoiceRecording_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::AuracronVoiceBridge_eventPlayVoiceRecording_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execPlayVoiceRecording)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RecordingID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayVoiceRecording(Z_Param_RecordingID);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function PlayVoiceRecording ***************************

// ********** Begin Class UAuracronVoiceBridge Function RemoveVoiceFilter **************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics
{
	struct AuracronVoiceBridge_eventRemoveVoiceFilter_Parms
	{
		FString PlayerID;
		FString FilterType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remover filtro de voz\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover filtro de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilterType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventRemoveVoiceFilter_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::NewProp_FilterType = { "FilterType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventRemoveVoiceFilter_Parms, FilterType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterType_MetaData), NewProp_FilterType_MetaData) };
void Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventRemoveVoiceFilter_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventRemoveVoiceFilter_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::NewProp_FilterType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "RemoveVoiceFilter", Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::AuracronVoiceBridge_eventRemoveVoiceFilter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::AuracronVoiceBridge_eventRemoveVoiceFilter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execRemoveVoiceFilter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_FilterType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveVoiceFilter(Z_Param_PlayerID,Z_Param_FilterType);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function RemoveVoiceFilter ****************************

// ********** Begin Class UAuracronVoiceBridge Function SetChannelVolume ***************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics
{
	struct AuracronVoiceBridge_eventSetChannelVolume_Parms
	{
		FString ChannelID;
		float Volume;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Channels" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir volume do canal\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir volume do canal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Volume;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::NewProp_ChannelID = { "ChannelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventSetChannelVolume_Parms, ChannelID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelID_MetaData), NewProp_ChannelID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::NewProp_Volume = { "Volume", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventSetChannelVolume_Parms, Volume), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventSetChannelVolume_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventSetChannelVolume_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::NewProp_ChannelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::NewProp_Volume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "SetChannelVolume", Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::AuracronVoiceBridge_eventSetChannelVolume_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::AuracronVoiceBridge_eventSetChannelVolume_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execSetChannelVolume)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChannelID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Volume);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetChannelVolume(Z_Param_ChannelID,Z_Param_Volume);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function SetChannelVolume *****************************

// ********** Begin Class UAuracronVoiceBridge Function SetParticipantVolume ***********************
struct Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics
{
	struct AuracronVoiceBridge_eventSetParticipantVolume_Parms
	{
		FString PlayerID;
		float Volume;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Participants" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir volume do participante\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir volume do participante" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Volume;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventSetParticipantVolume_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::NewProp_Volume = { "Volume", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventSetParticipantVolume_Parms, Volume), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventSetParticipantVolume_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventSetParticipantVolume_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::NewProp_Volume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "SetParticipantVolume", Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::AuracronVoiceBridge_eventSetParticipantVolume_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::AuracronVoiceBridge_eventSetParticipantVolume_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execSetParticipantVolume)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Volume);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetParticipantVolume(Z_Param_PlayerID,Z_Param_Volume);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function SetParticipantVolume *************************

// ********** Begin Class UAuracronVoiceBridge Function SetProximityDistance ***********************
struct Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics
{
	struct AuracronVoiceBridge_eventSetProximityDistance_Parms
	{
		float Distance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|3D" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir dist\xc3\x83\xc2\xa2ncia de proximity chat\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir dist\xc3\x83\xc2\xa2ncia de proximity chat" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventSetProximityDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventSetProximityDistance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventSetProximityDistance_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "SetProximityDistance", Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::AuracronVoiceBridge_eventSetProximityDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::AuracronVoiceBridge_eventSetProximityDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execSetProximityDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetProximityDistance(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function SetProximityDistance *************************

// ********** Begin Class UAuracronVoiceBridge Function StartVoiceRecording ************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics
{
	struct AuracronVoiceBridge_eventStartVoiceRecording_Parms
	{
		FString RecordingID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Recording" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Iniciar grava\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar grava\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecordingID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecordingID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::NewProp_RecordingID = { "RecordingID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventStartVoiceRecording_Parms, RecordingID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecordingID_MetaData), NewProp_RecordingID_MetaData) };
void Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventStartVoiceRecording_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventStartVoiceRecording_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::NewProp_RecordingID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "StartVoiceRecording", Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::AuracronVoiceBridge_eventStartVoiceRecording_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::AuracronVoiceBridge_eventStartVoiceRecording_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execStartVoiceRecording)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RecordingID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartVoiceRecording(Z_Param_RecordingID);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function StartVoiceRecording **************************

// ********** Begin Class UAuracronVoiceBridge Function StopVoiceRecording *************************
struct Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics
{
	struct AuracronVoiceBridge_eventStopVoiceRecording_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|Recording" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Parar grava\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar grava\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventStopVoiceRecording_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventStopVoiceRecording_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "StopVoiceRecording", Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::AuracronVoiceBridge_eventStopVoiceRecording_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::AuracronVoiceBridge_eventStopVoiceRecording_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execStopVoiceRecording)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopVoiceRecording();
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function StopVoiceRecording ***************************

// ********** Begin Class UAuracronVoiceBridge Function UpdatePlayer3DPosition *********************
struct Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics
{
	struct AuracronVoiceBridge_eventUpdatePlayer3DPosition_Parms
	{
		FString PlayerID;
		FVector Position;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Voice|3D" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o 3D do jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o 3D do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventUpdatePlayer3DPosition_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVoiceBridge_eventUpdatePlayer3DPosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVoiceBridge_eventUpdatePlayer3DPosition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVoiceBridge_eventUpdatePlayer3DPosition_Parms), &Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVoiceBridge, nullptr, "UpdatePlayer3DPosition", Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::AuracronVoiceBridge_eventUpdatePlayer3DPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::AuracronVoiceBridge_eventUpdatePlayer3DPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVoiceBridge::execUpdatePlayer3DPosition)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdatePlayer3DPosition(Z_Param_PlayerID,Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class UAuracronVoiceBridge Function UpdatePlayer3DPosition ***********************

// ********** Begin Class UAuracronVoiceBridge *****************************************************
void UAuracronVoiceBridge::StaticRegisterNativesUAuracronVoiceBridge()
{
	UClass* Class = UAuracronVoiceBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyVoiceFilter", &UAuracronVoiceBridge::execApplyVoiceFilter },
		{ "ApplyVoiceModulation", &UAuracronVoiceBridge::execApplyVoiceModulation },
		{ "ConnectToVoiceChat", &UAuracronVoiceBridge::execConnectToVoiceChat },
		{ "CreateVoiceChannel", &UAuracronVoiceBridge::execCreateVoiceChannel },
		{ "DeafenAudio", &UAuracronVoiceBridge::execDeafenAudio },
		{ "DisconnectFromVoiceChat", &UAuracronVoiceBridge::execDisconnectFromVoiceChat },
		{ "Enable3DVoice", &UAuracronVoiceBridge::execEnable3DVoice },
		{ "GetChannelParticipants", &UAuracronVoiceBridge::execGetChannelParticipants },
		{ "InitializeVoiceChat", &UAuracronVoiceBridge::execInitializeVoiceChat },
		{ "IsParticipantSpeaking", &UAuracronVoiceBridge::execIsParticipantSpeaking },
		{ "JoinVoiceChannel", &UAuracronVoiceBridge::execJoinVoiceChannel },
		{ "LeaveVoiceChannel", &UAuracronVoiceBridge::execLeaveVoiceChannel },
		{ "MuteMicrophone", &UAuracronVoiceBridge::execMuteMicrophone },
		{ "MuteParticipant", &UAuracronVoiceBridge::execMuteParticipant },
		{ "MuteVoiceChannel", &UAuracronVoiceBridge::execMuteVoiceChannel },
		{ "OnRep_VoiceState", &UAuracronVoiceBridge::execOnRep_VoiceState },
		{ "PlayVoiceRecording", &UAuracronVoiceBridge::execPlayVoiceRecording },
		{ "RemoveVoiceFilter", &UAuracronVoiceBridge::execRemoveVoiceFilter },
		{ "SetChannelVolume", &UAuracronVoiceBridge::execSetChannelVolume },
		{ "SetParticipantVolume", &UAuracronVoiceBridge::execSetParticipantVolume },
		{ "SetProximityDistance", &UAuracronVoiceBridge::execSetProximityDistance },
		{ "StartVoiceRecording", &UAuracronVoiceBridge::execStartVoiceRecording },
		{ "StopVoiceRecording", &UAuracronVoiceBridge::execStopVoiceRecording },
		{ "UpdatePlayer3DPosition", &UAuracronVoiceBridge::execUpdatePlayer3DPosition },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronVoiceBridge;
UClass* UAuracronVoiceBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronVoiceBridge;
	if (!Z_Registration_Info_UClass_UAuracronVoiceBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronVoiceBridge"),
			Z_Registration_Info_UClass_UAuracronVoiceBridge.InnerSingleton,
			StaticRegisterNativesUAuracronVoiceBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronVoiceBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronVoiceBridge_NoRegister()
{
	return UAuracronVoiceBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronVoiceBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Voice" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Comunica\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o por Voz\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de voice chat\n */" },
#endif
		{ "DisplayName", "AURACRON Voice Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronVoiceBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Comunica\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o por Voz\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de voice chat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoiceConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de voz */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de voz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveVoiceChannels_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Canais de voz ativos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Canais de voz ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectedParticipants_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Participantes conectados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Participantes conectados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentVoiceState_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado de voz atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado de voz atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnParticipantJoined_MetaData[] = {
		{ "Category", "AURACRON Voice|Events" },
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnParticipantLeft_MetaData[] = {
		{ "Category", "AURACRON Voice|Events" },
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnParticipantStartedSpeaking_MetaData[] = {
		{ "Category", "AURACRON Voice|Events" },
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnParticipantStoppedSpeaking_MetaData[] = {
		{ "Category", "AURACRON Voice|Events" },
		{ "ModuleRelativePath", "Public/AuracronVoiceBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VoiceConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveVoiceChannels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveVoiceChannels;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectedParticipants_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ConnectedParticipants;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentVoiceState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentVoiceState;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnParticipantJoined;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnParticipantLeft;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnParticipantStartedSpeaking;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnParticipantStoppedSpeaking;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceFilter, "ApplyVoiceFilter" }, // 1638113476
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_ApplyVoiceModulation, "ApplyVoiceModulation" }, // 3630220197
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_ConnectToVoiceChat, "ConnectToVoiceChat" }, // 2825538484
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_CreateVoiceChannel, "CreateVoiceChannel" }, // 2508787991
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_DeafenAudio, "DeafenAudio" }, // 2979842484
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_DisconnectFromVoiceChat, "DisconnectFromVoiceChat" }, // 3481821451
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_Enable3DVoice, "Enable3DVoice" }, // 3845343596
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_GetChannelParticipants, "GetChannelParticipants" }, // 2745659717
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_InitializeVoiceChat, "InitializeVoiceChat" }, // 933924297
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_IsParticipantSpeaking, "IsParticipantSpeaking" }, // 718542917
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_JoinVoiceChannel, "JoinVoiceChannel" }, // 1553165948
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_LeaveVoiceChannel, "LeaveVoiceChannel" }, // 2684323920
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_MuteMicrophone, "MuteMicrophone" }, // 523955746
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_MuteParticipant, "MuteParticipant" }, // 2080255398
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_MuteVoiceChannel, "MuteVoiceChannel" }, // 699596548
		{ &Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature, "OnParticipantJoined__DelegateSignature" }, // 922362751
		{ &Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature, "OnParticipantLeft__DelegateSignature" }, // 3483974303
		{ &Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature, "OnParticipantStartedSpeaking__DelegateSignature" }, // 2569349219
		{ &Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature, "OnParticipantStoppedSpeaking__DelegateSignature" }, // 953272026
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_OnRep_VoiceState, "OnRep_VoiceState" }, // 662552272
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_PlayVoiceRecording, "PlayVoiceRecording" }, // 68342282
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_RemoveVoiceFilter, "RemoveVoiceFilter" }, // 2933777368
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_SetChannelVolume, "SetChannelVolume" }, // 539511887
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_SetParticipantVolume, "SetParticipantVolume" }, // 2053351134
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_SetProximityDistance, "SetProximityDistance" }, // 2762407860
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_StartVoiceRecording, "StartVoiceRecording" }, // 3241544771
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_StopVoiceRecording, "StopVoiceRecording" }, // 4260745449
		{ &Z_Construct_UFunction_UAuracronVoiceBridge_UpdatePlayer3DPosition, "UpdatePlayer3DPosition" }, // 1618037957
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronVoiceBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_VoiceConfiguration = { "VoiceConfiguration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVoiceBridge, VoiceConfiguration), Z_Construct_UScriptStruct_FAuracronVoiceConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoiceConfiguration_MetaData), NewProp_VoiceConfiguration_MetaData) }; // 2376893664
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_ActiveVoiceChannels_Inner = { "ActiveVoiceChannels", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronVoiceChannel, METADATA_PARAMS(0, nullptr) }; // 2004951449
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_ActiveVoiceChannels = { "ActiveVoiceChannels", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVoiceBridge, ActiveVoiceChannels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveVoiceChannels_MetaData), NewProp_ActiveVoiceChannels_MetaData) }; // 2004951449
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_ConnectedParticipants_Inner = { "ConnectedParticipants", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronVoiceParticipant, METADATA_PARAMS(0, nullptr) }; // 4234025503
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_ConnectedParticipants = { "ConnectedParticipants", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVoiceBridge, ConnectedParticipants), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectedParticipants_MetaData), NewProp_ConnectedParticipants_MetaData) }; // 4234025503
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_CurrentVoiceState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_CurrentVoiceState = { "CurrentVoiceState", "OnRep_VoiceState", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVoiceBridge, CurrentVoiceState), Z_Construct_UEnum_AuracronVoiceBridge_EAuracronVoiceState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentVoiceState_MetaData), NewProp_CurrentVoiceState_MetaData) }; // 124221615
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_OnParticipantJoined = { "OnParticipantJoined", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVoiceBridge, OnParticipantJoined), Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantJoined__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnParticipantJoined_MetaData), NewProp_OnParticipantJoined_MetaData) }; // 922362751
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_OnParticipantLeft = { "OnParticipantLeft", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVoiceBridge, OnParticipantLeft), Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantLeft__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnParticipantLeft_MetaData), NewProp_OnParticipantLeft_MetaData) }; // 3483974303
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_OnParticipantStartedSpeaking = { "OnParticipantStartedSpeaking", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVoiceBridge, OnParticipantStartedSpeaking), Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStartedSpeaking__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnParticipantStartedSpeaking_MetaData), NewProp_OnParticipantStartedSpeaking_MetaData) }; // 2569349219
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_OnParticipantStoppedSpeaking = { "OnParticipantStoppedSpeaking", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVoiceBridge, OnParticipantStoppedSpeaking), Z_Construct_UDelegateFunction_UAuracronVoiceBridge_OnParticipantStoppedSpeaking__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnParticipantStoppedSpeaking_MetaData), NewProp_OnParticipantStoppedSpeaking_MetaData) }; // 953272026
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronVoiceBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_VoiceConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_ActiveVoiceChannels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_ActiveVoiceChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_ConnectedParticipants_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_ConnectedParticipants,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_CurrentVoiceState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_CurrentVoiceState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_OnParticipantJoined,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_OnParticipantLeft,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_OnParticipantStartedSpeaking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVoiceBridge_Statics::NewProp_OnParticipantStoppedSpeaking,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVoiceBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronVoiceBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVoiceBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVoiceBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronVoiceBridge_Statics::ClassParams = {
	&UAuracronVoiceBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronVoiceBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVoiceBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVoiceBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronVoiceBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronVoiceBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronVoiceBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronVoiceBridge.OuterSingleton, Z_Construct_UClass_UAuracronVoiceBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronVoiceBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronVoiceBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_VoiceConfiguration(TEXT("VoiceConfiguration"));
	static FName Name_ActiveVoiceChannels(TEXT("ActiveVoiceChannels"));
	static FName Name_ConnectedParticipants(TEXT("ConnectedParticipants"));
	static FName Name_CurrentVoiceState(TEXT("CurrentVoiceState"));
	const bool bIsValid = true
		&& Name_VoiceConfiguration == ClassReps[(int32)ENetFields_Private::VoiceConfiguration].Property->GetFName()
		&& Name_ActiveVoiceChannels == ClassReps[(int32)ENetFields_Private::ActiveVoiceChannels].Property->GetFName()
		&& Name_ConnectedParticipants == ClassReps[(int32)ENetFields_Private::ConnectedParticipants].Property->GetFName()
		&& Name_CurrentVoiceState == ClassReps[(int32)ENetFields_Private::CurrentVoiceState].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronVoiceBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronVoiceBridge);
UAuracronVoiceBridge::~UAuracronVoiceBridge() {}
// ********** End Class UAuracronVoiceBridge *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h__Script_AuracronVoiceBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronVoiceChannelType_StaticEnum, TEXT("EAuracronVoiceChannelType"), &Z_Registration_Info_UEnum_EAuracronVoiceChannelType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1910304459U) },
		{ EAuracronVoiceQuality_StaticEnum, TEXT("EAuracronVoiceQuality"), &Z_Registration_Info_UEnum_EAuracronVoiceQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3140920330U) },
		{ EAuracronVoiceState_StaticEnum, TEXT("EAuracronVoiceState"), &Z_Registration_Info_UEnum_EAuracronVoiceState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 124221615U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronVoiceConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronVoiceConfiguration_Statics::NewStructOps, TEXT("AuracronVoiceConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronVoiceConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronVoiceConfiguration), 2376893664U) },
		{ FAuracronVoiceChannel::StaticStruct, Z_Construct_UScriptStruct_FAuracronVoiceChannel_Statics::NewStructOps, TEXT("AuracronVoiceChannel"), &Z_Registration_Info_UScriptStruct_FAuracronVoiceChannel, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronVoiceChannel), 2004951449U) },
		{ FAuracronVoiceParticipant::StaticStruct, Z_Construct_UScriptStruct_FAuracronVoiceParticipant_Statics::NewStructOps, TEXT("AuracronVoiceParticipant"), &Z_Registration_Info_UScriptStruct_FAuracronVoiceParticipant, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronVoiceParticipant), 4234025503U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronVoiceBridge, UAuracronVoiceBridge::StaticClass, TEXT("UAuracronVoiceBridge"), &Z_Registration_Info_UClass_UAuracronVoiceBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronVoiceBridge), 2316121760U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h__Script_AuracronVoiceBridge_286177182(TEXT("/Script/AuracronVoiceBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h__Script_AuracronVoiceBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h__Script_AuracronVoiceBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h__Script_AuracronVoiceBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h__Script_AuracronVoiceBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h__Script_AuracronVoiceBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVoiceBridge_Public_AuracronVoiceBridge_h__Script_AuracronVoiceBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
