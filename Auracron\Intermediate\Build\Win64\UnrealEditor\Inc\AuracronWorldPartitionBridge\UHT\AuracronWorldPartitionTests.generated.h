// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionTests.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionTests_generated_h
#error "AuracronWorldPartitionTests.generated.h already included, missing '#pragma once' in AuracronWorldPartitionTests.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionTests_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronWorldPartitionTestManager;
enum class EAuracronTestCategory : uint8;
struct FAuracronTestConfiguration;
struct FAuracronTestResult;
struct FAuracronTestSuiteResult;

// ********** Begin ScriptStruct FAuracronTestConfiguration ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_83_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTestConfiguration;
// ********** End ScriptStruct FAuracronTestConfiguration ******************************************

// ********** Begin ScriptStruct FAuracronTestResult ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_169_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTestResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTestResult;
// ********** End ScriptStruct FAuracronTestResult *************************************************

// ********** Begin ScriptStruct FAuracronTestSuiteResult ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_219_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTestSuiteResult;
// ********** End ScriptStruct FAuracronTestSuiteResult ********************************************

// ********** Begin Delegate FOnTestCompleted ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_413_DELEGATE \
static void FOnTestCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTestCompleted, const FString& TestName, FAuracronTestResult Result);


// ********** End Delegate FOnTestCompleted ********************************************************

// ********** Begin Delegate FOnTestSuiteCompleted *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_414_DELEGATE \
static void FOnTestSuiteCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTestSuiteCompleted, const FString& SuiteName, FAuracronTestSuiteResult Result);


// ********** End Delegate FOnTestSuiteCompleted ***************************************************

// ********** Begin Delegate FOnTestFailed *********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_415_DELEGATE \
static void FOnTestFailed_DelegateWrapper(const FMulticastScriptDelegate& OnTestFailed, const FString& TestName, const FString& ErrorMessage);


// ********** End Delegate FOnTestFailed ***********************************************************

// ********** Begin Class UAuracronWorldPartitionTestManager ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_278_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execClearTestHistory); \
	DECLARE_FUNCTION(execGetTestHistory); \
	DECLARE_FUNCTION(execGenerateTestSummary); \
	DECLARE_FUNCTION(execGenerateTestReport); \
	DECLARE_FUNCTION(execTestMemoryStress); \
	DECLARE_FUNCTION(execTestContinuousStreaming); \
	DECLARE_FUNCTION(execRunStressTests); \
	DECLARE_FUNCTION(execTestMassiveStreamingLoad); \
	DECLARE_FUNCTION(execTestLargeWorldCreation); \
	DECLARE_FUNCTION(execRunLargeWorldTests); \
	DECLARE_FUNCTION(execTestStreamingStability); \
	DECLARE_FUNCTION(execTestStreamingPriority); \
	DECLARE_FUNCTION(execTestCellLoadingUnloading); \
	DECLARE_FUNCTION(execRunStreamingTests); \
	DECLARE_FUNCTION(execTestCPUPerformance); \
	DECLARE_FUNCTION(execTestMemoryUsage); \
	DECLARE_FUNCTION(execTestStreamingPerformance); \
	DECLARE_FUNCTION(execRunPerformanceTests); \
	DECLARE_FUNCTION(execTestLumenIntegration); \
	DECLARE_FUNCTION(execTestPCGIntegration); \
	DECLARE_FUNCTION(execTestWorldPartitionIntegration); \
	DECLARE_FUNCTION(execRunIntegrationTests); \
	DECLARE_FUNCTION(execTestDebugSystem); \
	DECLARE_FUNCTION(execTestPerformanceMonitoring); \
	DECLARE_FUNCTION(execTestStreamingSystem); \
	DECLARE_FUNCTION(execTestCellManagement); \
	DECLARE_FUNCTION(execTestGridSystemCreation); \
	DECLARE_FUNCTION(execRunUnitTests); \
	DECLARE_FUNCTION(execCancelRunningTests); \
	DECLARE_FUNCTION(execIsTestRunning); \
	DECLARE_FUNCTION(execRunTestsAsync); \
	DECLARE_FUNCTION(execRunSingleTest); \
	DECLARE_FUNCTION(execRunTestCategory); \
	DECLARE_FUNCTION(execRunAllTests); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionTestManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_278_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionTestManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionTestManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionTestManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionTestManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionTestManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_278_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionTestManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionTestManager(UAuracronWorldPartitionTestManager&&) = delete; \
	UAuracronWorldPartitionTestManager(const UAuracronWorldPartitionTestManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionTestManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionTestManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionTestManager) \
	NO_API virtual ~UAuracronWorldPartitionTestManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_275_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_278_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_278_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_278_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h_278_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionTestManager;

// ********** End Class UAuracronWorldPartitionTestManager *****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h

// ********** Begin Enum EAuracronTestCategory *****************************************************
#define FOREACH_ENUM_EAURACRONTESTCATEGORY(op) \
	op(EAuracronTestCategory::Unit) \
	op(EAuracronTestCategory::Integration) \
	op(EAuracronTestCategory::Performance) \
	op(EAuracronTestCategory::Streaming) \
	op(EAuracronTestCategory::LargeWorld) \
	op(EAuracronTestCategory::Stress) \
	op(EAuracronTestCategory::Regression) \
	op(EAuracronTestCategory::Validation) 

enum class EAuracronTestCategory : uint8;
template<> struct TIsUEnumClass<EAuracronTestCategory> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronTestCategory>();
// ********** End Enum EAuracronTestCategory *******************************************************

// ********** Begin Enum EAuracronTestSeverity *****************************************************
#define FOREACH_ENUM_EAURACRONTESTSEVERITY(op) \
	op(EAuracronTestSeverity::Low) \
	op(EAuracronTestSeverity::Medium) \
	op(EAuracronTestSeverity::High) \
	op(EAuracronTestSeverity::Critical) 

enum class EAuracronTestSeverity : uint8;
template<> struct TIsUEnumClass<EAuracronTestSeverity> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronTestSeverity>();
// ********** End Enum EAuracronTestSeverity *******************************************************

// ********** Begin Enum EAuracronTestExecutionMode ************************************************
#define FOREACH_ENUM_EAURACRONTESTEXECUTIONMODE(op) \
	op(EAuracronTestExecutionMode::Sequential) \
	op(EAuracronTestExecutionMode::Parallel) \
	op(EAuracronTestExecutionMode::Isolated) \
	op(EAuracronTestExecutionMode::Continuous) 

enum class EAuracronTestExecutionMode : uint8;
template<> struct TIsUEnumClass<EAuracronTestExecutionMode> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronTestExecutionMode>();
// ********** End Enum EAuracronTestExecutionMode **************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
