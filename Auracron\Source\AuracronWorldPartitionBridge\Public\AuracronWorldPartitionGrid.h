// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Grid System Header
// Bridge 3.2: World Partition - Grid System

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"

// World Partition Grid includes for UE5.6
#include "WorldPartition/WorldPartitionRuntimeSpatialHash.h"
#include "WorldPartition/WorldPartitionRuntimeCell.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/WorldPartitionActorDesc.h"
#include "WorldPartition/WorldPartitionActorDescView.h"

// Spatial indexing includes
#include "Engine/World.h"
#include "Math/Box.h"
#include "Math/Vector.h"
#include "Math/IntVector.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"

#include "AuracronWorldPartitionGrid.generated.h"

// Forward declarations
class UAuracronWorldPartitionGridManager;
class UAuracronSpatialIndex;
class UWorldPartitionRuntimeSpatialHash;

// =============================================================================
// GRID TYPES AND ENUMS
// =============================================================================

// Grid subdivision types
UENUM(BlueprintType)
enum class EAuracronGridSubdivisionType : uint8
{
    Uniform                 UMETA(DisplayName = "Uniform"),
    Adaptive                UMETA(DisplayName = "Adaptive"),
    Hierarchical            UMETA(DisplayName = "Hierarchical"),
    QuadTree                UMETA(DisplayName = "QuadTree"),
    Octree                  UMETA(DisplayName = "Octree"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Grid cell states
UENUM(BlueprintType)
enum class EAuracronGridCellState : uint8
{
    Empty                   UMETA(DisplayName = "Empty"),
    Populated               UMETA(DisplayName = "Populated"),
    Loading                 UMETA(DisplayName = "Loading"),
    Loaded                  UMETA(DisplayName = "Loaded"),
    Unloading               UMETA(DisplayName = "Unloading"),
    Error                   UMETA(DisplayName = "Error")
};

// Spatial query types
UENUM(BlueprintType)
enum class EAuracronGridSpatialQueryType : uint8
{
    Point                   UMETA(DisplayName = "Point"),
    Sphere                  UMETA(DisplayName = "Sphere"),
    Box                     UMETA(DisplayName = "Box"),
    Frustum                 UMETA(DisplayName = "Frustum"),
    Ray                     UMETA(DisplayName = "Ray"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Grid optimization levels
UENUM(BlueprintType)
enum class EAuracronGridOptimizationLevel : uint8
{
    None                    UMETA(DisplayName = "None"),
    Basic                   UMETA(DisplayName = "Basic"),
    Moderate                UMETA(DisplayName = "Moderate"),
    Aggressive              UMETA(DisplayName = "Aggressive"),
    Maximum                 UMETA(DisplayName = "Maximum")
};

// =============================================================================
// GRID CONFIGURATION
// =============================================================================

/**
 * Grid Configuration
 * Configuration settings for world partition grid system
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronGridConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    EAuracronGridSubdivisionType SubdivisionType = EAuracronGridSubdivisionType::Uniform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    int32 CellSize = 25600; // 256m default

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    int32 MinCellSize = 6400; // 64m minimum

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    int32 MaxCellSize = 102400; // 1024m maximum

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    int32 MaxSubdivisionLevels = 8;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    FVector WorldOrigin = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    FBox WorldBounds = FBox(FVector(-1000000), FVector(1000000));

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    EAuracronGridOptimizationLevel OptimizationLevel = EAuracronGridOptimizationLevel::Moderate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableAdaptiveSubdivision = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableSpatialIndexing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    int32 MaxActorsPerCell = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncProcessing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxConcurrentOperations = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float CellProcessingTimeLimit = 0.016f; // 16ms

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableDebugVisualization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogGridOperations = false;

    FAuracronGridConfiguration()
    {
        SubdivisionType = EAuracronGridSubdivisionType::Uniform;
        CellSize = 25600;
        MinCellSize = 6400;
        MaxCellSize = 102400;
        MaxSubdivisionLevels = 8;
        WorldOrigin = FVector::ZeroVector;
        WorldBounds = FBox(FVector(-1000000), FVector(1000000));
        OptimizationLevel = EAuracronGridOptimizationLevel::Moderate;
        bEnableAdaptiveSubdivision = true;
        bEnableSpatialIndexing = true;
        MaxActorsPerCell = 1000;
        bEnableAsyncProcessing = true;
        MaxConcurrentOperations = 4;
        CellProcessingTimeLimit = 0.016f;
        bEnableDebugVisualization = false;
        bLogGridOperations = false;
    }
};

// =============================================================================
// GRID CELL DATA
// =============================================================================

/**
 * Grid Cell Data
 * Data structure representing a single grid cell
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronGridCell
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    FIntVector Coordinates;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    FBox Bounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    EAuracronGridCellState State = EAuracronGridCellState::Empty;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    int32 SubdivisionLevel = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    int32 ActorCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    float Density = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    TArray<FString> ActorIds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    TArray<FString> ChildCells;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    FString ParentCell;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    TMap<FString, FString> Properties;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    FDateTime LastUpdateTime;

    FAuracronGridCell()
    {
        State = EAuracronGridCellState::Empty;
        SubdivisionLevel = 0;
        ActorCount = 0;
        Density = 0.0f;
        CreationTime = FDateTime::Now();
        LastUpdateTime = CreationTime;
    }

    // Calculate cell density
    void UpdateDensity();

    // Check if cell needs subdivision
    bool ShouldSubdivide(int32 MaxActorsPerCell) const;

    // Get cell center
    FVector GetCenter() const;

    // Get cell size
    FVector GetSize() const;
};

// =============================================================================
// SPATIAL QUERY PARAMETERS
// =============================================================================

/**
 * Spatial Query Parameters
 * Parameters for spatial queries in the grid system
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronSpatialQueryParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    EAuracronGridSpatialQueryType QueryType = EAuracronGridSpatialQueryType::Sphere;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FVector QueryLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float QueryRadius = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FBox QueryBox = FBox(FVector(-500), FVector(500));

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FVector QueryDirection = FVector::ForwardVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float QueryDistance = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    bool bIncludeChildCells = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    bool bIncludeEmptyCells = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    int32 MaxResults = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    TArray<FString> FilterTags;

    FAuracronSpatialQueryParams()
    {
        QueryType = EAuracronGridSpatialQueryType::Sphere;
        QueryLocation = FVector::ZeroVector;
        QueryRadius = 1000.0f;
        QueryBox = FBox(FVector(-500), FVector(500));
        QueryDirection = FVector::ForwardVector;
        QueryDistance = 10000.0f;
        bIncludeChildCells = true;
        bIncludeEmptyCells = false;
        MaxResults = 1000;
    }
};

// =============================================================================
// SPATIAL QUERY RESULT
// =============================================================================

/**
 * Spatial Query Result
 * Result of a spatial query operation
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronGridSpatialQueryResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TArray<FAuracronGridCell> FoundCells;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TArray<FString> FoundActors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    int32 TotalResults = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    float QueryTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    bool bQuerySuccessful = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    FString ErrorMessage;

    FAuracronSpatialQueryResult()
    {
        TotalResults = 0;
        QueryTime = 0.0f;
        bQuerySuccessful = false;
    }
};

// =============================================================================
// GRID STATISTICS
// =============================================================================

/**
 * Grid Statistics
 * Performance and usage statistics for the grid system
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronGridStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalCells = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 PopulatedCells = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 EmptyCells = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 MaxSubdivisionLevel = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageCellDensity = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalActors = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float AverageQueryTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TotalQueries = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 SuccessfulQueries = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float QuerySuccessRate = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronGridStatistics()
    {
        TotalCells = 0;
        PopulatedCells = 0;
        EmptyCells = 0;
        MaxSubdivisionLevel = 0;
        AverageCellDensity = 0.0f;
        TotalActors = 0;
        MemoryUsageMB = 0.0f;
        AverageQueryTime = 0.0f;
        TotalQueries = 0;
        SuccessfulQueries = 0;
        QuerySuccessRate = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// WORLD PARTITION GRID MANAGER
// =============================================================================

/**
 * World Partition Grid Manager
 * Central manager for grid-based world partitioning
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronWorldPartitionGridManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    static UAuracronWorldPartitionGridManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void Initialize(const FAuracronGridConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    bool IsInitialized() const;

    // Grid management
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void CreateGrid(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void DestroyGrid();

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void RebuildGrid();

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void OptimizeGrid();

    // Cell operations
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    FAuracronGridCell CreateCell(const FIntVector& Coordinates, int32 SubdivisionLevel = 0);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    bool RemoveCell(const FString& CellId);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    FAuracronGridCell GetCell(const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    FAuracronGridCell GetCellAtLocation(const FVector& Location) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    TArray<FAuracronGridCell> GetCellsInRadius(const FVector& Location, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    TArray<FAuracronGridCell> GetCellsInBox(const FBox& Box) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    TArray<FAuracronGridCell> GetAllCells() const;

    // Cell subdivision
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    TArray<FAuracronGridCell> SubdivideCell(const FString& CellId);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    bool MergeCells(const TArray<FString>& CellIds);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void AutoSubdivideCells();

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void AutoMergeCells();

    // Actor management
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void AddActorToGrid(const FString& ActorId, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void RemoveActorFromGrid(const FString& ActorId);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void UpdateActorLocation(const FString& ActorId, const FVector& NewLocation);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    TArray<FString> GetActorsInCell(const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    TArray<FString> GetActorsInRadius(const FVector& Location, float Radius) const;

    // Spatial queries
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    FAuracronGridSpatialQueryResult ExecuteSpatialQuery(const FAuracronSpatialQueryParams& QueryParams) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    TArray<FAuracronGridCell> QueryCellsByPoint(const FVector& Point) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    TArray<FAuracronGridCell> QueryCellsBySphere(const FVector& Center, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    TArray<FAuracronGridCell> QueryCellsByBox(const FBox& Box) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    TArray<FAuracronGridCell> QueryCellsByRay(const FVector& Origin, const FVector& Direction, float Distance) const;

    // Coordinate mapping
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    FIntVector WorldToGridCoordinates(const FVector& WorldLocation) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    FVector GridToWorldCoordinates(const FIntVector& GridCoordinates) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    FString CoordinatesToCellId(const FIntVector& Coordinates, int32 SubdivisionLevel = 0) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    FIntVector CellIdToCoordinates(const FString& CellId) const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void SetConfiguration(const FAuracronGridConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    FAuracronGridConfiguration GetConfiguration() const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void SetCellSize(int32 NewCellSize);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    int32 GetCellSize() const;

    // Statistics and monitoring
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    FAuracronGridStatistics GetGridStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    int32 GetTotalCellCount() const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    int32 GetPopulatedCellCount() const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    float GetAverageCellDensity() const;

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void DrawDebugGrid(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void DrawDebugCell(UWorld* World, const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "Grid Manager")
    void LogGridState() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCellCreated, FAuracronGridCell, Cell);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCellRemoved, FString, CellId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCellSubdivided, FString, ParentCellId, TArray<FAuracronGridCell>, ChildCells);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnActorAddedToCell, FString, ActorId, FString, CellId);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCellCreated OnCellCreated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCellRemoved OnCellRemoved;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCellSubdivided OnCellSubdivided;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnActorAddedToCell OnActorAddedToCell;

private:
    static UAuracronWorldPartitionGridManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronGridConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Grid data
    TMap<FString, FAuracronGridCell> GridCells;
    TMap<FString, FString> ActorToCellMap; // ActorId -> CellId
    TMap<FIntVector, FString> CoordinateToCellMap; // Coordinates -> CellId

    // Statistics
    FAuracronGridStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection GridLock;

    // Internal functions
    void UpdateStatistics();
    FBox CalculateCellBounds(const FIntVector& Coordinates, int32 SubdivisionLevel) const;
    bool IsValidCoordinate(const FIntVector& Coordinates) const;
    void OnCellCreatedInternal(const FAuracronGridCell& Cell);
    void OnCellRemovedInternal(const FString& CellId);
    void ValidateConfiguration();
    float CalculateCellDensity(const FAuracronGridCell& Cell) const;
    
    // Grid management helper functions
    TArray<FString> FindAdjacentCells(const FString& CellId) const;
    FAuracronGridCell CreateMergedCell(const TArray<FString>& CellsToMerge);
    void UpdateSpatialHash(const FAuracronGridCell& Cell);
    
    // Spatial indexing
    TMap<FIntVector, TArray<FString>> SpatialHashMap;
    mutable FCriticalSection SpatialHashLock;
    
    // Event declarations for merging
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCellMerged, FString, MergedCellId, TArray<FString>, OriginalCellIds);
    
public:
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCellMerged OnCellMerged;
    
private:
};
