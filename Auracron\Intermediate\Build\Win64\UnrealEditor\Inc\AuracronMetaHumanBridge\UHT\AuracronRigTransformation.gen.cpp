// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanBridge/Public/AuracronRigTransformation.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronRigTransformation() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ERigValidationType();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronConstraintData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FBoneScalingData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FIKChainData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FRetargetingData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FRigValidationResult();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EBoneScalingType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EBoneScalingType;
static UEnum* EBoneScalingType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EBoneScalingType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EBoneScalingType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EBoneScalingType"));
	}
	return Z_Registration_Info_UEnum_EBoneScalingType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EBoneScalingType>()
{
	return EBoneScalingType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums for rig transformation operations\n" },
#endif
		{ "Hierarchical.DisplayName", "Hierarchical" },
		{ "Hierarchical.Name", "EBoneScalingType::Hierarchical" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
		{ "NonUniform.DisplayName", "Non-Uniform" },
		{ "NonUniform.Name", "EBoneScalingType::NonUniform" },
		{ "Proportional.DisplayName", "Proportional" },
		{ "Proportional.Name", "EBoneScalingType::Proportional" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums for rig transformation operations" },
#endif
		{ "Uniform.DisplayName", "Uniform" },
		{ "Uniform.Name", "EBoneScalingType::Uniform" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EBoneScalingType::Uniform", (int64)EBoneScalingType::Uniform },
		{ "EBoneScalingType::NonUniform", (int64)EBoneScalingType::NonUniform },
		{ "EBoneScalingType::Proportional", (int64)EBoneScalingType::Proportional },
		{ "EBoneScalingType::Hierarchical", (int64)EBoneScalingType::Hierarchical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EBoneScalingType",
	"EBoneScalingType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType()
{
	if (!Z_Registration_Info_UEnum_EBoneScalingType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EBoneScalingType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EBoneScalingType.InnerSingleton;
}
// ********** End Enum EBoneScalingType ************************************************************

// ********** Begin Enum EAuracronConstraintType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronConstraintType;
static UEnum* EAuracronConstraintType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronConstraintType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronConstraintType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EAuracronConstraintType"));
	}
	return Z_Registration_Info_UEnum_EAuracronConstraintType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAuracronConstraintType>()
{
	return EAuracronConstraintType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CCDIK.DisplayName", "CCD IK" },
		{ "CCDIK.Name", "EAuracronConstraintType::CCDIK" },
		{ "FABRIK.DisplayName", "FABRIK" },
		{ "FABRIK.Name", "EAuracronConstraintType::FABRIK" },
		{ "LookAt.DisplayName", "Look At" },
		{ "LookAt.Name", "EAuracronConstraintType::LookAt" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
		{ "Parent.DisplayName", "Parent" },
		{ "Parent.Name", "EAuracronConstraintType::Parent" },
		{ "Position.DisplayName", "Position" },
		{ "Position.Name", "EAuracronConstraintType::Position" },
		{ "Rotation.DisplayName", "Rotation" },
		{ "Rotation.Name", "EAuracronConstraintType::Rotation" },
		{ "Scale.DisplayName", "Scale" },
		{ "Scale.Name", "EAuracronConstraintType::Scale" },
		{ "TwoBoneIK.DisplayName", "Two Bone IK" },
		{ "TwoBoneIK.Name", "EAuracronConstraintType::TwoBoneIK" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronConstraintType::Position", (int64)EAuracronConstraintType::Position },
		{ "EAuracronConstraintType::Rotation", (int64)EAuracronConstraintType::Rotation },
		{ "EAuracronConstraintType::Scale", (int64)EAuracronConstraintType::Scale },
		{ "EAuracronConstraintType::Parent", (int64)EAuracronConstraintType::Parent },
		{ "EAuracronConstraintType::LookAt", (int64)EAuracronConstraintType::LookAt },
		{ "EAuracronConstraintType::TwoBoneIK", (int64)EAuracronConstraintType::TwoBoneIK },
		{ "EAuracronConstraintType::FABRIK", (int64)EAuracronConstraintType::FABRIK },
		{ "EAuracronConstraintType::CCDIK", (int64)EAuracronConstraintType::CCDIK },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EAuracronConstraintType",
	"EAuracronConstraintType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType()
{
	if (!Z_Registration_Info_UEnum_EAuracronConstraintType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronConstraintType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronConstraintType.InnerSingleton;
}
// ********** End Enum EAuracronConstraintType *****************************************************

// ********** Begin Enum EIKSolverType *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EIKSolverType;
static UEnum* EIKSolverType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EIKSolverType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EIKSolverType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EIKSolverType"));
	}
	return Z_Registration_Info_UEnum_EIKSolverType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EIKSolverType>()
{
	return EIKSolverType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CCDIK.DisplayName", "CCD IK" },
		{ "CCDIK.Name", "EIKSolverType::CCDIK" },
		{ "FABRIK.DisplayName", "FABRIK" },
		{ "FABRIK.Name", "EIKSolverType::FABRIK" },
		{ "FullBody.DisplayName", "Full Body" },
		{ "FullBody.Name", "EIKSolverType::FullBody" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
		{ "TwoBone.DisplayName", "Two Bone" },
		{ "TwoBone.Name", "EIKSolverType::TwoBone" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EIKSolverType::TwoBone", (int64)EIKSolverType::TwoBone },
		{ "EIKSolverType::FABRIK", (int64)EIKSolverType::FABRIK },
		{ "EIKSolverType::CCDIK", (int64)EIKSolverType::CCDIK },
		{ "EIKSolverType::FullBody", (int64)EIKSolverType::FullBody },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EIKSolverType",
	"EIKSolverType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType()
{
	if (!Z_Registration_Info_UEnum_EIKSolverType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EIKSolverType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EIKSolverType.InnerSingleton;
}
// ********** End Enum EIKSolverType ***************************************************************

// ********** Begin Enum ERigValidationType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERigValidationType;
static UEnum* ERigValidationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERigValidationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERigValidationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_ERigValidationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ERigValidationType"));
	}
	return Z_Registration_Info_UEnum_ERigValidationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ERigValidationType>()
{
	return ERigValidationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_ERigValidationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "ERigValidationType::Basic" },
		{ "BlueprintType", "true" },
		{ "Comprehensive.DisplayName", "Comprehensive" },
		{ "Comprehensive.Name", "ERigValidationType::Comprehensive" },
		{ "Constraints.DisplayName", "Constraints" },
		{ "Constraints.Name", "ERigValidationType::Constraints" },
		{ "Hierarchy.DisplayName", "Hierarchy" },
		{ "Hierarchy.Name", "ERigValidationType::Hierarchy" },
		{ "IKChains.DisplayName", "IK Chains" },
		{ "IKChains.Name", "ERigValidationType::IKChains" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERigValidationType::Basic", (int64)ERigValidationType::Basic },
		{ "ERigValidationType::Hierarchy", (int64)ERigValidationType::Hierarchy },
		{ "ERigValidationType::Constraints", (int64)ERigValidationType::Constraints },
		{ "ERigValidationType::IKChains", (int64)ERigValidationType::IKChains },
		{ "ERigValidationType::Comprehensive", (int64)ERigValidationType::Comprehensive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_ERigValidationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"ERigValidationType",
	"ERigValidationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_ERigValidationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ERigValidationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ERigValidationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_ERigValidationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ERigValidationType()
{
	if (!Z_Registration_Info_UEnum_ERigValidationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERigValidationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_ERigValidationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERigValidationType.InnerSingleton;
}
// ********** End Enum ERigValidationType **********************************************************

// ********** Begin ScriptStruct FBoneScalingData **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FBoneScalingData;
class UScriptStruct* FBoneScalingData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FBoneScalingData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FBoneScalingData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FBoneScalingData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("BoneScalingData"));
	}
	return Z_Registration_Info_UScriptStruct_FBoneScalingData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FBoneScalingData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures for rig transformation data\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures for rig transformation data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoneIndex_MetaData[] = {
		{ "Category", "Bone Scaling" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleFactor_MetaData[] = {
		{ "Category", "Bone Scaling" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScalingType_MetaData[] = {
		{ "Category", "Bone Scaling" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPropagateToChildren_MetaData[] = {
		{ "Category", "Bone Scaling" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExcludedBones_MetaData[] = {
		{ "Category", "Bone Scaling" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_BoneIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleFactor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ScalingType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ScalingType;
	static void NewProp_bPropagateToChildren_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPropagateToChildren;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExcludedBones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExcludedBones;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FBoneScalingData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_BoneIndex = { "BoneIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBoneScalingData, BoneIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoneIndex_MetaData), NewProp_BoneIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_ScaleFactor = { "ScaleFactor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBoneScalingData, ScaleFactor), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleFactor_MetaData), NewProp_ScaleFactor_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_ScalingType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_ScalingType = { "ScalingType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBoneScalingData, ScalingType), Z_Construct_UEnum_AuracronMetaHumanBridge_EBoneScalingType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScalingType_MetaData), NewProp_ScalingType_MetaData) }; // 1971812828
void Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_bPropagateToChildren_SetBit(void* Obj)
{
	((FBoneScalingData*)Obj)->bPropagateToChildren = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_bPropagateToChildren = { "bPropagateToChildren", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FBoneScalingData), &Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_bPropagateToChildren_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPropagateToChildren_MetaData), NewProp_bPropagateToChildren_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_ExcludedBones_Inner = { "ExcludedBones", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_ExcludedBones = { "ExcludedBones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBoneScalingData, ExcludedBones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExcludedBones_MetaData), NewProp_ExcludedBones_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FBoneScalingData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_BoneIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_ScaleFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_ScalingType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_ScalingType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_bPropagateToChildren,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_ExcludedBones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewProp_ExcludedBones,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBoneScalingData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FBoneScalingData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"BoneScalingData",
	Z_Construct_UScriptStruct_FBoneScalingData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBoneScalingData_Statics::PropPointers),
	sizeof(FBoneScalingData),
	alignof(FBoneScalingData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBoneScalingData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FBoneScalingData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FBoneScalingData()
{
	if (!Z_Registration_Info_UScriptStruct_FBoneScalingData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FBoneScalingData.InnerSingleton, Z_Construct_UScriptStruct_FBoneScalingData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FBoneScalingData.InnerSingleton;
}
// ********** End ScriptStruct FBoneScalingData ****************************************************

// ********** Begin ScriptStruct FAuracronConstraintData *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronConstraintData;
class UScriptStruct* FAuracronConstraintData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronConstraintData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronConstraintData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronConstraintData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("AuracronConstraintData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronConstraintData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronConstraintData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintType_MetaData[] = {
		{ "Category", "Constraint" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceBoneIndex_MetaData[] = {
		{ "Category", "Constraint" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetBoneIndex_MetaData[] = {
		{ "Category", "Constraint" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weight_MetaData[] = {
		{ "Category", "Constraint" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Constraint" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionOffset_MetaData[] = {
		{ "Category", "Constraint" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationOffset_MetaData[] = {
		{ "Category", "Constraint" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleOffset_MetaData[] = {
		{ "Category", "Constraint" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ConstraintType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ConstraintType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SourceBoneIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetBoneIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Weight;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RotationOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleOffset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronConstraintData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_ConstraintType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_ConstraintType = { "ConstraintType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronConstraintData, ConstraintType), Z_Construct_UEnum_AuracronMetaHumanBridge_EAuracronConstraintType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintType_MetaData), NewProp_ConstraintType_MetaData) }; // 1575409566
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_SourceBoneIndex = { "SourceBoneIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronConstraintData, SourceBoneIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceBoneIndex_MetaData), NewProp_SourceBoneIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_TargetBoneIndex = { "TargetBoneIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronConstraintData, TargetBoneIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetBoneIndex_MetaData), NewProp_TargetBoneIndex_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_Weight = { "Weight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronConstraintData, Weight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weight_MetaData), NewProp_Weight_MetaData) };
void Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronConstraintData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronConstraintData), &Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_PositionOffset = { "PositionOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronConstraintData, PositionOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionOffset_MetaData), NewProp_PositionOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_RotationOffset = { "RotationOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronConstraintData, RotationOffset), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationOffset_MetaData), NewProp_RotationOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_ScaleOffset = { "ScaleOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronConstraintData, ScaleOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleOffset_MetaData), NewProp_ScaleOffset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_ConstraintType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_ConstraintType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_SourceBoneIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_TargetBoneIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_Weight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_PositionOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_RotationOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewProp_ScaleOffset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"AuracronConstraintData",
	Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::PropPointers),
	sizeof(FAuracronConstraintData),
	alignof(FAuracronConstraintData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronConstraintData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronConstraintData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronConstraintData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronConstraintData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronConstraintData *********************************************

// ********** Begin ScriptStruct FIKChainData ******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FIKChainData;
class UScriptStruct* FIKChainData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FIKChainData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FIKChainData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FIKChainData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("IKChainData"));
	}
	return Z_Registration_Info_UScriptStruct_FIKChainData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FIKChainData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChainName_MetaData[] = {
		{ "Category", "IK Chain" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoneChain_MetaData[] = {
		{ "Category", "IK Chain" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolverType_MetaData[] = {
		{ "Category", "IK Chain" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPosition_MetaData[] = {
		{ "Category", "IK Chain" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetRotation_MetaData[] = {
		{ "Category", "IK Chain" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weight_MetaData[] = {
		{ "Category", "IK Chain" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "IK Chain" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxIterations_MetaData[] = {
		{ "Category", "IK Chain" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Precision_MetaData[] = {
		{ "Category", "IK Chain" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChainName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BoneChain_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BoneChain;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SolverType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SolverType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetRotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Weight;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxIterations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Precision;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FIKChainData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_ChainName = { "ChainName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FIKChainData, ChainName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChainName_MetaData), NewProp_ChainName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_BoneChain_Inner = { "BoneChain", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_BoneChain = { "BoneChain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FIKChainData, BoneChain), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoneChain_MetaData), NewProp_BoneChain_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_SolverType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_SolverType = { "SolverType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FIKChainData, SolverType), Z_Construct_UEnum_AuracronMetaHumanBridge_EIKSolverType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolverType_MetaData), NewProp_SolverType_MetaData) }; // 2582122045
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_TargetPosition = { "TargetPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FIKChainData, TargetPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPosition_MetaData), NewProp_TargetPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_TargetRotation = { "TargetRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FIKChainData, TargetRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetRotation_MetaData), NewProp_TargetRotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_Weight = { "Weight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FIKChainData, Weight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weight_MetaData), NewProp_Weight_MetaData) };
void Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FIKChainData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FIKChainData), &Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_MaxIterations = { "MaxIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FIKChainData, MaxIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxIterations_MetaData), NewProp_MaxIterations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_Precision = { "Precision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FIKChainData, Precision), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Precision_MetaData), NewProp_Precision_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FIKChainData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_ChainName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_BoneChain_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_BoneChain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_SolverType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_SolverType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_TargetPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_TargetRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_Weight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_MaxIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FIKChainData_Statics::NewProp_Precision,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FIKChainData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FIKChainData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"IKChainData",
	Z_Construct_UScriptStruct_FIKChainData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FIKChainData_Statics::PropPointers),
	sizeof(FIKChainData),
	alignof(FIKChainData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FIKChainData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FIKChainData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FIKChainData()
{
	if (!Z_Registration_Info_UScriptStruct_FIKChainData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FIKChainData.InnerSingleton, Z_Construct_UScriptStruct_FIKChainData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FIKChainData.InnerSingleton;
}
// ********** End ScriptStruct FIKChainData ********************************************************

// ********** Begin ScriptStruct FRetargetingData **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRetargetingData;
class UScriptStruct* FRetargetingData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRetargetingData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRetargetingData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRetargetingData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("RetargetingData"));
	}
	return Z_Registration_Info_UScriptStruct_FRetargetingData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRetargetingData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceSkeletonIndex_MetaData[] = {
		{ "Category", "Retargeting" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetSkeletonIndex_MetaData[] = {
		{ "Category", "Retargeting" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoneMapping_MetaData[] = {
		{ "Category", "Retargeting" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRetargetTranslation_MetaData[] = {
		{ "Category", "Retargeting" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRetargetRotation_MetaData[] = {
		{ "Category", "Retargeting" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRetargetScale_MetaData[] = {
		{ "Category", "Retargeting" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleFactor_MetaData[] = {
		{ "Category", "Retargeting" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SourceSkeletonIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetSkeletonIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BoneMapping_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BoneMapping_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BoneMapping;
	static void NewProp_bRetargetTranslation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRetargetTranslation;
	static void NewProp_bRetargetRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRetargetRotation;
	static void NewProp_bRetargetScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRetargetScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScaleFactor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRetargetingData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_SourceSkeletonIndex = { "SourceSkeletonIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRetargetingData, SourceSkeletonIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceSkeletonIndex_MetaData), NewProp_SourceSkeletonIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_TargetSkeletonIndex = { "TargetSkeletonIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRetargetingData, TargetSkeletonIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetSkeletonIndex_MetaData), NewProp_TargetSkeletonIndex_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_BoneMapping_ValueProp = { "BoneMapping", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_BoneMapping_Key_KeyProp = { "BoneMapping_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_BoneMapping = { "BoneMapping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRetargetingData, BoneMapping), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoneMapping_MetaData), NewProp_BoneMapping_MetaData) };
void Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetTranslation_SetBit(void* Obj)
{
	((FRetargetingData*)Obj)->bRetargetTranslation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetTranslation = { "bRetargetTranslation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRetargetingData), &Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetTranslation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRetargetTranslation_MetaData), NewProp_bRetargetTranslation_MetaData) };
void Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetRotation_SetBit(void* Obj)
{
	((FRetargetingData*)Obj)->bRetargetRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetRotation = { "bRetargetRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRetargetingData), &Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRetargetRotation_MetaData), NewProp_bRetargetRotation_MetaData) };
void Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetScale_SetBit(void* Obj)
{
	((FRetargetingData*)Obj)->bRetargetScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetScale = { "bRetargetScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRetargetingData), &Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRetargetScale_MetaData), NewProp_bRetargetScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_ScaleFactor = { "ScaleFactor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRetargetingData, ScaleFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleFactor_MetaData), NewProp_ScaleFactor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRetargetingData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_SourceSkeletonIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_TargetSkeletonIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_BoneMapping_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_BoneMapping_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_BoneMapping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetTranslation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_bRetargetScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRetargetingData_Statics::NewProp_ScaleFactor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRetargetingData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRetargetingData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"RetargetingData",
	Z_Construct_UScriptStruct_FRetargetingData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRetargetingData_Statics::PropPointers),
	sizeof(FRetargetingData),
	alignof(FRetargetingData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRetargetingData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRetargetingData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRetargetingData()
{
	if (!Z_Registration_Info_UScriptStruct_FRetargetingData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRetargetingData.InnerSingleton, Z_Construct_UScriptStruct_FRetargetingData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRetargetingData.InnerSingleton;
}
// ********** End ScriptStruct FRetargetingData ****************************************************

// ********** Begin ScriptStruct FRigValidationResult **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRigValidationResult;
class UScriptStruct* FRigValidationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRigValidationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRigValidationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRigValidationResult, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("RigValidationResult"));
	}
	return Z_Registration_Info_UScriptStruct_FRigValidationResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRigValidationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "Rig Validation" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Errors_MetaData[] = {
		{ "Category", "Rig Validation" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Warnings_MetaData[] = {
		{ "Category", "Rig Validation" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoneCount_MetaData[] = {
		{ "Category", "Rig Validation" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintCount_MetaData[] = {
		{ "Category", "Rig Validation" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IKChainCount_MetaData[] = {
		{ "Category", "Rig Validation" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceScore_MetaData[] = {
		{ "Category", "Rig Validation" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompatibilityScore_MetaData[] = {
		{ "Category", "Rig Validation" },
		{ "ModuleRelativePath", "Public/AuracronRigTransformation.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Errors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Errors;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Warnings_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Warnings;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BoneCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ConstraintCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_IKChainCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompatibilityScore;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRigValidationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FRigValidationResult*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRigValidationResult), &Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_Errors_Inner = { "Errors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_Errors = { "Errors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRigValidationResult, Errors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Errors_MetaData), NewProp_Errors_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_Warnings_Inner = { "Warnings", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_Warnings = { "Warnings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRigValidationResult, Warnings), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Warnings_MetaData), NewProp_Warnings_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_BoneCount = { "BoneCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRigValidationResult, BoneCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoneCount_MetaData), NewProp_BoneCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_ConstraintCount = { "ConstraintCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRigValidationResult, ConstraintCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintCount_MetaData), NewProp_ConstraintCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_IKChainCount = { "IKChainCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRigValidationResult, IKChainCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IKChainCount_MetaData), NewProp_IKChainCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_PerformanceScore = { "PerformanceScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRigValidationResult, PerformanceScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceScore_MetaData), NewProp_PerformanceScore_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_CompatibilityScore = { "CompatibilityScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRigValidationResult, CompatibilityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompatibilityScore_MetaData), NewProp_CompatibilityScore_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRigValidationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_bIsValid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_Errors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_Errors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_Warnings_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_Warnings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_BoneCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_ConstraintCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_IKChainCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_PerformanceScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewProp_CompatibilityScore,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRigValidationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRigValidationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"RigValidationResult",
	Z_Construct_UScriptStruct_FRigValidationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRigValidationResult_Statics::PropPointers),
	sizeof(FRigValidationResult),
	alignof(FRigValidationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRigValidationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRigValidationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRigValidationResult()
{
	if (!Z_Registration_Info_UScriptStruct_FRigValidationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRigValidationResult.InnerSingleton, Z_Construct_UScriptStruct_FRigValidationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRigValidationResult.InnerSingleton;
}
// ********** End ScriptStruct FRigValidationResult ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h__Script_AuracronMetaHumanBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EBoneScalingType_StaticEnum, TEXT("EBoneScalingType"), &Z_Registration_Info_UEnum_EBoneScalingType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1971812828U) },
		{ EAuracronConstraintType_StaticEnum, TEXT("EAuracronConstraintType"), &Z_Registration_Info_UEnum_EAuracronConstraintType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1575409566U) },
		{ EIKSolverType_StaticEnum, TEXT("EIKSolverType"), &Z_Registration_Info_UEnum_EIKSolverType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2582122045U) },
		{ ERigValidationType_StaticEnum, TEXT("ERigValidationType"), &Z_Registration_Info_UEnum_ERigValidationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1289837283U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FBoneScalingData::StaticStruct, Z_Construct_UScriptStruct_FBoneScalingData_Statics::NewStructOps, TEXT("BoneScalingData"), &Z_Registration_Info_UScriptStruct_FBoneScalingData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FBoneScalingData), 3737195691U) },
		{ FAuracronConstraintData::StaticStruct, Z_Construct_UScriptStruct_FAuracronConstraintData_Statics::NewStructOps, TEXT("AuracronConstraintData"), &Z_Registration_Info_UScriptStruct_FAuracronConstraintData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronConstraintData), 1360711162U) },
		{ FIKChainData::StaticStruct, Z_Construct_UScriptStruct_FIKChainData_Statics::NewStructOps, TEXT("IKChainData"), &Z_Registration_Info_UScriptStruct_FIKChainData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FIKChainData), 1290192535U) },
		{ FRetargetingData::StaticStruct, Z_Construct_UScriptStruct_FRetargetingData_Statics::NewStructOps, TEXT("RetargetingData"), &Z_Registration_Info_UScriptStruct_FRetargetingData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRetargetingData), 759157333U) },
		{ FRigValidationResult::StaticStruct, Z_Construct_UScriptStruct_FRigValidationResult_Statics::NewStructOps, TEXT("RigValidationResult"), &Z_Registration_Info_UScriptStruct_FRigValidationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRigValidationResult), 1033872092U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h__Script_AuracronMetaHumanBridge_330345944(TEXT("/Script/AuracronMetaHumanBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
