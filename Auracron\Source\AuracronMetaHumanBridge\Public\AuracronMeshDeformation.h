#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Math/Vector.h"
#include "Math/Transform.h"
#include "Engine/StaticMesh.h"
#include "Engine/SkeletalMesh.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Rendering/SkeletalMeshModel.h"
#include "Rendering/SkeletalMeshLODModel.h"
#include "StaticMeshResources.h"

#include "AuracronMeshDeformation.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronMeshDeformation, Log, All);

// Enums for mesh deformation operations
UENUM(BlueprintType)
enum class EVertexManipulationType : uint8
{
    Absolute        UMETA(DisplayName = "Absolute"),
    Relative        UMETA(DisplayName = "Relative"),
    Additive        UMETA(DisplayName = "Additive"),
    Multiplicative  UMETA(DisplayName = "Multiplicative")
};

UENUM(BlueprintType)
enum class ENormalRecalculationType : uint8
{
    None            UMETA(DisplayName = "None"),
    Weighted        UMETA(DisplayName = "Weighted"),
    Uniform         UMETA(DisplayName = "Uniform"),
    AngleWeighted   UMETA(DisplayName = "Angle Weighted")
};

UENUM(BlueprintType)
enum class EUVPreservationType : uint8
{
    None            UMETA(DisplayName = "None"),
    Stretch         UMETA(DisplayName = "Stretch"),
    Conformal       UMETA(DisplayName = "Conformal"),
    Authalic        UMETA(DisplayName = "Authalic")
};

UENUM(BlueprintType)
enum class EMeshValidationType : uint8
{
    Basic           UMETA(DisplayName = "Basic"),
    Comprehensive   UMETA(DisplayName = "Comprehensive"),
    Topology        UMETA(DisplayName = "Topology"),
    Geometry        UMETA(DisplayName = "Geometry"),
    UVMapping       UMETA(DisplayName = "UV Mapping")
};

// Structures for mesh deformation data
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FVertexData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertex Data")
    FVector Position = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertex Data")
    FVector Normal = FVector::UpVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertex Data")
    FVector Tangent = FVector::ForwardVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertex Data")
    FVector Binormal = FVector::RightVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertex Data")
    TArray<FVector2D> UVChannels;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertex Data")
    FLinearColor VertexColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertex Data")
    TArray<float> BlendWeights;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertex Data")
    TArray<int32> BlendIndices;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FMeshDeformationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Deformation")
    int32 MeshIndex = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Deformation")
    TArray<int32> VertexIndices;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Deformation")
    TArray<FVector> TargetPositions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Deformation")
    EVertexManipulationType ManipulationType = EVertexManipulationType::Absolute;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Deformation")
    float BlendWeight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Deformation")
    bool bRecalculateNormals = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Deformation")
    bool bPreserveUVs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Deformation")
    bool bUpdateTangents = true;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FLODGenerationSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Generation")
    int32 NumLODs = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Generation")
    TArray<float> ReductionPercentages = {0.5f, 0.25f, 0.125f, 0.0625f};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Generation")
    bool bPreserveUVBoundaries = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Generation")
    bool bPreserveHardEdges = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Generation")
    float WeldingThreshold = 0.0001f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Generation")
    bool bRecalculateNormals = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Generation")
    bool bGenerateLightmapUVs = false;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FMeshValidationResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Validation")
    bool bIsValid = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Validation")
    TArray<FString> Errors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Validation")
    TArray<FString> Warnings;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Validation")
    int32 VertexCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Validation")
    int32 TriangleCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Validation")
    int32 DegenerateTriangles = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Validation")
    int32 DuplicateVertices = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Validation")
    float QualityScore = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Validation")
    float PerformanceScore = 100.0f;
};

/**
 * Mesh deformation system for MetaHuman Bridge
 * Provides advanced mesh manipulation capabilities with UE5.6 APIs
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronMeshDeformation
{
public:
    FAuracronMeshDeformation();
    ~FAuracronMeshDeformation();

    // Core mesh deformation operations
    bool SetVertexPositions(int32 MeshIndex, const TArray<int32>& VertexIndices, const TArray<FVector>& Positions);
    bool TransformVertices(int32 MeshIndex, const TArray<int32>& VertexIndices, const FTransform& Transform, EVertexManipulationType ManipulationType);
    bool DeformMeshRegion(const FMeshDeformationData& DeformationData);
    bool ApplySmoothDeformation(int32 MeshIndex, int32 CenterVertex, float Radius, const FVector& Displacement, float Falloff);

    // Normal and tangent operations
    bool RecalculateNormals(int32 MeshIndex, ENormalRecalculationType RecalculationType, const TArray<int32>& VertexIndices = TArray<int32>());
    bool RecalculateTangents(int32 MeshIndex, const TArray<int32>& VertexIndices = TArray<int32>());
    bool PreserveUVMapping(int32 MeshIndex, EUVPreservationType PreservationType, const TArray<int32>& VertexIndices = TArray<int32>());

    // LOD generation
    int32 GenerateMeshLODs(int32 MeshIndex, const FLODGenerationSettings& Settings);
    bool GenerateLODLevel(int32 MeshIndex, int32 LODLevel, float ReductionPercentage, const FLODGenerationSettings& Settings);

    // Mesh optimization
    int32 OptimizeMeshDeformation(int32 MeshIndex, bool bRemoveUnusedVertices, bool bMergeIdenticalVertices, float Tolerance);
    bool WeldVertices(int32 MeshIndex, float WeldingThreshold);
    bool RemoveDegenerateTriangles(int32 MeshIndex);

    // Mesh validation
    FMeshValidationResult ValidateMeshIntegrity(int32 MeshIndex, EMeshValidationType ValidationType) const;
    bool ValidateTopology(int32 MeshIndex) const;
    bool ValidateGeometry(int32 MeshIndex) const;
    bool ValidateUVMapping(int32 MeshIndex) const;

    // Vertex data operations
    bool SetVertexData(int32 MeshIndex, const TArray<int32>& VertexIndices, const TArray<FVertexData>& VertexData);
    TArray<FVertexData> GetVertexData(int32 MeshIndex, const TArray<int32>& VertexIndices) const;
    bool SetVertexColors(int32 MeshIndex, const TArray<int32>& VertexIndices, const TArray<FLinearColor>& Colors);
    bool SetVertexUVs(int32 MeshIndex, const TArray<int32>& VertexIndices, const TArray<FVector2D>& UVs, int32 UVChannel = 0);

    // Blend shape operations
    FMeshDeformationData CreateDeformationFromBlendShape(int32 MeshIndex, int32 BlendShapeIndex, float Weight) const;
    bool ApplyBlendShapeDeformation(int32 MeshIndex, int32 BlendShapeIndex, float Weight);

    // Performance metrics
    float CalculateMeshQualityScore(int32 MeshIndex) const;
    float CalculateMeshPerformanceScore(int32 MeshIndex) const;
    int32 GetVertexCount(int32 MeshIndex) const;
    int32 GetTriangleCount(int32 MeshIndex) const;

    // Thread safety
    mutable FCriticalSection MeshDeformationMutex;

private:
    // State tracking
    FThreadSafeBool bMeshDeformationCacheValid;
    mutable TMap<int32, FMeshValidationResult> MeshValidationCache;

    // Internal helper methods
    bool IsValidVertexIndex(int32 MeshIndex, int32 VertexIndex) const;
    bool IsValidVertexData(const FVertexData& VertexData) const;
    bool IsValidMeshDeformationData(const FMeshDeformationData& DeformationData) const;
    
    void InvalidateMeshDeformationCache() const;
    void BuildMeshDeformationCache() const;
    
    FVector CalculateVertexNormal(int32 MeshIndex, int32 VertexIndex, ENormalRecalculationType RecalculationType) const;
    FVector2D CalculateVertexUV(int32 MeshIndex, int32 VertexIndex, EUVPreservationType PreservationType) const;
    float CalculateDeformationFalloff(float Distance, float Radius, float FalloffExponent) const;
    
    bool AreVerticesIdentical(const FVertexData& Vertex1, const FVertexData& Vertex2, float Tolerance) const;
    int32 CalculateTargetVertexCount(int32 OriginalCount, float ReductionPercentage) const;
    bool ValidateLODSettings(const FLODGenerationSettings& Settings) const;
    
    // Mesh access helpers
    UStaticMesh* GetStaticMeshFromIndex(int32 MeshIndex) const;
    const FMeshDescription* GetMeshDescription(int32 MeshIndex) const;
    float CalculateVertexCacheEfficiency(int32 MeshIndex) const;

    // Prevent copying
    FAuracronMeshDeformation(const FAuracronMeshDeformation&) = delete;
    FAuracronMeshDeformation& operator=(const FAuracronMeshDeformation&) = delete;
};
