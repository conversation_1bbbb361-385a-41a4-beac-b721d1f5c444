// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionStreaming.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionStreaming() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionStreamingManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingRequest();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingSource();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronStreamingPriority ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronStreamingPriority;
static UEnum* EAuracronStreamingPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronStreamingPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronStreamingPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronStreamingPriority"));
	}
	return Z_Registration_Info_UEnum_EAuracronStreamingPriority.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronStreamingPriority>()
{
	return EAuracronStreamingPriority_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming priorities\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAuracronStreamingPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronStreamingPriority::High" },
		{ "Highest.DisplayName", "Highest" },
		{ "Highest.Name", "EAuracronStreamingPriority::Highest" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronStreamingPriority::Low" },
		{ "Lowest.DisplayName", "Lowest" },
		{ "Lowest.Name", "EAuracronStreamingPriority::Lowest" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAuracronStreamingPriority::Normal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming priorities" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronStreamingPriority::Lowest", (int64)EAuracronStreamingPriority::Lowest },
		{ "EAuracronStreamingPriority::Low", (int64)EAuracronStreamingPriority::Low },
		{ "EAuracronStreamingPriority::Normal", (int64)EAuracronStreamingPriority::Normal },
		{ "EAuracronStreamingPriority::High", (int64)EAuracronStreamingPriority::High },
		{ "EAuracronStreamingPriority::Highest", (int64)EAuracronStreamingPriority::Highest },
		{ "EAuracronStreamingPriority::Critical", (int64)EAuracronStreamingPriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronStreamingPriority",
	"EAuracronStreamingPriority",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority()
{
	if (!Z_Registration_Info_UEnum_EAuracronStreamingPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronStreamingPriority.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronStreamingPriority.InnerSingleton;
}
// ********** End Enum EAuracronStreamingPriority **************************************************

// ********** Begin Enum EAuracronStreamingRequestType *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronStreamingRequestType;
static UEnum* EAuracronStreamingRequestType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronStreamingRequestType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronStreamingRequestType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronStreamingRequestType"));
	}
	return Z_Registration_Info_UEnum_EAuracronStreamingRequestType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronStreamingRequestType>()
{
	return EAuracronStreamingRequestType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming request types\n" },
#endif
		{ "ForceLoad.DisplayName", "Force Load" },
		{ "ForceLoad.Name", "EAuracronStreamingRequestType::ForceLoad" },
		{ "ForceUnload.DisplayName", "Force Unload" },
		{ "ForceUnload.Name", "EAuracronStreamingRequestType::ForceUnload" },
		{ "Load.DisplayName", "Load" },
		{ "Load.Name", "EAuracronStreamingRequestType::Load" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
		{ "Preload.DisplayName", "Preload" },
		{ "Preload.Name", "EAuracronStreamingRequestType::Preload" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming request types" },
#endif
		{ "Unload.DisplayName", "Unload" },
		{ "Unload.Name", "EAuracronStreamingRequestType::Unload" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronStreamingRequestType::Load", (int64)EAuracronStreamingRequestType::Load },
		{ "EAuracronStreamingRequestType::Unload", (int64)EAuracronStreamingRequestType::Unload },
		{ "EAuracronStreamingRequestType::Preload", (int64)EAuracronStreamingRequestType::Preload },
		{ "EAuracronStreamingRequestType::ForceLoad", (int64)EAuracronStreamingRequestType::ForceLoad },
		{ "EAuracronStreamingRequestType::ForceUnload", (int64)EAuracronStreamingRequestType::ForceUnload },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronStreamingRequestType",
	"EAuracronStreamingRequestType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType()
{
	if (!Z_Registration_Info_UEnum_EAuracronStreamingRequestType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronStreamingRequestType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronStreamingRequestType.InnerSingleton;
}
// ********** End Enum EAuracronStreamingRequestType ***********************************************

// ********** Begin Enum EAuracronStreamingRequestState ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronStreamingRequestState;
static UEnum* EAuracronStreamingRequestState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronStreamingRequestState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronStreamingRequestState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronStreamingRequestState"));
	}
	return Z_Registration_Info_UEnum_EAuracronStreamingRequestState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronStreamingRequestState>()
{
	return EAuracronStreamingRequestState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cancelled.DisplayName", "Cancelled" },
		{ "Cancelled.Name", "EAuracronStreamingRequestState::Cancelled" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming states\n" },
#endif
		{ "Completed.DisplayName", "Completed" },
		{ "Completed.Name", "EAuracronStreamingRequestState::Completed" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronStreamingRequestState::Failed" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
		{ "Pending.DisplayName", "Pending" },
		{ "Pending.Name", "EAuracronStreamingRequestState::Pending" },
		{ "Processing.DisplayName", "Processing" },
		{ "Processing.Name", "EAuracronStreamingRequestState::Processing" },
		{ "Timeout.DisplayName", "Timeout" },
		{ "Timeout.Name", "EAuracronStreamingRequestState::Timeout" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming states" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronStreamingRequestState::Pending", (int64)EAuracronStreamingRequestState::Pending },
		{ "EAuracronStreamingRequestState::Processing", (int64)EAuracronStreamingRequestState::Processing },
		{ "EAuracronStreamingRequestState::Completed", (int64)EAuracronStreamingRequestState::Completed },
		{ "EAuracronStreamingRequestState::Failed", (int64)EAuracronStreamingRequestState::Failed },
		{ "EAuracronStreamingRequestState::Cancelled", (int64)EAuracronStreamingRequestState::Cancelled },
		{ "EAuracronStreamingRequestState::Timeout", (int64)EAuracronStreamingRequestState::Timeout },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronStreamingRequestState",
	"EAuracronStreamingRequestState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState()
{
	if (!Z_Registration_Info_UEnum_EAuracronStreamingRequestState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronStreamingRequestState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronStreamingRequestState.InnerSingleton;
}
// ********** End Enum EAuracronStreamingRequestState **********************************************

// ********** Begin Enum EAuracronMemoryManagementStrategy *****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronMemoryManagementStrategy;
static UEnum* EAuracronMemoryManagementStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronMemoryManagementStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronMemoryManagementStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronMemoryManagementStrategy"));
	}
	return Z_Registration_Info_UEnum_EAuracronMemoryManagementStrategy.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronMemoryManagementStrategy>()
{
	return EAuracronMemoryManagementStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EAuracronMemoryManagementStrategy::Aggressive" },
		{ "Balanced.DisplayName", "Balanced" },
		{ "Balanced.Name", "EAuracronMemoryManagementStrategy::Balanced" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory management strategies\n" },
#endif
		{ "Conservative.DisplayName", "Conservative" },
		{ "Conservative.Name", "EAuracronMemoryManagementStrategy::Conservative" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronMemoryManagementStrategy::Custom" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory management strategies" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronMemoryManagementStrategy::Conservative", (int64)EAuracronMemoryManagementStrategy::Conservative },
		{ "EAuracronMemoryManagementStrategy::Balanced", (int64)EAuracronMemoryManagementStrategy::Balanced },
		{ "EAuracronMemoryManagementStrategy::Aggressive", (int64)EAuracronMemoryManagementStrategy::Aggressive },
		{ "EAuracronMemoryManagementStrategy::Custom", (int64)EAuracronMemoryManagementStrategy::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronMemoryManagementStrategy",
	"EAuracronMemoryManagementStrategy",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy()
{
	if (!Z_Registration_Info_UEnum_EAuracronMemoryManagementStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronMemoryManagementStrategy.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronMemoryManagementStrategy.InnerSingleton;
}
// ********** End Enum EAuracronMemoryManagementStrategy *******************************************

// ********** Begin ScriptStruct FAuracronStreamingConfiguration ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronStreamingConfiguration;
class UScriptStruct* FAuracronStreamingConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronStreamingConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronStreamingConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronStreamingConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Streaming Configuration\n * Configuration settings for world partition streaming system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming Configuration\nConfiguration settings for world partition streaming system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableStreaming_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDistanceBasedStreaming_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnloadingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreloadDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentStreamingRequests_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCellsPerFrame_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingTimeSliceMs_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryStrategy_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 16ms per frame\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "16ms per frame" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryPressureThreshold_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePrioritySystem_MetaData[] = {
		{ "Category", "Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 80%\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "80%" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PriorityUpdateInterval_MetaData[] = {
		{ "Category", "Priority" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncStreaming_MetaData[] = {
		{ "Category", "Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 1 second\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "1 second" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AsyncWorkerThreads_MetaData[] = {
		{ "Category", "Async" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableStreamingDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogStreamingOperations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableStreaming;
	static void NewProp_bEnableDistanceBasedStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDistanceBasedStreaming;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnloadingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PreloadDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentStreamingRequests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCellsPerFrame;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingTimeSliceMs;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MemoryStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MemoryStrategy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryPressureThreshold;
	static void NewProp_bEnablePrioritySystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePrioritySystem;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PriorityUpdateInterval;
	static void NewProp_bEnableAsyncStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncStreaming;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AsyncWorkerThreads;
	static void NewProp_bEnableStreamingDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableStreamingDebug;
	static void NewProp_bLogStreamingOperations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogStreamingOperations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronStreamingConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableStreaming_SetBit(void* Obj)
{
	((FAuracronStreamingConfiguration*)Obj)->bEnableStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableStreaming = { "bEnableStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableStreaming_MetaData), NewProp_bEnableStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableDistanceBasedStreaming_SetBit(void* Obj)
{
	((FAuracronStreamingConfiguration*)Obj)->bEnableDistanceBasedStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableDistanceBasedStreaming = { "bEnableDistanceBasedStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableDistanceBasedStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDistanceBasedStreaming_MetaData), NewProp_bEnableDistanceBasedStreaming_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_StreamingDistance = { "StreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, StreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingDistance_MetaData), NewProp_StreamingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_UnloadingDistance = { "UnloadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, UnloadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnloadingDistance_MetaData), NewProp_UnloadingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_PreloadDistance = { "PreloadDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, PreloadDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreloadDistance_MetaData), NewProp_PreloadDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MaxConcurrentStreamingRequests = { "MaxConcurrentStreamingRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, MaxConcurrentStreamingRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentStreamingRequests_MetaData), NewProp_MaxConcurrentStreamingRequests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MaxCellsPerFrame = { "MaxCellsPerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, MaxCellsPerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCellsPerFrame_MetaData), NewProp_MaxCellsPerFrame_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_StreamingTimeSliceMs = { "StreamingTimeSliceMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, StreamingTimeSliceMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingTimeSliceMs_MetaData), NewProp_StreamingTimeSliceMs_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MemoryStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MemoryStrategy = { "MemoryStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, MemoryStrategy), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronMemoryManagementStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryStrategy_MetaData), NewProp_MemoryStrategy_MetaData) }; // 1899572570
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MaxMemoryUsageMB = { "MaxMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, MaxMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMemoryUsageMB_MetaData), NewProp_MaxMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MemoryPressureThreshold = { "MemoryPressureThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, MemoryPressureThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryPressureThreshold_MetaData), NewProp_MemoryPressureThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnablePrioritySystem_SetBit(void* Obj)
{
	((FAuracronStreamingConfiguration*)Obj)->bEnablePrioritySystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnablePrioritySystem = { "bEnablePrioritySystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnablePrioritySystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePrioritySystem_MetaData), NewProp_bEnablePrioritySystem_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_PriorityUpdateInterval = { "PriorityUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, PriorityUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PriorityUpdateInterval_MetaData), NewProp_PriorityUpdateInterval_MetaData) };
void Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableAsyncStreaming_SetBit(void* Obj)
{
	((FAuracronStreamingConfiguration*)Obj)->bEnableAsyncStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableAsyncStreaming = { "bEnableAsyncStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableAsyncStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncStreaming_MetaData), NewProp_bEnableAsyncStreaming_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_AsyncWorkerThreads = { "AsyncWorkerThreads", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingConfiguration, AsyncWorkerThreads), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AsyncWorkerThreads_MetaData), NewProp_AsyncWorkerThreads_MetaData) };
void Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableStreamingDebug_SetBit(void* Obj)
{
	((FAuracronStreamingConfiguration*)Obj)->bEnableStreamingDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableStreamingDebug = { "bEnableStreamingDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableStreamingDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableStreamingDebug_MetaData), NewProp_bEnableStreamingDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bLogStreamingOperations_SetBit(void* Obj)
{
	((FAuracronStreamingConfiguration*)Obj)->bLogStreamingOperations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bLogStreamingOperations = { "bLogStreamingOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStreamingConfiguration), &Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bLogStreamingOperations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogStreamingOperations_MetaData), NewProp_bLogStreamingOperations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableDistanceBasedStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_StreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_UnloadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_PreloadDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MaxConcurrentStreamingRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MaxCellsPerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_StreamingTimeSliceMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MemoryStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MemoryStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MaxMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_MemoryPressureThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnablePrioritySystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_PriorityUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableAsyncStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_AsyncWorkerThreads,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bEnableStreamingDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewProp_bLogStreamingOperations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronStreamingConfiguration",
	Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::PropPointers),
	sizeof(FAuracronStreamingConfiguration),
	alignof(FAuracronStreamingConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronStreamingConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronStreamingConfiguration *************************************

// ********** Begin ScriptStruct FAuracronStreamingRequest *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronStreamingRequest;
class UScriptStruct* FAuracronStreamingRequest::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingRequest.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronStreamingRequest.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronStreamingRequest, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronStreamingRequest"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingRequest.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Streaming Request\n * Represents a single streaming request\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming Request\nRepresents a single streaming request" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestId_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestType_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingSourceLocation_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceFromSource_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestTime_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingTime_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletionTime_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequestId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RequestType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RequestType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingSourceLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceFromSource;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RequestTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProcessingTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronStreamingRequest>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_RequestId = { "RequestId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, RequestId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestId_MetaData), NewProp_RequestId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_RequestType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_RequestType = { "RequestType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, RequestType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestType_MetaData), NewProp_RequestType_MetaData) }; // 386087251
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, Priority), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 1066228594
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, State), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 2680542013
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_StreamingSourceLocation = { "StreamingSourceLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, StreamingSourceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingSourceLocation_MetaData), NewProp_StreamingSourceLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_DistanceFromSource = { "DistanceFromSource", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, DistanceFromSource), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceFromSource_MetaData), NewProp_DistanceFromSource_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_RequestTime = { "RequestTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, RequestTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestTime_MetaData), NewProp_RequestTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_ProcessingTime = { "ProcessingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, ProcessingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingTime_MetaData), NewProp_ProcessingTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_CompletionTime = { "CompletionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingRequest, CompletionTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletionTime_MetaData), NewProp_CompletionTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_RequestId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_RequestType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_RequestType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_StreamingSourceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_DistanceFromSource,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_RequestTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_ProcessingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewProp_CompletionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronStreamingRequest",
	Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::PropPointers),
	sizeof(FAuracronStreamingRequest),
	alignof(FAuracronStreamingRequest),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingRequest()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingRequest.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronStreamingRequest.InnerSingleton, Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingRequest.InnerSingleton;
}
// ********** End ScriptStruct FAuracronStreamingRequest *******************************************

// ********** Begin ScriptStruct FAuracronStreamingSource ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronStreamingSource;
class UScriptStruct* FAuracronStreamingSource::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingSource.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronStreamingSource.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronStreamingSource, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronStreamingSource"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingSource.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Streaming Source\n * Represents a source for streaming operations (player, camera, etc.)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming Source\nRepresents a source for streaming operations (player, camera, etc.)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceId_MetaData[] = {
		{ "Category", "Source" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Source" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Velocity_MetaData[] = {
		{ "Category", "Source" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingRadius_MetaData[] = {
		{ "Category", "Source" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Source" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Source" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePrediction_MetaData[] = {
		{ "Category", "Source" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictionTime_MetaData[] = {
		{ "Category", "Source" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Source" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 2 seconds ahead\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "2 seconds ahead" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Velocity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Priority;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bEnablePrediction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePrediction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PredictionTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronStreamingSource>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_SourceId = { "SourceId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingSource, SourceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceId_MetaData), NewProp_SourceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingSource, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_Velocity = { "Velocity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingSource, Velocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Velocity_MetaData), NewProp_Velocity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_StreamingRadius = { "StreamingRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingSource, StreamingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingRadius_MetaData), NewProp_StreamingRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingSource, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
void Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronStreamingSource*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStreamingSource), &Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_bEnablePrediction_SetBit(void* Obj)
{
	((FAuracronStreamingSource*)Obj)->bEnablePrediction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_bEnablePrediction = { "bEnablePrediction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStreamingSource), &Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_bEnablePrediction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePrediction_MetaData), NewProp_bEnablePrediction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_PredictionTime = { "PredictionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingSource, PredictionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictionTime_MetaData), NewProp_PredictionTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingSource, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_SourceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_Velocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_StreamingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_bEnablePrediction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_PredictionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronStreamingSource",
	Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::PropPointers),
	sizeof(FAuracronStreamingSource),
	alignof(FAuracronStreamingSource),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingSource()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingSource.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronStreamingSource.InnerSingleton, Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingSource.InnerSingleton;
}
// ********** End ScriptStruct FAuracronStreamingSource ********************************************

// ********** Begin ScriptStruct FAuracronStreamingBridgeStatistics ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronStreamingBridgeStatistics;
class UScriptStruct* FAuracronStreamingBridgeStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingBridgeStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronStreamingBridgeStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronStreamingBridgeStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingBridgeStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Streaming Statistics\n * Performance and usage statistics for streaming system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming Statistics\nPerformance and usage statistics for streaming system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalStreamingRequests_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedRequests_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedRequests_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PendingRequests_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadingTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageUnloadingTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryPressure_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingEfficiency_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 0.0 to 1.0\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "0.0 to 1.0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellsLoaded_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Success rate\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Success rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellsUnloaded_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalStreamingRequests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompletedRequests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedRequests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PendingRequests;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadingTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageUnloadingTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PeakMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryPressure;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingEfficiency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CellsLoaded;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CellsUnloaded;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronStreamingBridgeStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_TotalStreamingRequests = { "TotalStreamingRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, TotalStreamingRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalStreamingRequests_MetaData), NewProp_TotalStreamingRequests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_CompletedRequests = { "CompletedRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, CompletedRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedRequests_MetaData), NewProp_CompletedRequests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_FailedRequests = { "FailedRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, FailedRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedRequests_MetaData), NewProp_FailedRequests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_PendingRequests = { "PendingRequests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, PendingRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PendingRequests_MetaData), NewProp_PendingRequests_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_AverageLoadingTime = { "AverageLoadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, AverageLoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadingTime_MetaData), NewProp_AverageLoadingTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_AverageUnloadingTime = { "AverageUnloadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, AverageUnloadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageUnloadingTime_MetaData), NewProp_AverageUnloadingTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_CurrentMemoryUsageMB = { "CurrentMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, CurrentMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMemoryUsageMB_MetaData), NewProp_CurrentMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_PeakMemoryUsageMB = { "PeakMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, PeakMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakMemoryUsageMB_MetaData), NewProp_PeakMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_MemoryPressure = { "MemoryPressure", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, MemoryPressure), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryPressure_MetaData), NewProp_MemoryPressure_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_StreamingEfficiency = { "StreamingEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, StreamingEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingEfficiency_MetaData), NewProp_StreamingEfficiency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_CellsLoaded = { "CellsLoaded", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, CellsLoaded), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellsLoaded_MetaData), NewProp_CellsLoaded_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_CellsUnloaded = { "CellsUnloaded", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, CellsUnloaded), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellsUnloaded_MetaData), NewProp_CellsUnloaded_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingBridgeStatistics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_TotalStreamingRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_CompletedRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_FailedRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_PendingRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_AverageLoadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_AverageUnloadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_CurrentMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_PeakMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_MemoryPressure,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_StreamingEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_CellsLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_CellsUnloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronStreamingBridgeStatistics",
	Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::PropPointers),
	sizeof(FAuracronStreamingBridgeStatistics),
	alignof(FAuracronStreamingBridgeStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingBridgeStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronStreamingBridgeStatistics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingBridgeStatistics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronStreamingBridgeStatistics **********************************

// ********** Begin Delegate FOnCellStreamingStarted ***********************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventOnCellStreamingStarted_Parms
	{
		FString CellId;
		EAuracronStreamingRequestType RequestType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RequestType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RequestType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventOnCellStreamingStarted_Parms, CellId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::NewProp_RequestType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::NewProp_RequestType = { "RequestType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventOnCellStreamingStarted_Parms, RequestType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType, METADATA_PARAMS(0, nullptr) }; // 386087251
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::NewProp_RequestType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::NewProp_RequestType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "OnCellStreamingStarted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::AuracronWorldPartitionStreamingManager_eventOnCellStreamingStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::AuracronWorldPartitionStreamingManager_eventOnCellStreamingStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionStreamingManager::FOnCellStreamingStarted_DelegateWrapper(const FMulticastScriptDelegate& OnCellStreamingStarted, const FString& CellId, EAuracronStreamingRequestType RequestType)
{
	struct AuracronWorldPartitionStreamingManager_eventOnCellStreamingStarted_Parms
	{
		FString CellId;
		EAuracronStreamingRequestType RequestType;
	};
	AuracronWorldPartitionStreamingManager_eventOnCellStreamingStarted_Parms Parms;
	Parms.CellId=CellId;
	Parms.RequestType=RequestType;
	OnCellStreamingStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCellStreamingStarted *************************************************

// ********** Begin Delegate FOnCellStreamingCompleted *********************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventOnCellStreamingCompleted_Parms
	{
		FString CellId;
		EAuracronStreamingRequestType RequestType;
		float ProcessingTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RequestType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RequestType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProcessingTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventOnCellStreamingCompleted_Parms, CellId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::NewProp_RequestType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::NewProp_RequestType = { "RequestType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventOnCellStreamingCompleted_Parms, RequestType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType, METADATA_PARAMS(0, nullptr) }; // 386087251
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::NewProp_ProcessingTime = { "ProcessingTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventOnCellStreamingCompleted_Parms, ProcessingTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::NewProp_RequestType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::NewProp_RequestType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::NewProp_ProcessingTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "OnCellStreamingCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::AuracronWorldPartitionStreamingManager_eventOnCellStreamingCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::AuracronWorldPartitionStreamingManager_eventOnCellStreamingCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionStreamingManager::FOnCellStreamingCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnCellStreamingCompleted, const FString& CellId, EAuracronStreamingRequestType RequestType, float ProcessingTime)
{
	struct AuracronWorldPartitionStreamingManager_eventOnCellStreamingCompleted_Parms
	{
		FString CellId;
		EAuracronStreamingRequestType RequestType;
		float ProcessingTime;
	};
	AuracronWorldPartitionStreamingManager_eventOnCellStreamingCompleted_Parms Parms;
	Parms.CellId=CellId;
	Parms.RequestType=RequestType;
	Parms.ProcessingTime=ProcessingTime;
	OnCellStreamingCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCellStreamingCompleted ***********************************************

// ********** Begin Delegate FOnCellStreamingFailed ************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventOnCellStreamingFailed_Parms
	{
		FString CellId;
		EAuracronStreamingRequestType RequestType;
		FString ErrorMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RequestType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RequestType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventOnCellStreamingFailed_Parms, CellId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::NewProp_RequestType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::NewProp_RequestType = { "RequestType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventOnCellStreamingFailed_Parms, RequestType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingRequestType, METADATA_PARAMS(0, nullptr) }; // 386087251
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventOnCellStreamingFailed_Parms, ErrorMessage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::NewProp_RequestType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::NewProp_RequestType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "OnCellStreamingFailed__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::AuracronWorldPartitionStreamingManager_eventOnCellStreamingFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::AuracronWorldPartitionStreamingManager_eventOnCellStreamingFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionStreamingManager::FOnCellStreamingFailed_DelegateWrapper(const FMulticastScriptDelegate& OnCellStreamingFailed, const FString& CellId, EAuracronStreamingRequestType RequestType, const FString& ErrorMessage)
{
	struct AuracronWorldPartitionStreamingManager_eventOnCellStreamingFailed_Parms
	{
		FString CellId;
		EAuracronStreamingRequestType RequestType;
		FString ErrorMessage;
	};
	AuracronWorldPartitionStreamingManager_eventOnCellStreamingFailed_Parms Parms;
	Parms.CellId=CellId;
	Parms.RequestType=RequestType;
	Parms.ErrorMessage=ErrorMessage;
	OnCellStreamingFailed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCellStreamingFailed **************************************************

// ********** Begin Delegate FOnMemoryPressureChanged **********************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventOnMemoryPressureChanged_Parms
	{
		float MemoryPressure;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryPressure;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::NewProp_MemoryPressure = { "MemoryPressure", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventOnMemoryPressureChanged_Parms, MemoryPressure), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::NewProp_MemoryPressure,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "OnMemoryPressureChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::AuracronWorldPartitionStreamingManager_eventOnMemoryPressureChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::AuracronWorldPartitionStreamingManager_eventOnMemoryPressureChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionStreamingManager::FOnMemoryPressureChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMemoryPressureChanged, float MemoryPressure)
{
	struct AuracronWorldPartitionStreamingManager_eventOnMemoryPressureChanged_Parms
	{
		float MemoryPressure;
	};
	AuracronWorldPartitionStreamingManager_eventOnMemoryPressureChanged_Parms Parms;
	Parms.MemoryPressure=MemoryPressure;
	OnMemoryPressureChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMemoryPressureChanged ************************************************

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function AddStreamingSource ******
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventAddStreamingSource_Parms
	{
		FAuracronStreamingSource StreamingSource;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming sources management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming sources management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingSource_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingSource;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::NewProp_StreamingSource = { "StreamingSource", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventAddStreamingSource_Parms, StreamingSource), Z_Construct_UScriptStruct_FAuracronStreamingSource, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingSource_MetaData), NewProp_StreamingSource_MetaData) }; // 4033651170
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventAddStreamingSource_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::NewProp_StreamingSource,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "AddStreamingSource", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::AuracronWorldPartitionStreamingManager_eventAddStreamingSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::AuracronWorldPartitionStreamingManager_eventAddStreamingSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execAddStreamingSource)
{
	P_GET_STRUCT_REF(FAuracronStreamingSource,Z_Param_Out_StreamingSource);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->AddStreamingSource(Z_Param_Out_StreamingSource);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function AddStreamingSource ********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function CalculateCellPriority ***
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventCalculateCellPriority_Parms
	{
		FString CellId;
		FVector SourceLocation;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventCalculateCellPriority_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::NewProp_SourceLocation = { "SourceLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventCalculateCellPriority_Parms, SourceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceLocation_MetaData), NewProp_SourceLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventCalculateCellPriority_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::NewProp_SourceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "CalculateCellPriority", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::AuracronWorldPartitionStreamingManager_eventCalculateCellPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::AuracronWorldPartitionStreamingManager_eventCalculateCellPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execCalculateCellPriority)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_SourceLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateCellPriority(Z_Param_CellId,Z_Param_Out_SourceLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function CalculateCellPriority *****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function CancelStreamingRequest **
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventCancelStreamingRequest_Parms
	{
		FString RequestId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequestId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::NewProp_RequestId = { "RequestId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventCancelStreamingRequest_Parms, RequestId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestId_MetaData), NewProp_RequestId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionStreamingManager_eventCancelStreamingRequest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionStreamingManager_eventCancelStreamingRequest_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::NewProp_RequestId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "CancelStreamingRequest", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::AuracronWorldPartitionStreamingManager_eventCancelStreamingRequest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::AuracronWorldPartitionStreamingManager_eventCancelStreamingRequest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execCancelStreamingRequest)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RequestId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelStreamingRequest(Z_Param_RequestId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function CancelStreamingRequest ****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function ClearCompletedRequests **
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ClearCompletedRequests_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ClearCompletedRequests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "ClearCompletedRequests", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ClearCompletedRequests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ClearCompletedRequests_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ClearCompletedRequests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ClearCompletedRequests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execClearCompletedRequests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearCompletedRequests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function ClearCompletedRequests ****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function DrawDebugStreamingInfo **
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventDrawDebugStreamingInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventDrawDebugStreamingInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "DrawDebugStreamingInfo", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::AuracronWorldPartitionStreamingManager_eventDrawDebugStreamingInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::AuracronWorldPartitionStreamingManager_eventDrawDebugStreamingInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execDrawDebugStreamingInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugStreamingInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function DrawDebugStreamingInfo ****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function EnableStreamingDebug ****
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventEnableStreamingDebug_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionStreamingManager_eventEnableStreamingDebug_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionStreamingManager_eventEnableStreamingDebug_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "EnableStreamingDebug", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::AuracronWorldPartitionStreamingManager_eventEnableStreamingDebug_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::AuracronWorldPartitionStreamingManager_eventEnableStreamingDebug_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execEnableStreamingDebug)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableStreamingDebug(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function EnableStreamingDebug ******

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function ForceMemoryCleanup ******
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ForceMemoryCleanup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ForceMemoryCleanup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "ForceMemoryCleanup", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ForceMemoryCleanup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ForceMemoryCleanup_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ForceMemoryCleanup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ForceMemoryCleanup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execForceMemoryCleanup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceMemoryCleanup();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function ForceMemoryCleanup ********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetActiveRequestCount ***
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetActiveRequestCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetActiveRequestCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetActiveRequestCount", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::AuracronWorldPartitionStreamingManager_eventGetActiveRequestCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::AuracronWorldPartitionStreamingManager_eventGetActiveRequestCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetActiveRequestCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveRequestCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetActiveRequestCount *****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetActiveRequests *******
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetActiveRequests_Parms
	{
		TArray<FAuracronStreamingRequest> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronStreamingRequest, METADATA_PARAMS(0, nullptr) }; // 1784376675
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetActiveRequests_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1784376675
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetActiveRequests", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::AuracronWorldPartitionStreamingManager_eventGetActiveRequests_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::AuracronWorldPartitionStreamingManager_eventGetActiveRequests_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetActiveRequests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronStreamingRequest>*)Z_Param__Result=P_THIS->GetActiveRequests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetActiveRequests *********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetActiveStreamingSources 
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetActiveStreamingSources_Parms
	{
		TArray<FAuracronStreamingSource> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronStreamingSource, METADATA_PARAMS(0, nullptr) }; // 4033651170
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetActiveStreamingSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4033651170
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetActiveStreamingSources", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::AuracronWorldPartitionStreamingManager_eventGetActiveStreamingSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::AuracronWorldPartitionStreamingManager_eventGetActiveStreamingSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetActiveStreamingSources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronStreamingSource>*)Z_Param__Result=P_THIS->GetActiveStreamingSources();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetActiveStreamingSources *

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetCellsInStreamingRange 
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetCellsInStreamingRange_Parms
	{
		FVector Location;
		float Radius;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetCellsInStreamingRange_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetCellsInStreamingRange_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetCellsInStreamingRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetCellsInStreamingRange", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::AuracronWorldPartitionStreamingManager_eventGetCellsInStreamingRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::AuracronWorldPartitionStreamingManager_eventGetCellsInStreamingRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetCellsInStreamingRange)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetCellsInStreamingRange(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetCellsInStreamingRange **

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetConfiguration ********
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetConfiguration_Parms
	{
		FAuracronStreamingConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronStreamingConfiguration, METADATA_PARAMS(0, nullptr) }; // 2225828921
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::AuracronWorldPartitionStreamingManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::AuracronWorldPartitionStreamingManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronStreamingConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetConfiguration **********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetCurrentMemoryUsage ***
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetCurrentMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetCurrentMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetCurrentMemoryUsage", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::AuracronWorldPartitionStreamingManager_eventGetCurrentMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::AuracronWorldPartitionStreamingManager_eventGetCurrentMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetCurrentMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetCurrentMemoryUsage *****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetInstance *************
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionStreamingManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::AuracronWorldPartitionStreamingManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::AuracronWorldPartitionStreamingManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionStreamingManager**)Z_Param__Result=UAuracronWorldPartitionStreamingManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetInstance ***************

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetMemoryPressure *******
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetMemoryPressure_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetMemoryPressure_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetMemoryPressure", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::AuracronWorldPartitionStreamingManager_eventGetMemoryPressure_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::AuracronWorldPartitionStreamingManager_eventGetMemoryPressure_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetMemoryPressure)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMemoryPressure();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetMemoryPressure *********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetPendingRequestCount **
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetPendingRequestCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetPendingRequestCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetPendingRequestCount", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::AuracronWorldPartitionStreamingManager_eventGetPendingRequestCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::AuracronWorldPartitionStreamingManager_eventGetPendingRequestCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetPendingRequestCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPendingRequestCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetPendingRequestCount ****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetPendingRequests ******
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetPendingRequests_Parms
	{
		TArray<FAuracronStreamingRequest> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronStreamingRequest, METADATA_PARAMS(0, nullptr) }; // 1784376675
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetPendingRequests_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1784376675
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetPendingRequests", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::AuracronWorldPartitionStreamingManager_eventGetPendingRequests_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::AuracronWorldPartitionStreamingManager_eventGetPendingRequests_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetPendingRequests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronStreamingRequest>*)Z_Param__Result=P_THIS->GetPendingRequests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetPendingRequests ********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetRequestsByPriority ***
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetRequestsByPriority_Parms
	{
		EAuracronStreamingPriority Priority;
		TArray<FAuracronStreamingRequest> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetRequestsByPriority_Parms, Priority), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority, METADATA_PARAMS(0, nullptr) }; // 1066228594
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronStreamingRequest, METADATA_PARAMS(0, nullptr) }; // 1784376675
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetRequestsByPriority_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1784376675
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetRequestsByPriority", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::AuracronWorldPartitionStreamingManager_eventGetRequestsByPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::AuracronWorldPartitionStreamingManager_eventGetRequestsByPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetRequestsByPriority)
{
	P_GET_ENUM(EAuracronStreamingPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronStreamingRequest>*)Z_Param__Result=P_THIS->GetRequestsByPriority(EAuracronStreamingPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetRequestsByPriority *****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetStreamingDistance ****
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetStreamingDistance_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetStreamingDistance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetStreamingDistance", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::AuracronWorldPartitionStreamingManager_eventGetStreamingDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::AuracronWorldPartitionStreamingManager_eventGetStreamingDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetStreamingDistance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetStreamingDistance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetStreamingDistance ******

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetStreamingEfficiency **
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetStreamingEfficiency_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetStreamingEfficiency_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetStreamingEfficiency", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::AuracronWorldPartitionStreamingManager_eventGetStreamingEfficiency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::AuracronWorldPartitionStreamingManager_eventGetStreamingEfficiency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetStreamingEfficiency)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetStreamingEfficiency();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetStreamingEfficiency ****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetStreamingRequest *****
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetStreamingRequest_Parms
	{
		FString RequestId;
		FAuracronStreamingRequest ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Request management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Request management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequestId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::NewProp_RequestId = { "RequestId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetStreamingRequest_Parms, RequestId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestId_MetaData), NewProp_RequestId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetStreamingRequest_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronStreamingRequest, METADATA_PARAMS(0, nullptr) }; // 1784376675
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::NewProp_RequestId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetStreamingRequest", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::AuracronWorldPartitionStreamingManager_eventGetStreamingRequest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::AuracronWorldPartitionStreamingManager_eventGetStreamingRequest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetStreamingRequest)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RequestId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronStreamingRequest*)Z_Param__Result=P_THIS->GetStreamingRequest(Z_Param_RequestId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetStreamingRequest *******

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function GetStreamingStatistics **
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventGetStreamingStatistics_Parms
	{
		FAuracronStreamingBridgeStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventGetStreamingStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics, METADATA_PARAMS(0, nullptr) }; // 1894954096
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "GetStreamingStatistics", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::AuracronWorldPartitionStreamingManager_eventGetStreamingStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::AuracronWorldPartitionStreamingManager_eventGetStreamingStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execGetStreamingStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronStreamingBridgeStatistics*)Z_Param__Result=P_THIS->GetStreamingStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function GetStreamingStatistics ****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function Initialize **************
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventInitialize_Parms
	{
		FAuracronStreamingConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronStreamingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2225828921
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::AuracronWorldPartitionStreamingManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::AuracronWorldPartitionStreamingManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronStreamingConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function Initialize ****************

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function IsInitialized ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionStreamingManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionStreamingManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::AuracronWorldPartitionStreamingManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::AuracronWorldPartitionStreamingManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function IsInitialized *************

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function IsMemoryPressureHigh ****
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventIsMemoryPressureHigh_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionStreamingManager_eventIsMemoryPressureHigh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionStreamingManager_eventIsMemoryPressureHigh_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "IsMemoryPressureHigh", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::AuracronWorldPartitionStreamingManager_eventIsMemoryPressureHigh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::AuracronWorldPartitionStreamingManager_eventIsMemoryPressureHigh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execIsMemoryPressureHigh)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsMemoryPressureHigh();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function IsMemoryPressureHigh ******

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function IsStreamingDebugEnabled *
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventIsStreamingDebugEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionStreamingManager_eventIsStreamingDebugEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionStreamingManager_eventIsStreamingDebugEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "IsStreamingDebugEnabled", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::AuracronWorldPartitionStreamingManager_eventIsStreamingDebugEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::AuracronWorldPartitionStreamingManager_eventIsStreamingDebugEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execIsStreamingDebugEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsStreamingDebugEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function IsStreamingDebugEnabled ***

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function LogStreamingState *******
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_LogStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_LogStreamingState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "LogStreamingState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_LogStreamingState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_LogStreamingState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_LogStreamingState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_LogStreamingState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execLogStreamingState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogStreamingState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function LogStreamingState *********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function ProcessStreamingRequests 
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ProcessStreamingRequests_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ProcessStreamingRequests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "ProcessStreamingRequests", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ProcessStreamingRequests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ProcessStreamingRequests_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ProcessStreamingRequests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ProcessStreamingRequests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execProcessStreamingRequests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ProcessStreamingRequests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function ProcessStreamingRequests **

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function RemoveStreamingSource ***
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventRemoveStreamingSource_Parms
	{
		FString SourceId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::NewProp_SourceId = { "SourceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventRemoveStreamingSource_Parms, SourceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceId_MetaData), NewProp_SourceId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionStreamingManager_eventRemoveStreamingSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionStreamingManager_eventRemoveStreamingSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::NewProp_SourceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "RemoveStreamingSource", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::AuracronWorldPartitionStreamingManager_eventRemoveStreamingSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::AuracronWorldPartitionStreamingManager_eventRemoveStreamingSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execRemoveStreamingSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SourceId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveStreamingSource(Z_Param_SourceId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function RemoveStreamingSource *****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function RequestCellLoading ******
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventRequestCellLoading_Parms
	{
		FString CellId;
		EAuracronStreamingPriority Priority;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming operations\n" },
#endif
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventRequestCellLoading_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventRequestCellLoading_Parms, Priority), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority, METADATA_PARAMS(0, nullptr) }; // 1066228594
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventRequestCellLoading_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "RequestCellLoading", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::AuracronWorldPartitionStreamingManager_eventRequestCellLoading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::AuracronWorldPartitionStreamingManager_eventRequestCellLoading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execRequestCellLoading)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_GET_ENUM(EAuracronStreamingPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->RequestCellLoading(Z_Param_CellId,EAuracronStreamingPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function RequestCellLoading ********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function RequestCellPreloading ***
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventRequestCellPreloading_Parms
	{
		FString CellId;
		EAuracronStreamingPriority Priority;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "CPP_Default_Priority", "Low" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventRequestCellPreloading_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventRequestCellPreloading_Parms, Priority), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority, METADATA_PARAMS(0, nullptr) }; // 1066228594
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventRequestCellPreloading_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "RequestCellPreloading", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::AuracronWorldPartitionStreamingManager_eventRequestCellPreloading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::AuracronWorldPartitionStreamingManager_eventRequestCellPreloading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execRequestCellPreloading)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_GET_ENUM(EAuracronStreamingPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->RequestCellPreloading(Z_Param_CellId,EAuracronStreamingPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function RequestCellPreloading *****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function RequestCellUnloading ****
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventRequestCellUnloading_Parms
	{
		FString CellId;
		EAuracronStreamingPriority Priority;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventRequestCellUnloading_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventRequestCellUnloading_Parms, Priority), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority, METADATA_PARAMS(0, nullptr) }; // 1066228594
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventRequestCellUnloading_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "RequestCellUnloading", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::AuracronWorldPartitionStreamingManager_eventRequestCellUnloading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::AuracronWorldPartitionStreamingManager_eventRequestCellUnloading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execRequestCellUnloading)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_GET_ENUM(EAuracronStreamingPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->RequestCellUnloading(Z_Param_CellId,EAuracronStreamingPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function RequestCellUnloading ******

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function ResetStatistics *********
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function ResetStatistics ***********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function SetConfiguration ********
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventSetConfiguration_Parms
	{
		FAuracronStreamingConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronStreamingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2225828921
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::AuracronWorldPartitionStreamingManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::AuracronWorldPartitionStreamingManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronStreamingConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function SetConfiguration **********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function SetRequestPriority ******
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventSetRequestPriority_Parms
	{
		FString RequestId;
		EAuracronStreamingPriority Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequestId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::NewProp_RequestId = { "RequestId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventSetRequestPriority_Parms, RequestId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestId_MetaData), NewProp_RequestId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventSetRequestPriority_Parms, Priority), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronStreamingPriority, METADATA_PARAMS(0, nullptr) }; // 1066228594
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::NewProp_RequestId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "SetRequestPriority", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::AuracronWorldPartitionStreamingManager_eventSetRequestPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::AuracronWorldPartitionStreamingManager_eventSetRequestPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execSetRequestPriority)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RequestId);
	P_GET_ENUM(EAuracronStreamingPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetRequestPriority(Z_Param_RequestId,EAuracronStreamingPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function SetRequestPriority ********

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function SetStreamingDistance ****
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventSetStreamingDistance_Parms
	{
		float Distance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventSetStreamingDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::NewProp_Distance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "SetStreamingDistance", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::AuracronWorldPartitionStreamingManager_eventSetStreamingDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::AuracronWorldPartitionStreamingManager_eventSetStreamingDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execSetStreamingDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetStreamingDistance(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function SetStreamingDistance ******

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function Shutdown ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function Shutdown ******************

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function Tick ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::AuracronWorldPartitionStreamingManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::AuracronWorldPartitionStreamingManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function Tick **********************

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function UpdateDistanceBasedStreaming 
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateDistanceBasedStreaming_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Distance-based streaming\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance-based streaming" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateDistanceBasedStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "UpdateDistanceBasedStreaming", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateDistanceBasedStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateDistanceBasedStreaming_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateDistanceBasedStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateDistanceBasedStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execUpdateDistanceBasedStreaming)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDistanceBasedStreaming();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function UpdateDistanceBasedStreaming 

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function UpdateMemoryManagement **
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateMemoryManagement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateMemoryManagement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "UpdateMemoryManagement", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateMemoryManagement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateMemoryManagement_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateMemoryManagement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateMemoryManagement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execUpdateMemoryManagement)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateMemoryManagement();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function UpdateMemoryManagement ****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function UpdateRequestPriorities *
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateRequestPriorities_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority system\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateRequestPriorities_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "UpdateRequestPriorities", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateRequestPriorities_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateRequestPriorities_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateRequestPriorities()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateRequestPriorities_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execUpdateRequestPriorities)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateRequestPriorities();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function UpdateRequestPriorities ***

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function UpdateStreamingSource ***
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventUpdateStreamingSource_Parms
	{
		FString SourceId;
		FVector Location;
		FVector Velocity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Velocity_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Velocity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::NewProp_SourceId = { "SourceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventUpdateStreamingSource_Parms, SourceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceId_MetaData), NewProp_SourceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventUpdateStreamingSource_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::NewProp_Velocity = { "Velocity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventUpdateStreamingSource_Parms, Velocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Velocity_MetaData), NewProp_Velocity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::NewProp_SourceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::NewProp_Velocity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "UpdateStreamingSource", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::AuracronWorldPartitionStreamingManager_eventUpdateStreamingSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::AuracronWorldPartitionStreamingManager_eventUpdateStreamingSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execUpdateStreamingSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SourceId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Velocity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateStreamingSource(Z_Param_SourceId,Z_Param_Out_Location,Z_Param_Out_Velocity);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function UpdateStreamingSource *****

// ********** Begin Class UAuracronWorldPartitionStreamingManager Function UpdateStreamingSourcesFromWorld 
struct Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics
{
	struct AuracronWorldPartitionStreamingManager_eventUpdateStreamingSourcesFromWorld_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionStreamingManager_eventUpdateStreamingSourcesFromWorld_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, nullptr, "UpdateStreamingSourcesFromWorld", Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::AuracronWorldPartitionStreamingManager_eventUpdateStreamingSourcesFromWorld_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::AuracronWorldPartitionStreamingManager_eventUpdateStreamingSourcesFromWorld_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionStreamingManager::execUpdateStreamingSourcesFromWorld)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateStreamingSourcesFromWorld(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionStreamingManager Function UpdateStreamingSourcesFromWorld 

// ********** Begin Class UAuracronWorldPartitionStreamingManager **********************************
void UAuracronWorldPartitionStreamingManager::StaticRegisterNativesUAuracronWorldPartitionStreamingManager()
{
	UClass* Class = UAuracronWorldPartitionStreamingManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddStreamingSource", &UAuracronWorldPartitionStreamingManager::execAddStreamingSource },
		{ "CalculateCellPriority", &UAuracronWorldPartitionStreamingManager::execCalculateCellPriority },
		{ "CancelStreamingRequest", &UAuracronWorldPartitionStreamingManager::execCancelStreamingRequest },
		{ "ClearCompletedRequests", &UAuracronWorldPartitionStreamingManager::execClearCompletedRequests },
		{ "DrawDebugStreamingInfo", &UAuracronWorldPartitionStreamingManager::execDrawDebugStreamingInfo },
		{ "EnableStreamingDebug", &UAuracronWorldPartitionStreamingManager::execEnableStreamingDebug },
		{ "ForceMemoryCleanup", &UAuracronWorldPartitionStreamingManager::execForceMemoryCleanup },
		{ "GetActiveRequestCount", &UAuracronWorldPartitionStreamingManager::execGetActiveRequestCount },
		{ "GetActiveRequests", &UAuracronWorldPartitionStreamingManager::execGetActiveRequests },
		{ "GetActiveStreamingSources", &UAuracronWorldPartitionStreamingManager::execGetActiveStreamingSources },
		{ "GetCellsInStreamingRange", &UAuracronWorldPartitionStreamingManager::execGetCellsInStreamingRange },
		{ "GetConfiguration", &UAuracronWorldPartitionStreamingManager::execGetConfiguration },
		{ "GetCurrentMemoryUsage", &UAuracronWorldPartitionStreamingManager::execGetCurrentMemoryUsage },
		{ "GetInstance", &UAuracronWorldPartitionStreamingManager::execGetInstance },
		{ "GetMemoryPressure", &UAuracronWorldPartitionStreamingManager::execGetMemoryPressure },
		{ "GetPendingRequestCount", &UAuracronWorldPartitionStreamingManager::execGetPendingRequestCount },
		{ "GetPendingRequests", &UAuracronWorldPartitionStreamingManager::execGetPendingRequests },
		{ "GetRequestsByPriority", &UAuracronWorldPartitionStreamingManager::execGetRequestsByPriority },
		{ "GetStreamingDistance", &UAuracronWorldPartitionStreamingManager::execGetStreamingDistance },
		{ "GetStreamingEfficiency", &UAuracronWorldPartitionStreamingManager::execGetStreamingEfficiency },
		{ "GetStreamingRequest", &UAuracronWorldPartitionStreamingManager::execGetStreamingRequest },
		{ "GetStreamingStatistics", &UAuracronWorldPartitionStreamingManager::execGetStreamingStatistics },
		{ "Initialize", &UAuracronWorldPartitionStreamingManager::execInitialize },
		{ "IsInitialized", &UAuracronWorldPartitionStreamingManager::execIsInitialized },
		{ "IsMemoryPressureHigh", &UAuracronWorldPartitionStreamingManager::execIsMemoryPressureHigh },
		{ "IsStreamingDebugEnabled", &UAuracronWorldPartitionStreamingManager::execIsStreamingDebugEnabled },
		{ "LogStreamingState", &UAuracronWorldPartitionStreamingManager::execLogStreamingState },
		{ "ProcessStreamingRequests", &UAuracronWorldPartitionStreamingManager::execProcessStreamingRequests },
		{ "RemoveStreamingSource", &UAuracronWorldPartitionStreamingManager::execRemoveStreamingSource },
		{ "RequestCellLoading", &UAuracronWorldPartitionStreamingManager::execRequestCellLoading },
		{ "RequestCellPreloading", &UAuracronWorldPartitionStreamingManager::execRequestCellPreloading },
		{ "RequestCellUnloading", &UAuracronWorldPartitionStreamingManager::execRequestCellUnloading },
		{ "ResetStatistics", &UAuracronWorldPartitionStreamingManager::execResetStatistics },
		{ "SetConfiguration", &UAuracronWorldPartitionStreamingManager::execSetConfiguration },
		{ "SetRequestPriority", &UAuracronWorldPartitionStreamingManager::execSetRequestPriority },
		{ "SetStreamingDistance", &UAuracronWorldPartitionStreamingManager::execSetStreamingDistance },
		{ "Shutdown", &UAuracronWorldPartitionStreamingManager::execShutdown },
		{ "Tick", &UAuracronWorldPartitionStreamingManager::execTick },
		{ "UpdateDistanceBasedStreaming", &UAuracronWorldPartitionStreamingManager::execUpdateDistanceBasedStreaming },
		{ "UpdateMemoryManagement", &UAuracronWorldPartitionStreamingManager::execUpdateMemoryManagement },
		{ "UpdateRequestPriorities", &UAuracronWorldPartitionStreamingManager::execUpdateRequestPriorities },
		{ "UpdateStreamingSource", &UAuracronWorldPartitionStreamingManager::execUpdateStreamingSource },
		{ "UpdateStreamingSourcesFromWorld", &UAuracronWorldPartitionStreamingManager::execUpdateStreamingSourcesFromWorld },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionStreamingManager;
UClass* UAuracronWorldPartitionStreamingManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionStreamingManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionStreamingManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionStreamingManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionStreamingManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionStreamingManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionStreamingManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_NoRegister()
{
	return UAuracronWorldPartitionStreamingManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Streaming Manager\n * Central manager for world partition streaming operations\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionStreaming.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Streaming Manager\nCentral manager for world partition streaming operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCellStreamingStarted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCellStreamingCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCellStreamingFailed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMemoryPressureChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionStreaming.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCellStreamingStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCellStreamingCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCellStreamingFailed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMemoryPressureChanged;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_AddStreamingSource, "AddStreamingSource" }, // 3390622565
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CalculateCellPriority, "CalculateCellPriority" }, // 1416151323
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_CancelStreamingRequest, "CancelStreamingRequest" }, // 2293260066
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ClearCompletedRequests, "ClearCompletedRequests" }, // 3490302810
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_DrawDebugStreamingInfo, "DrawDebugStreamingInfo" }, // 3947305075
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_EnableStreamingDebug, "EnableStreamingDebug" }, // 2174045605
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ForceMemoryCleanup, "ForceMemoryCleanup" }, // 1334399864
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequestCount, "GetActiveRequestCount" }, // 448859251
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveRequests, "GetActiveRequests" }, // 59794831
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetActiveStreamingSources, "GetActiveStreamingSources" }, // 1139689547
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCellsInStreamingRange, "GetCellsInStreamingRange" }, // 4157059385
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetConfiguration, "GetConfiguration" }, // 2781855692
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetCurrentMemoryUsage, "GetCurrentMemoryUsage" }, // 2459548832
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetInstance, "GetInstance" }, // 2172036131
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetMemoryPressure, "GetMemoryPressure" }, // 3262169189
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequestCount, "GetPendingRequestCount" }, // 264435640
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetPendingRequests, "GetPendingRequests" }, // 1612139993
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetRequestsByPriority, "GetRequestsByPriority" }, // 1325435886
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingDistance, "GetStreamingDistance" }, // 1241503566
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingEfficiency, "GetStreamingEfficiency" }, // 2680507975
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingRequest, "GetStreamingRequest" }, // 663529506
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_GetStreamingStatistics, "GetStreamingStatistics" }, // 1015961879
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Initialize, "Initialize" }, // 1170015022
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsInitialized, "IsInitialized" }, // 3961224457
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsMemoryPressureHigh, "IsMemoryPressureHigh" }, // 2242887598
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_IsStreamingDebugEnabled, "IsStreamingDebugEnabled" }, // 2161430025
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_LogStreamingState, "LogStreamingState" }, // 2148448701
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature, "OnCellStreamingCompleted__DelegateSignature" }, // 3173997499
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature, "OnCellStreamingFailed__DelegateSignature" }, // 2062962026
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature, "OnCellStreamingStarted__DelegateSignature" }, // 3800102117
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature, "OnMemoryPressureChanged__DelegateSignature" }, // 1738344423
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ProcessStreamingRequests, "ProcessStreamingRequests" }, // 1794782083
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RemoveStreamingSource, "RemoveStreamingSource" }, // 418453055
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellLoading, "RequestCellLoading" }, // 4042874452
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellPreloading, "RequestCellPreloading" }, // 3711902688
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_RequestCellUnloading, "RequestCellUnloading" }, // 3579256450
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_ResetStatistics, "ResetStatistics" }, // 350902629
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetConfiguration, "SetConfiguration" }, // 2454194123
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetRequestPriority, "SetRequestPriority" }, // 3150456056
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_SetStreamingDistance, "SetStreamingDistance" }, // 1444012997
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Shutdown, "Shutdown" }, // 2626194178
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_Tick, "Tick" }, // 387799616
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateDistanceBasedStreaming, "UpdateDistanceBasedStreaming" }, // 3718920596
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateMemoryManagement, "UpdateMemoryManagement" }, // 1397715917
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateRequestPriorities, "UpdateRequestPriorities" }, // 2918636941
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSource, "UpdateStreamingSource" }, // 2677047805
		{ &Z_Construct_UFunction_UAuracronWorldPartitionStreamingManager_UpdateStreamingSourcesFromWorld, "UpdateStreamingSourcesFromWorld" }, // 2455294475
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionStreamingManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_OnCellStreamingStarted = { "OnCellStreamingStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionStreamingManager, OnCellStreamingStarted), Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCellStreamingStarted_MetaData), NewProp_OnCellStreamingStarted_MetaData) }; // 3800102117
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_OnCellStreamingCompleted = { "OnCellStreamingCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionStreamingManager, OnCellStreamingCompleted), Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCellStreamingCompleted_MetaData), NewProp_OnCellStreamingCompleted_MetaData) }; // 3173997499
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_OnCellStreamingFailed = { "OnCellStreamingFailed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionStreamingManager, OnCellStreamingFailed), Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCellStreamingFailed_MetaData), NewProp_OnCellStreamingFailed_MetaData) }; // 2062962026
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_OnMemoryPressureChanged = { "OnMemoryPressureChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionStreamingManager, OnMemoryPressureChanged), Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMemoryPressureChanged_MetaData), NewProp_OnMemoryPressureChanged_MetaData) }; // 1738344423
void Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionStreamingManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionStreamingManager), &Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionStreamingManager, Configuration), Z_Construct_UScriptStruct_FAuracronStreamingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2225828921
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionStreamingManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_OnCellStreamingStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_OnCellStreamingCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_OnCellStreamingFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_OnMemoryPressureChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::ClassParams = {
	&UAuracronWorldPartitionStreamingManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionStreamingManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionStreamingManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionStreamingManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionStreamingManager.OuterSingleton;
}
UAuracronWorldPartitionStreamingManager::UAuracronWorldPartitionStreamingManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionStreamingManager);
UAuracronWorldPartitionStreamingManager::~UAuracronWorldPartitionStreamingManager() {}
// ********** End Class UAuracronWorldPartitionStreamingManager ************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronStreamingPriority_StaticEnum, TEXT("EAuracronStreamingPriority"), &Z_Registration_Info_UEnum_EAuracronStreamingPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1066228594U) },
		{ EAuracronStreamingRequestType_StaticEnum, TEXT("EAuracronStreamingRequestType"), &Z_Registration_Info_UEnum_EAuracronStreamingRequestType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 386087251U) },
		{ EAuracronStreamingRequestState_StaticEnum, TEXT("EAuracronStreamingRequestState"), &Z_Registration_Info_UEnum_EAuracronStreamingRequestState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2680542013U) },
		{ EAuracronMemoryManagementStrategy_StaticEnum, TEXT("EAuracronMemoryManagementStrategy"), &Z_Registration_Info_UEnum_EAuracronMemoryManagementStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1899572570U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronStreamingConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics::NewStructOps, TEXT("AuracronStreamingConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronStreamingConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronStreamingConfiguration), 2225828921U) },
		{ FAuracronStreamingRequest::StaticStruct, Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics::NewStructOps, TEXT("AuracronStreamingRequest"), &Z_Registration_Info_UScriptStruct_FAuracronStreamingRequest, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronStreamingRequest), 1784376675U) },
		{ FAuracronStreamingSource::StaticStruct, Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics::NewStructOps, TEXT("AuracronStreamingSource"), &Z_Registration_Info_UScriptStruct_FAuracronStreamingSource, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronStreamingSource), 4033651170U) },
		{ FAuracronStreamingBridgeStatistics::StaticStruct, Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics::NewStructOps, TEXT("AuracronStreamingBridgeStatistics"), &Z_Registration_Info_UScriptStruct_FAuracronStreamingBridgeStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronStreamingBridgeStatistics), 1894954096U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionStreamingManager, UAuracronWorldPartitionStreamingManager::StaticClass, TEXT("UAuracronWorldPartitionStreamingManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionStreamingManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionStreamingManager), 2787171684U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h__Script_AuracronWorldPartitionBridge_3167239535(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
