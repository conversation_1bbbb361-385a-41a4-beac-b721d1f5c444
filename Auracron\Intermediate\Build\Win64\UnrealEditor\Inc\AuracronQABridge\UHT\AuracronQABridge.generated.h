// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronQABridge.h"

#ifdef AURACRONQABRIDGE_AuracronQABridge_generated_h
#error "AuracronQABridge.generated.h already included, missing '#pragma once' in AuracronQABridge.h"
#endif
#define AURACRONQABRIDGE_AuracronQABridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UObject;
enum class EAuracronQATestType : uint8;
struct FAuracronQAPerformanceData;
struct FAuracronQATestCase;
struct FAuracronQATestExecution;

// ********** Begin ScriptStruct FAuracronQATestCase ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_105_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronQATestCase_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronQATestCase;
// ********** End ScriptStruct FAuracronQATestCase *************************************************

// ********** Begin ScriptStruct FAuracronQATestExecution ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_166_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronQATestExecution;
// ********** End ScriptStruct FAuracronQATestExecution ********************************************

// ********** Begin ScriptStruct FAuracronQAPerformanceData ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_215_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronQAPerformanceData;
// ********** End ScriptStruct FAuracronQAPerformanceData ******************************************

// ********** Begin Class UAuracronQABridge ********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_270_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetQADataForPython); \
	DECLARE_FUNCTION(execExecutePythonTestScript); \
	DECLARE_FUNCTION(execInitializePythonBindings); \
	DECLARE_FUNCTION(execGetQASystemConfiguration); \
	DECLARE_FUNCTION(execImportQATestCasesFromJSON); \
	DECLARE_FUNCTION(execExportQAResultsToJSON); \
	DECLARE_FUNCTION(execTestAssetStreamingPerformance); \
	DECLARE_FUNCTION(execValidateAssetLoadingPerformance); \
	DECLARE_FUNCTION(execStopMemoryTracking); \
	DECLARE_FUNCTION(execStartMemoryTracking); \
	DECLARE_FUNCTION(execProfileChampionPerformance); \
	DECLARE_FUNCTION(execStopPerformanceProfiling); \
	DECLARE_FUNCTION(execStartPerformanceProfiling); \
	DECLARE_FUNCTION(execCaptureChampionRender); \
	DECLARE_FUNCTION(execCompareScreenshots); \
	DECLARE_FUNCTION(execCaptureScreenshot); \
	DECLARE_FUNCTION(execValidateAssetsByPath); \
	DECLARE_FUNCTION(execValidateAssets); \
	DECLARE_FUNCTION(execValidateAsset); \
	DECLARE_FUNCTION(execExecuteTestsByType); \
	DECLARE_FUNCTION(execExecuteTestsByTag); \
	DECLARE_FUNCTION(execExecuteTestSuite); \
	DECLARE_FUNCTION(execExecuteTestCase); \
	DECLARE_FUNCTION(execInitializeAutomationTesting);


AURACRONQABRIDGE_API UClass* Z_Construct_UClass_UAuracronQABridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_270_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronQABridge(); \
	friend struct Z_Construct_UClass_UAuracronQABridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONQABRIDGE_API UClass* Z_Construct_UClass_UAuracronQABridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronQABridge, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronQABridge"), Z_Construct_UClass_UAuracronQABridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronQABridge)


#define FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_270_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronQABridge(UAuracronQABridge&&) = delete; \
	UAuracronQABridge(const UAuracronQABridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronQABridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronQABridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronQABridge) \
	NO_API virtual ~UAuracronQABridge();


#define FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_267_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_270_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_270_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_270_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h_270_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronQABridge;

// ********** End Class UAuracronQABridge **********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h

// ********** Begin Enum EAuracronQATestType *******************************************************
#define FOREACH_ENUM_EAURACRONQATESTTYPE(op) \
	op(EAuracronQATestType::VisualValidation) \
	op(EAuracronQATestType::GameplayTesting) \
	op(EAuracronQATestType::PerformanceTesting) \
	op(EAuracronQATestType::BalanceVerification) \
	op(EAuracronQATestType::AssetValidation) \
	op(EAuracronQATestType::IntegrationTesting) 

enum class EAuracronQATestType : uint8;
template<> struct TIsUEnumClass<EAuracronQATestType> { enum { Value = true }; };
template<> AURACRONQABRIDGE_API UEnum* StaticEnum<EAuracronQATestType>();
// ********** End Enum EAuracronQATestType *********************************************************

// ********** Begin Enum EAuracronQATestResult *****************************************************
#define FOREACH_ENUM_EAURACRONQATESTRESULT(op) \
	op(EAuracronQATestResult::Passed) \
	op(EAuracronQATestResult::Failed) \
	op(EAuracronQATestResult::Warning) \
	op(EAuracronQATestResult::Skipped) \
	op(EAuracronQATestResult::Error) 

enum class EAuracronQATestResult : uint8;
template<> struct TIsUEnumClass<EAuracronQATestResult> { enum { Value = true }; };
template<> AURACRONQABRIDGE_API UEnum* StaticEnum<EAuracronQATestResult>();
// ********** End Enum EAuracronQATestResult *******************************************************

// ********** Begin Enum EAuracronQASeverity *******************************************************
#define FOREACH_ENUM_EAURACRONQASEVERITY(op) \
	op(EAuracronQASeverity::Critical) \
	op(EAuracronQASeverity::High) \
	op(EAuracronQASeverity::Medium) \
	op(EAuracronQASeverity::Low) \
	op(EAuracronQASeverity::Info) 

enum class EAuracronQASeverity : uint8;
template<> struct TIsUEnumClass<EAuracronQASeverity> { enum { Value = true }; };
template<> AURACRONQABRIDGE_API UEnum* StaticEnum<EAuracronQASeverity>();
// ********** End Enum EAuracronQASeverity *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
