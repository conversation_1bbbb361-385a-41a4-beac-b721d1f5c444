using UnrealBuildTool;
public class AuracronLumenBridge : ModuleRules
{
    public AuracronLumenBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(new string[] 
        {
            "Core",
            "CoreUObject",
            "Engine",
            "RenderCore",
            "RHI","DeveloperSettings",
            "PythonScriptPlugin"
        });
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "Slate",
            "SlateCore",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "EngineSettings",
            "Projects","DesktopPlatform",
            "ApplicationCore",
            "AppFramework",
            "MainFrame",
            "SourceControl",
            "SourceControlWindows",
            "ToolWidgets",
            "WorkspaceMenuStructure",
            "AssetTools",
            "AssetRegistry",
            "ContentBrowser",
            "ContentBrowserData",
            "EditorSubsystem",
            "GameplayTags",
            "GameplayTasks",
            "GameplayAbilities",
            "MaterialEditor",
            "MaterialUtilities",
            "MaterialBaking",
            "MeshUtilities",
            "MeshUtilitiesCommon",
            "MeshDescription",
            "StaticMeshDescription",
            "MeshConversion",
            "GeometryProcessingInterfaces",
            "DynamicMesh",
            "GeometryCore",
            "InteractiveToolsFramework",
            "EditorInteractiveToolsFramework",
            "ModelingComponents",
            "ModelingOperators","GPULightmass",
            "Engine",
            "Landscape",
            "Foliage","TextureUtilitiesCommon",
            "ImageCore",
            "ImageWrapper",
            "TextureCompressor",
            "DerivedDataCache",
            "TargetDeviceServices",
            "LauncherServices","ShaderCompilerCommon",
            "ShaderPreprocessor"
        });
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers",
                "MaterialEditor",});
        }
        // Enable RTTI for Python integration
        bUseRTTI = true;
        // Enable exceptions for Python integration
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Platform specific settings
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_PLATFORM_WINDOWS=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Android)
        {
            PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_PLATFORM_ANDROID=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_PLATFORM_IOS=1");
        }
        // Lumen specific definitions
        PublicDefinitions.Add("WITH_LUMEN=1");
        PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_VERSION_MAJOR=1");
        PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_VERSION_MINOR=0");
        PublicDefinitions.Add("AURACRON_LUMEN_BRIDGE_VERSION_PATCH=0");
    }
}
