// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Wind System Header
// Bridge 4.6: Foliage - Wind System

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageLOD.h"

// UE5.6 Wind System includes
#include "Components/WindDirectionalSourceComponent.h"
#include "Engine/WindDirectionalSource.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"

// Foliage includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageType.h"
#include "FoliageInstancedStaticMeshComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Math and Utilities
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Misc/DateTime.h"

// Noise includes
#include "Math/Noise.h"

#include "AuracronFoliageWind.generated.h"

// Forward declarations
class UAuracronFoliageWindManager;
class UAuracronFoliageBiomeManager;

// =============================================================================
// WIND TYPES AND ENUMS
// =============================================================================

// Wind types
UENUM(BlueprintType)
enum class EAuracronWindType : uint8
{
    Global                  UMETA(DisplayName = "Global Wind"),
    Directional             UMETA(DisplayName = "Directional Wind"),
    Radial                  UMETA(DisplayName = "Radial Wind"),
    Vortex                  UMETA(DisplayName = "Vortex Wind"),
    Turbulence              UMETA(DisplayName = "Turbulence"),
    Seasonal                UMETA(DisplayName = "Seasonal Wind"),
    Interactive             UMETA(DisplayName = "Interactive Wind"),
    Custom                  UMETA(DisplayName = "Custom Wind")
};

// Wind strength levels
UENUM(BlueprintType)
enum class EAuracronWindStrength : uint8
{
    Calm                    UMETA(DisplayName = "Calm"),
    Light                   UMETA(DisplayName = "Light Breeze"),
    Moderate                UMETA(DisplayName = "Moderate Breeze"),
    Strong                  UMETA(DisplayName = "Strong Breeze"),
    Gale                    UMETA(DisplayName = "Gale"),
    Storm                   UMETA(DisplayName = "Storm"),
    Hurricane               UMETA(DisplayName = "Hurricane"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Wind animation types
UENUM(BlueprintType)
enum class EAuracronWindAnimationType : uint8
{
    SimpleWave              UMETA(DisplayName = "Simple Wave"),
    ComplexWave             UMETA(DisplayName = "Complex Wave"),
    Noise                   UMETA(DisplayName = "Noise Based"),
    Procedural              UMETA(DisplayName = "Procedural"),
    PhysicsBased            UMETA(DisplayName = "Physics Based"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Seasonal wind patterns
UENUM(BlueprintType)
enum class EAuracronSeasonalWindPattern : uint8
{
    Spring                  UMETA(DisplayName = "Spring Pattern"),
    Summer                  UMETA(DisplayName = "Summer Pattern"),
    Autumn                  UMETA(DisplayName = "Autumn Pattern"),
    Winter                  UMETA(DisplayName = "Winter Pattern"),
    Monsoon                 UMETA(DisplayName = "Monsoon Pattern"),
    Trade                   UMETA(DisplayName = "Trade Winds"),
    Westerlies              UMETA(DisplayName = "Westerlies"),
    Custom                  UMETA(DisplayName = "Custom Pattern")
};

// =============================================================================
// WIND CONFIGURATION DATA
// =============================================================================

/**
 * Wind Configuration Data
 * Configuration for wind system behavior
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronWindConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind System")
    bool bEnableWindSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind System")
    EAuracronWindType DefaultWindType = EAuracronWindType::Global;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind System")
    EAuracronWindStrength DefaultWindStrength = EAuracronWindStrength::Moderate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Global Wind")
    FVector GlobalWindDirection = FVector(1.0f, 0.0f, 0.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Global Wind")
    float GlobalWindStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Global Wind")
    float GlobalWindSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    EAuracronWindAnimationType AnimationType = EAuracronWindAnimationType::ComplexWave;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float AnimationSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float AnimationIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    bool bEnableWindVariation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    float VariationFrequency = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    float VariationAmplitude = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal")
    bool bEnableSeasonalWinds = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal")
    EAuracronSeasonalWindPattern SeasonalPattern = EAuracronSeasonalWindPattern::Spring;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal")
    float SeasonalIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncWindUpdates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxWindZonesPerFrame = 50;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float WindUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxWindDistance = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameters")
    TSoftObjectPtr<UMaterialParameterCollection> WindParameterCollection;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameters")
    FString WindDirectionParameterName = TEXT("WindDirection");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameters")
    FString WindStrengthParameterName = TEXT("WindStrength");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameters")
    FString WindSpeedParameterName = TEXT("WindSpeed");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameters")
    FString WindTimeParameterName = TEXT("WindTime");

    FAuracronWindConfiguration()
    {
        bEnableWindSystem = true;
        DefaultWindType = EAuracronWindType::Global;
        DefaultWindStrength = EAuracronWindStrength::Moderate;
        GlobalWindDirection = FVector(1.0f, 0.0f, 0.0f);
        GlobalWindStrength = 1.0f;
        GlobalWindSpeed = 1.0f;
        AnimationType = EAuracronWindAnimationType::ComplexWave;
        AnimationSpeed = 1.0f;
        AnimationIntensity = 1.0f;
        bEnableWindVariation = true;
        VariationFrequency = 0.1f;
        VariationAmplitude = 0.3f;
        bEnableSeasonalWinds = true;
        SeasonalPattern = EAuracronSeasonalWindPattern::Spring;
        SeasonalIntensity = 1.0f;
        bEnableAsyncWindUpdates = true;
        MaxWindZonesPerFrame = 50;
        WindUpdateInterval = 0.1f;
        MaxWindDistance = 10000.0f;
        WindDirectionParameterName = TEXT("WindDirection");
        WindStrengthParameterName = TEXT("WindStrength");
        WindSpeedParameterName = TEXT("WindSpeed");
        WindTimeParameterName = TEXT("WindTime");
    }
};

// =============================================================================
// WIND ZONE DATA
// =============================================================================

/**
 * Wind Zone Data
 * Data for individual wind zones
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronWindZoneData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind Zone")
    FString ZoneId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind Zone")
    FString ZoneName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind Zone")
    EAuracronWindType WindType = EAuracronWindType::Directional;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind Zone")
    EAuracronWindStrength WindStrength = EAuracronWindStrength::Moderate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    FVector CenterLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float Radius = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float Height = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind Properties")
    FVector WindDirection = FVector(1.0f, 0.0f, 0.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind Properties")
    float WindForce = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind Properties")
    float WindSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind Properties")
    float TurbulenceLevel = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    EAuracronWindAnimationType AnimationType = EAuracronWindAnimationType::ComplexWave;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float AnimationPhase = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float AnimationFrequency = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    bool bEnableVariation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    float VariationSeed = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    float VariationScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Falloff")
    bool bUseFalloff = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Falloff")
    float FalloffExponent = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Falloff")
    float FalloffStartDistance = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome Integration")
    FString BiomeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome Integration")
    TArray<FString> AffectedFoliageTypes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    float LastUpdateTime = 0.0f;

    FAuracronWindZoneData()
    {
        WindType = EAuracronWindType::Directional;
        WindStrength = EAuracronWindStrength::Moderate;
        CenterLocation = FVector::ZeroVector;
        Radius = 1000.0f;
        Height = 500.0f;
        WindDirection = FVector(1.0f, 0.0f, 0.0f);
        WindForce = 1.0f;
        WindSpeed = 1.0f;
        TurbulenceLevel = 0.3f;
        AnimationType = EAuracronWindAnimationType::ComplexWave;
        AnimationPhase = 0.0f;
        AnimationFrequency = 1.0f;
        bEnableVariation = true;
        VariationSeed = 0.0f;
        VariationScale = 1.0f;
        bUseFalloff = true;
        FalloffExponent = 2.0f;
        FalloffStartDistance = 0.7f;
        bIsActive = true;
        LastUpdateTime = 0.0f;
    }
};

// =============================================================================
// SEASONAL WIND DATA
// =============================================================================

/**
 * Seasonal Wind Data
 * Data for seasonal wind patterns
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronSeasonalWindData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal Wind")
    EAuracronSeasonalWindPattern Pattern = EAuracronSeasonalWindPattern::Spring;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal Wind")
    FVector BaseDirection = FVector(1.0f, 0.0f, 0.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal Wind")
    float BaseStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal Wind")
    float SeasonalModifier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    float DirectionVariation = 15.0f; // degrees

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    float StrengthVariation = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
    float CycleDuration = 300.0f; // seconds

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
    float TransitionDuration = 30.0f; // seconds

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    bool bAffectTemperature = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    bool bAffectHumidity = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    float TemperatureEffect = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    float HumidityEffect = 0.1f;

    FAuracronSeasonalWindData()
    {
        Pattern = EAuracronSeasonalWindPattern::Spring;
        BaseDirection = FVector(1.0f, 0.0f, 0.0f);
        BaseStrength = 1.0f;
        SeasonalModifier = 1.0f;
        DirectionVariation = 15.0f;
        StrengthVariation = 0.3f;
        CycleDuration = 300.0f;
        TransitionDuration = 30.0f;
        bAffectTemperature = true;
        bAffectHumidity = true;
        TemperatureEffect = 0.1f;
        HumidityEffect = 0.1f;
    }
};

// =============================================================================
// WIND PERFORMANCE DATA
// =============================================================================

/**
 * Wind Performance Data
 * Performance metrics for wind system
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronWindPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TotalWindZones = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ActiveWindZones = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 AffectedFoliageInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float WindUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaterialUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float AnimationUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float AverageFrameTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaterialParameterUpdates = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MemoryUsageMB = 0.0f;

    FAuracronWindPerformanceData()
    {
        TotalWindZones = 0;
        ActiveWindZones = 0;
        AffectedFoliageInstances = 0;
        WindUpdateTime = 0.0f;
        MaterialUpdateTime = 0.0f;
        AnimationUpdateTime = 0.0f;
        AverageFrameTime = 0.0f;
        MaterialParameterUpdates = 0;
        MemoryUsageMB = 0.0f;
    }
};

// =============================================================================
// FOLIAGE WIND MANAGER
// =============================================================================

/**
 * Foliage Wind Manager
 * Manager for the foliage wind system including wind zones, seasonal patterns, and animation
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronFoliageWindManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    static UAuracronFoliageWindManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void Initialize(const FAuracronWindConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void Tick(float DeltaTime);

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void SetConfiguration(const FAuracronWindConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    FAuracronWindConfiguration GetConfiguration() const;

    // Global wind control
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void SetGlobalWind(const FVector& Direction, float Strength, float Speed);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void GetGlobalWind(FVector& Direction, float& Strength, float& Speed) const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void EnableGlobalWind(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    bool IsGlobalWindEnabled() const;

    // Wind zone management
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    FString CreateWindZone(const FAuracronWindZoneData& ZoneData);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    bool UpdateWindZone(const FString& ZoneId, const FAuracronWindZoneData& ZoneData);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    bool RemoveWindZone(const FString& ZoneId);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    FAuracronWindZoneData GetWindZone(const FString& ZoneId) const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    TArray<FAuracronWindZoneData> GetAllWindZones() const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    TArray<FString> GetWindZonesInArea(const FBox& Area) const;

    // Wind strength control
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void SetWindStrength(EAuracronWindStrength StrengthLevel);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    EAuracronWindStrength GetWindStrength() const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    float GetWindStrengthValue(EAuracronWindStrength StrengthLevel) const;

    // Wind direction control
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void SetWindDirection(const FVector& Direction);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    FVector GetWindDirection() const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    FVector GetWindAtLocation(const FVector& Location) const;

    // Seasonal wind patterns
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void SetSeasonalWindPattern(EAuracronSeasonalWindPattern Pattern);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    EAuracronSeasonalWindPattern GetSeasonalWindPattern() const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void UpdateSeasonalWinds(float SeasonProgress);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    FAuracronSeasonalWindData GetSeasonalWindData() const;

    // Foliage animation
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void SetFoliageWindAnimation(const FString& FoliageTypeId, EAuracronWindAnimationType AnimationType);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void UpdateFoliageWindAnimation(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void SetAnimationSpeed(float Speed);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    float GetAnimationSpeed() const;

    // Material parameter control
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void UpdateMaterialParameters();

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void SetMaterialParameterCollection(UMaterialParameterCollection* ParameterCollection);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    UMaterialParameterCollection* GetMaterialParameterCollection() const;

    // Biome integration
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void SetBiomeWindSettings(const FString& BiomeId, const FAuracronWindZoneData& WindSettings);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    FAuracronWindZoneData GetBiomeWindSettings(const FString& BiomeId) const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void ApplyBiomeWindToFoliage(const FString& BiomeId);

    // Performance monitoring
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    FAuracronWindPerformanceData GetPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void UpdatePerformanceMetrics();

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    int32 GetActiveWindZoneCount() const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    int32 GetAffectedFoliageCount() const;

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void DrawDebugWindZones(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Wind Manager")
    void LogWindStatistics() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWindZoneCreated, FString, ZoneId, FAuracronWindZoneData, ZoneData);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWindZoneRemoved, FString, ZoneId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnGlobalWindChanged, FVector, Direction, float, Strength);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSeasonalWindChanged, EAuracronSeasonalWindPattern, Pattern);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnWindZoneCreated OnWindZoneCreated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnWindZoneRemoved OnWindZoneRemoved;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnGlobalWindChanged OnGlobalWindChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnSeasonalWindChanged OnSeasonalWindChanged;

private:
    static UAuracronFoliageWindManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronWindConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Wind data
    TMap<FString, FAuracronWindZoneData> WindZones;
    FAuracronSeasonalWindData SeasonalWindData;
    TMap<FString, FAuracronWindZoneData> BiomeWindSettings;

    // Material parameter collection
    UPROPERTY()
    TWeakObjectPtr<UMaterialParameterCollection> WindParameterCollection;

    UPROPERTY()
    TWeakObjectPtr<UMaterialParameterCollectionInstance> WindParameterInstance;

    // Performance data
    FAuracronWindPerformanceData PerformanceData;
    float LastPerformanceUpdate = 0.0f;
    float LastWindUpdate = 0.0f;
    float LastMaterialUpdate = 0.0f;

    // Animation data
    float WindTime = 0.0f;
    float SeasonProgress = 0.0f;

    // Debug settings
    bool bDebugVisualizationEnabled = false;

    // Thread safety
    mutable FCriticalSection WindLock;

    // Internal functions
    void ValidateConfiguration();
    FString GenerateWindZoneId() const;
    void UpdateWindZoneInternal(FAuracronWindZoneData& ZoneData, float DeltaTime);
    void UpdateMaterialParametersInternal();
    void UpdateSeasonalWindsInternal(float DeltaTime);
    void UpdatePerformanceDataInternal();
    FVector CalculateWindAtLocationInternal(const FVector& Location) const;
    float CalculateWindStrengthFromEnum(EAuracronWindStrength StrengthLevel) const;
    void ApplyWindToFoliageInstance(UHierarchicalInstancedStaticMeshComponent* Component, const FAuracronWindZoneData& WindData);
};
