# AURACRON Content Structure

Estrutura organizada de conteúdo para o jogo AURACRON.

## Estrutura de Diretórios

### Core/
Assets fundamentais e configurações base do jogo.
- GameModes/
- GameStates/
- PlayerControllers/
- GameInstances/
- Settings/

### Characters/
Personagens e sistemas relacionados.
- Heroes/ - Her<PERSON>is jogáveis
- NPCs/ - Personagens não-jogáveis
- MetaHumans/ - Assets MetaHuman
- Animations/ - Animações
- Rigs/ - Control Rigs

### Maps/
Mapas e níveis do jogo.
- MainMenu/ - Menu principal
- Arena/ - Arenas de combate
- TestMaps/ - Mapas de teste
- Procedural/ - Mapas gerados proceduralmente

### Abilities/
Sistema de habilidades e sígilos.
- Sigils/ - S<PERSON><PERSON><PERSON>
- <PERSON>pel<PERSON>/ - Magias e habilidades
- Effects/ - Efeitos visuais
- Audio/ - <PERSON><PERSON>o das habilidades

### UI/
Interface do usuário.
- HUD/ - Interface durante o jogo
- Menus/ - Menus e telas
- Widgets/ - Widgets reutilizáveis
- Icons/ - Ícones e imagens

### Audio/
Sistema de áudio.
- Music/ - Música
- SFX/ - Efeitos sonoros
- Voice/ - Vozes e diálogos
- Ambient/ - Sons ambientes

### VFX/
Efeitos visuais.
- Particles/ - Sistemas de partículas
- Niagara/ - Efeitos Niagara
- Materials/ - Materiais para VFX
- Textures/ - Texturas para efeitos

### Materials/
Materiais e shaders.
- Characters/ - Materiais de personagens
- Environment/ - Materiais ambientais
- UI/ - Materiais de interface
- VFX/ - Materiais para efeitos

### Textures/
Texturas organizadas por categoria.
- Characters/
- Environment/
- UI/
- VFX/

### Meshes/
Modelos 3D.
- Characters/
- Environment/
- Props/
- Weapons/

### PCG/
Assets do Procedural Content Generation.
- Graphs/ - Grafos PCG
- Settings/ - Configurações PCG
- Data/ - Dados para geração
- Templates/ - Templates reutilizáveis

### Realms/
Assets específicos dos Realms do jogo.
- Terrestrial/ - Planície Radiante
- Aerial/ - Firmamento Zephyr
- Abyssal/ - Abismo Umbrio
- Transitions/ - Transições entre realms

## Convenções de Nomenclatura

- Prefixos por categoria (BP_ para Blueprints, M_ para Materials, etc.)
- Nomes descritivos em inglês
- Organização hierárquica clara
- Versionamento para assets importantes
