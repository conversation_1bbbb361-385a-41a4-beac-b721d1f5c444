// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Node System Header
// Bridge 2.2: PCG Framework - Graph System Core

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"

// PCG Framework includes
#include "PCGSettings.h"
#include "PCGNode.h"
#include "PCGPin.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"

#include "AuracronPCGNodeSystem.generated.h"

// Forward declarations
class UAuracronPCGNodeRegistry;
class UAuracronPCGNodeFactory;

// Node categories for organization
UENUM(BlueprintType)
enum class EAuracronPCGNodeCategory : uint8
{
    Input           UMETA(DisplayName = "Input"),
    Output          UMETA(DisplayName = "Output"),
    Generator       UMETA(DisplayName = "Generator"),
    Filter          UMETA(DisplayName = "Filter"),
    Transform       UMETA(DisplayName = "Transform"),
    Sampler         UMETA(DisplayName = "Sampler"),
    Utility         UMETA(DisplayName = "Utility"),
    Custom          UMETA(DisplayName = "Custom"),
    Debug           UMETA(DisplayName = "Debug"),
    Advanced        UMETA(DisplayName = "Advanced")
};

// Node execution priority
UENUM(BlueprintType)
enum class EAuracronPCGNodePriority : uint8
{
    Lowest          UMETA(DisplayName = "Lowest"),
    Low             UMETA(DisplayName = "Low"),
    Normal          UMETA(DisplayName = "Normal"),
    High            UMETA(DisplayName = "High"),
    Highest         UMETA(DisplayName = "Highest"),
    Critical        UMETA(DisplayName = "Critical")
};

// Node metadata for registration
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGNodeMetadata
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
    FString NodeName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
    FString NodeDescription;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
    EAuracronPCGNodeCategory Category = EAuracronPCGNodeCategory::Custom;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
    EAuracronPCGNodePriority Priority = EAuracronPCGNodePriority::Normal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
    FString Author;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
    FString Version;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
    TArray<FString> Tags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
    bool bIsExperimental = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
    bool bRequiresGPU = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
    FLinearColor NodeColor = FLinearColor::White;

    FAuracronPCGNodeMetadata()
    {
        NodeName = TEXT("Custom Node");
        NodeDescription = TEXT("Custom AURACRON PCG Node");
        Category = EAuracronPCGNodeCategory::Custom;
        Priority = EAuracronPCGNodePriority::Normal;
        Author = TEXT("AURACRON");
        Version = TEXT("1.0");
        bIsExperimental = false;
        bRequiresGPU = false;
        NodeColor = FLinearColor(0.2f, 0.7f, 1.0f);
    }
};

/**
 * Base settings class for AURACRON custom PCG nodes
 * Extends the framework base with node-specific functionality
 */
UCLASS(BlueprintType, Blueprintable, Abstract)
class AURACRONPCGFRAMEWORK_API UAuracronPCGNodeSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronPCGNodeSettings();

    // Node metadata
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Info")
    FAuracronPCGNodeMetadata NodeMetadata;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Settings")
    bool bCacheResults = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Settings")
    bool bShowAdvancedSettings = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Settings")
    bool bAllowParallelExecution = true;

    // Node validation
    UFUNCTION(BlueprintCallable, Category = "Node Validation")
    virtual bool ValidateNodeSettings(TArray<FString>& ValidationErrors) const;

    UFUNCTION(BlueprintCallable, Category = "Node Validation")
    virtual bool IsNodeConfigurationValid() const;

    // Node information
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Info")
    virtual FString GetNodeDisplayName() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Info")
    virtual FString GetNodeTooltip() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Info")
    virtual EAuracronPCGNodeCategory GetNodeCategory() const { return NodeMetadata.Category; }

    // UPCGSettings overrides
    virtual TArray<FPCGPinProperties> GetCustomInputPinProperties() const override;
    virtual TArray<FPCGPinProperties> GetCustomOutputPinProperties() const override;

#if WITH_EDITOR
    virtual FText GetDefaultNodeTitle() const override;
    virtual FText GetNodeTooltipText() const override;
    virtual FLinearColor GetNodeTitleColor() const override;
#endif

protected:
    // Override these in derived classes for custom pin configuration
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const;
};

/**
 * Enhanced PCG element for AURACRON custom nodes
 * Provides additional functionality and integration
 */
class AURACRONPCGFRAMEWORK_API FAuracronPCGNodeElement : public FAuracronPCGElementBase
{
public:
    FAuracronPCGNodeElement();
    virtual ~FAuracronPCGNodeElement() = default;

    // Enhanced execution with node-specific features
    virtual bool Execute(FPCGContext* Context) const override;

protected:
    // Node-specific execution methods
    virtual bool PreExecute(FPCGContext* Context) const;
    virtual bool PostExecute(FPCGContext* Context, const FAuracronPCGElementResult& Result) const;
    virtual bool ValidateNodeExecution(FPCGContext* Context) const;

    // Performance monitoring
    virtual void BeginNodeProfiling(FPCGContext* Context) const;
    virtual void EndNodeProfiling(FPCGContext* Context, const FAuracronPCGElementResult& Result) const;

    // Caching support
    virtual bool ShouldUseCache(FPCGContext* Context) const;
    virtual bool LoadFromCache(FPCGContext* Context, FPCGDataCollection& OutputData) const;
    virtual void SaveToCache(FPCGContext* Context, const FPCGDataCollection& OutputData) const;

private:
    mutable TMap<FString, double> ProfilingData;
    mutable FCriticalSection ProfilingLock;
};

/**
 * Template class for typed AURACRON PCG nodes
 * Provides type-safe access to settings
 */
template<typename SettingsType>
class AURACRONPCGFRAMEWORK_API TAuracronPCGNodeElement : public FAuracronPCGNodeElement
{
    static_assert(TIsDerivedFrom<SettingsType, UAuracronPCGNodeSettings>::IsDerived, 
                  "SettingsType must derive from UAuracronPCGNodeSettings");

public:
    using NodeSettingsClass = SettingsType;

protected:
    // Type-safe settings access
    const SettingsType* GetNodeSettings(FPCGContext* Context) const
    {
        if (Context && Context->Node)
        {
            return Cast<SettingsType>(Context->Node->GetSettings());
        }
        return nullptr;
    }

    // Type-safe parameter extraction
    virtual FAuracronPCGElementParams ExtractParameters(const UPCGSettings* Settings) const override
    {
        if (const SettingsType* TypedSettings = Cast<SettingsType>(Settings))
        {
            return TypedSettings->ElementParameters;
        }
        return FAuracronPCGElementParams();
    }
};

/**
 * Node registry for managing custom PCG nodes
 * Handles registration, discovery, and instantiation
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGNodeRegistry : public UObject
{
    GENERATED_BODY()

public:
    UAuracronPCGNodeRegistry();

    // Node registration
    UFUNCTION(BlueprintCallable, Category = "Node Registry")
    bool RegisterNode(TSubclassOf<UAuracronPCGNodeSettings> NodeClass);

    UFUNCTION(BlueprintCallable, Category = "Node Registry")
    bool UnregisterNode(TSubclassOf<UAuracronPCGNodeSettings> NodeClass);

    UFUNCTION(BlueprintCallable, Category = "Node Registry")
    bool IsNodeRegistered(TSubclassOf<UAuracronPCGNodeSettings> NodeClass) const;

    // Node discovery
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Registry")
    TArray<TSubclassOf<UAuracronPCGNodeSettings>> GetAllRegisteredNodes() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Registry")
    TArray<TSubclassOf<UAuracronPCGNodeSettings>> GetNodesByCategory(EAuracronPCGNodeCategory Category) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Registry")
    TArray<TSubclassOf<UAuracronPCGNodeSettings>> SearchNodesByName(const FString& SearchTerm) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Registry")
    TArray<TSubclassOf<UAuracronPCGNodeSettings>> GetNodesByTag(const FString& Tag) const;

    // Node metadata
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Registry")
    FAuracronPCGNodeMetadata GetNodeMetadata(TSubclassOf<UAuracronPCGNodeSettings> NodeClass) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Registry")
    TMap<EAuracronPCGNodeCategory, int32> GetNodeCountByCategory() const;

    // Node instantiation
    UFUNCTION(BlueprintCallable, Category = "Node Registry")
    UAuracronPCGNodeSettings* CreateNodeInstance(TSubclassOf<UAuracronPCGNodeSettings> NodeClass) const;

    // Registry management
    UFUNCTION(BlueprintCallable, Category = "Node Registry")
    void RefreshRegistry();

    UFUNCTION(BlueprintCallable, Category = "Node Registry")
    void ClearRegistry();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Registry")
    int32 GetRegisteredNodeCount() const { return RegisteredNodes.Num(); }

    // Validation
    UFUNCTION(BlueprintCallable, Category = "Node Registry")
    bool ValidateNodeClass(TSubclassOf<UAuracronPCGNodeSettings> NodeClass, TArray<FString>& ValidationErrors) const;

    // Singleton access
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Node Registry")
    static UAuracronPCGNodeRegistry* GetGlobalRegistry();

protected:
    // Registered nodes storage
    UPROPERTY()
    TArray<TSubclassOf<UAuracronPCGNodeSettings>> RegisteredNodes;

    UPROPERTY()
    TMap<TSubclassOf<UAuracronPCGNodeSettings>, FAuracronPCGNodeMetadata> NodeMetadataMap;

    // Internal methods
    virtual void InitializeRegistry();
    virtual void DiscoverBuiltInNodes();
    virtual bool ValidateNodeRegistration(TSubclassOf<UAuracronPCGNodeSettings> NodeClass) const;
    virtual FAuracronPCGNodeMetadata ExtractNodeMetadata(TSubclassOf<UAuracronPCGNodeSettings> NodeClass) const;

private:
    static UAuracronPCGNodeRegistry* GlobalRegistryInstance;
    mutable FCriticalSection RegistryLock;
};

// Convenience macros for node creation
#define AURACRON_PCG_NODE_SETTINGS(ClassName, ElementClassName) \
    UCLASS(BlueprintType, Blueprintable) \
    class AURACRONPCGFRAMEWORK_API ClassName : public UAuracronPCGNodeSettings \
    { \
        GENERATED_BODY() \
    public: \
        ClassName() { ElementName = TEXT(#ElementClassName); } \
    protected: \
        virtual FPCGElementPtr CreateCustomElement() const override \
        { \
            return MakeShared<ElementClassName>(); \
        } \
    };

#define AURACRON_PCG_NODE_ELEMENT(ClassName, SettingsClassName) \
    class AURACRONPCGFRAMEWORK_API ClassName : public TAuracronPCGNodeElement<SettingsClassName> \
    { \
    public: \
        ClassName() = default; \
        virtual ~ClassName() = default; \
    protected: \
        virtual FAuracronPCGElementResult ProcessData(const FPCGDataCollection& InputData, FPCGDataCollection& OutputData, const FAuracronPCGElementParams& Parameters) const override; \
    };

#define AURACRON_PCG_REGISTER_NODE(NodeClass) \
    static bool bRegistered_##NodeClass = []() { \
        if (UAuracronPCGNodeRegistry* Registry = UAuracronPCGNodeRegistry::GetGlobalRegistry()) \
        { \
            return Registry->RegisterNode(NodeClass::StaticClass()); \
        } \
        return false; \
    }();

// Node creation helpers
namespace AuracronPCGNodeUtils
{
    AURACRONPCGFRAMEWORK_API TSubclassOf<UAuracronPCGNodeSettings> FindNodeByName(const FString& NodeName);
    AURACRONPCGFRAMEWORK_API TArray<TSubclassOf<UAuracronPCGNodeSettings>> GetCompatibleNodes(EPCGDataType InputType, EPCGDataType OutputType);
    AURACRONPCGFRAMEWORK_API bool IsNodeCompatibleWithData(TSubclassOf<UAuracronPCGNodeSettings> NodeClass, EPCGDataType DataType);
    AURACRONPCGFRAMEWORK_API FString GenerateNodeDocumentation(TSubclassOf<UAuracronPCGNodeSettings> NodeClass);
}
