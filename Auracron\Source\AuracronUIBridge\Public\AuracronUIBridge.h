﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Interface do UsuÃ¡rio Bridge
// IntegraÃ§Ã£o C++ para UI/UX usando Common UI Framework e APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "ModularGameplay/Public/GameFrameworkComponent.h"
#include "UMG.h"
#include "Blueprint/UserWidget.h"
#include "Components/Widget.h"
#include "Components/PanelWidget.h"
#include "Components/CanvasPanel.h"
#include "Components/VerticalBox.h"
#include "Components/HorizontalBox.h"
#include "Components/GridPanel.h"
#include "Components/ScrollBox.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/ProgressBar.h"
#include "Components/Slider.h"
#include "Components/CheckBox.h"
#include "Components/ComboBoxString.h"
#include "CommonUI/Public/CommonUserWidget.h"
#include "CommonUI/Public/CommonActivatableWidget.h"
#include "CommonUI/Public/CommonButtonBase.h"
#include "CommonUI/Public/CommonTextBlock.h"
#include "CommonUI/Public/CommonBorder.h"
#include "CommonUI/Public/CommonImage.h"
#include "CommonUI/Public/CommonProgressBar.h"
#include "CommonUI/Public/CommonWidgetSwitcher.h"
#include "CommonUI/Public/CommonTabListWidgetBase.h"
#include "CommonUI/Public/CommonListView.h"
#include "CommonUI/Public/CommonTileView.h"
#include "CommonUI/Public/CommonTreeView.h"
#include "CommonInput/Public/CommonInputSubsystem.h"
#include "CommonInput/Public/CommonInputSettings.h"
#include "GameplayTags/Classes/GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputMappingContext.h"
#include "InputAction.h"
#include "AuracronUIBridge.generated.h"

// Forward Declarations
class UUserWidget;
class UCommonUserWidget;
class UCommonActivatableWidget;
class UCommonButtonBase;
class UCommonTextBlock;
class UCommonProgressBar;
class UCommonInputSubsystem;
class UEnhancedInputComponent;
class UInputMappingContext;
class UInputAction;

/**
 * EnumeraÃ§Ã£o para tipos de UI
 */
UENUM(BlueprintType)
enum class EAuracronUIType : uint8
{
    None            UMETA(DisplayName = "None"),
    MainMenu        UMETA(DisplayName = "Main Menu"),
    InGameHUD       UMETA(DisplayName = "In-Game HUD"),
    ChampionSelect  UMETA(DisplayName = "Champion Select"),
    SigilosMenu     UMETA(DisplayName = "Sigilos Menu"),
    ProgressionMenu UMETA(DisplayName = "Progression Menu"),
    SettingsMenu    UMETA(DisplayName = "Settings Menu"),
    ScoreBoard      UMETA(DisplayName = "Score Board"),
    Minimap         UMETA(DisplayName = "Minimap"),
    AbilityBar      UMETA(DisplayName = "Ability Bar"),
    InventoryMenu   UMETA(DisplayName = "Inventory Menu"),
    ShopMenu        UMETA(DisplayName = "Shop Menu"),
    Tutorial        UMETA(DisplayName = "Tutorial"),
    LoadingScreen   UMETA(DisplayName = "Loading Screen"),
    ErrorDialog     UMETA(DisplayName = "Error Dialog"),
    ConfirmDialog   UMETA(DisplayName = "Confirm Dialog")
};

/**
 * EnumeraÃ§Ã£o para plataformas de input
 */
UENUM(BlueprintType)
enum class EAuracronInputPlatform : uint8
{
    PC              UMETA(DisplayName = "PC (Keyboard/Mouse)"),
    Console         UMETA(DisplayName = "Console (Gamepad)"),
    Mobile          UMETA(DisplayName = "Mobile (Touch)"),
    VR              UMETA(DisplayName = "VR"),
    Auto            UMETA(DisplayName = "Auto Detect")
};

/**
 * EnumeraÃ§Ã£o para estados de UI
 */
UENUM(BlueprintType)
enum class EAuracronUIState : uint8
{
    Hidden          UMETA(DisplayName = "Hidden"),
    Visible         UMETA(DisplayName = "Visible"),
    Transitioning   UMETA(DisplayName = "Transitioning"),
    Loading         UMETA(DisplayName = "Loading"),
    Error           UMETA(DisplayName = "Error"),
    Disabled        UMETA(DisplayName = "Disabled")
};

/**
 * Estrutura para entrada de estado de UI (substitui TMap para replicação)
 */
USTRUCT(BlueprintType)
struct AURACRONUIBRIDGE_API FAuracronUIStateEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI State")
    EAuracronUIType UIType = EAuracronUIType::MainMenu;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI State")
    EAuracronUIState UIState = EAuracronUIState::Hidden;

    FAuracronUIStateEntry()
    {
        UIType = EAuracronUIType::MainMenu;
        UIState = EAuracronUIState::Hidden;
    }

    FAuracronUIStateEntry(EAuracronUIType InUIType, EAuracronUIState InUIState)
        : UIType(InUIType), UIState(InUIState)
    {
    }
};

/**
 * Estrutura para configuraÃ§Ã£o de UI adaptativa
 */
USTRUCT(BlueprintType)
struct AURACRONUIBRIDGE_API FAuracronUIConfiguration
{
    GENERATED_BODY()

    /** Escala da UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration", meta = (ClampMin = "0.5", ClampMax = "3.0"))
    float UIScale = 1.0f;

    /** Opacidade da UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration", meta = (ClampMin = "0.1", ClampMax = "1.0"))
    float UIOpacity = 1.0f;

    /** Plataforma de input atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    EAuracronInputPlatform InputPlatform = EAuracronInputPlatform::Auto;

    /** Usar animaÃ§Ãµes de UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    bool bUseUIAnimations = true;

    /** Velocidade das animaÃ§Ãµes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float AnimationSpeed = 1.0f;

    /** Usar efeitos de partÃ­culas na UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    bool bUseUIParticleEffects = true;

    /** Usar sons de UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    bool bUseUISounds = true;

    /** Volume dos sons de UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float UISoundVolume = 0.8f;

    /** Usar haptic feedback */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    bool bUseHapticFeedback = true;

    /** Intensidade do haptic feedback */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float HapticIntensity = 0.5f;

    /** Modo de alto contraste */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    bool bHighContrastMode = false;

    /** Suporte a daltonismo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    bool bColorBlindSupport = false;

    /** Tipo de daltonismo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    FString ColorBlindnessType = TEXT("None");

    /** Tamanho da fonte */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration", meta = (ClampMin = "0.5", ClampMax = "3.0"))
    float FontScale = 1.0f;

    /** Usar leitor de tela */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    bool bScreenReaderSupport = false;

    /** Idioma da UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    FString UILanguage = TEXT("en");

    /** RegiÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    FString UIRegion = TEXT("US");

    /** Usar layout responsivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    bool bUseResponsiveLayout = true;

    /** ResoluÃ§Ã£o de referÃªncia */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    FVector2D ReferenceResolution = FVector2D(1920.0f, 1080.0f);

    /** Usar safe zones */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration")
    bool bUseSafeZones = true;

    /** Margem das safe zones */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI Configuration", meta = (ClampMin = "0.0", ClampMax = "0.2"))
    float SafeZoneMargin = 0.05f;
};

/**
 * Estrutura para configuraÃ§Ã£o de minimapa 3D
 */
USTRUCT(BlueprintType)
struct AURACRONUIBRIDGE_API FAuracronMinimapConfiguration
{
    GENERATED_BODY()

    /** Tamanho do minimapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration", meta = (ClampMin = "100.0", ClampMax = "500.0"))
    FVector2D MinimapSize = FVector2D(200.0f, 200.0f);

    /** PosiÃ§Ã£o do minimapa na tela */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    FVector2D MinimapPosition = FVector2D(0.85f, 0.15f);

    /** Zoom do minimapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float MinimapZoom = 1.0f;

    /** RotaÃ§Ã£o do minimapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration", meta = (ClampMin = "0.0", ClampMax = "360.0"))
    float MinimapRotation = 0.0f;

    /** Mostrar todos os realms */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    bool bShowAllRealms = true;

    /** Mostrar jogadores aliados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    bool bShowAlliedPlayers = true;

    /** Mostrar jogadores inimigos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    bool bShowEnemyPlayers = false;

    /** Mostrar objetivos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    bool bShowObjectives = true;

    /** Mostrar wards */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    bool bShowWards = true;

    /** Usar cores de time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    bool bUseTeamColors = true;

    /** Cor do time aliado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    FLinearColor AlliedTeamColor = FLinearColor::Blue;

    /** Cor do time inimigo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    FLinearColor EnemyTeamColor = FLinearColor::Red;

    /** Cor dos objetivos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    FLinearColor ObjectiveColor = FLinearColor::Yellow;

    /** AtualizaÃ§Ã£o em tempo real */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    bool bRealTimeUpdate = true;

    /** FrequÃªncia de atualizaÃ§Ã£o (Hz) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration", meta = (ClampMin = "1", ClampMax = "60"))
    int32 UpdateFrequency = 10;

    /** Usar fog of war */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration")
    bool bUseFogOfWar = true;

    /** Raio de visÃ£o no minimapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minimap Configuration", meta = (ClampMin = "500.0", ClampMax = "5000.0"))
    float VisionRadius = 1500.0f;
};

/**
 * Estrutura para configuraÃ§Ã£o de HUD de combate
 */
USTRUCT(BlueprintType)
struct AURACRONUIBRIDGE_API FAuracronCombatHUDConfiguration
{
    GENERATED_BODY()

    /** Mostrar barra de HP */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowHealthBar = true;

    /** Mostrar barra de mana */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowManaBar = true;

    /** Mostrar cooldowns de habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowAbilityCooldowns = true;

    /** Mostrar indicadores de dano */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowDamageIndicators = true;

    /** Mostrar indicadores de cura */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowHealingIndicators = true;

    /** Mostrar buffs/debuffs */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowStatusEffects = true;

    /** Mostrar XP e nÃ­vel */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowExperienceBar = true;

    /** Mostrar gold */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowGold = true;

    /** Mostrar KDA */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowKDA = true;

    /** Mostrar timer da partida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowMatchTimer = true;

    /** PosiÃ§Ã£o da barra de HP */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    FVector2D HealthBarPosition = FVector2D(0.1f, 0.9f);

    /** Tamanho da barra de HP */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    FVector2D HealthBarSize = FVector2D(300.0f, 20.0f);

    /** PosiÃ§Ã£o da barra de mana */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    FVector2D ManaBarPosition = FVector2D(0.1f, 0.95f);

    /** Tamanho da barra de mana */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    FVector2D ManaBarSize = FVector2D(300.0f, 15.0f);

    /** PosiÃ§Ã£o das habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    FVector2D AbilitiesPosition = FVector2D(0.5f, 0.95f);

    /** Tamanho dos Ã­cones de habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration", meta = (ClampMin = "32.0", ClampMax = "128.0"))
    float AbilityIconSize = 64.0f;

    /** EspaÃ§amento entre habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration", meta = (ClampMin = "2.0", ClampMax = "20.0"))
    float AbilitySpacing = 8.0f;

    /** Usar layout compacto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bUseCompactLayout = false;

    /** Mostrar tooltips */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bShowTooltips = true;

    /** Delay para mostrar tooltips */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration", meta = (ClampMin = "0.0", ClampMax = "3.0"))
    float TooltipDelay = 0.5f;

    /** Usar cores de time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    bool bUseTeamColors = true;

    /** Cor do HP */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    FLinearColor HealthColor = FLinearColor::Green;

    /** Cor da mana */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    FLinearColor ManaColor = FLinearColor::Blue;

    /** Cor do XP */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    FLinearColor ExperienceColor = FLinearColor::Yellow;

    /** Cor dos cooldowns */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat HUD Configuration")
    FLinearColor CooldownColor = FLinearColor(0.2f, 0.2f, 0.2f, 0.8f);
};

/**
 * Estrutura para configuraÃ§Ã£o de input cross-platform
 */
USTRUCT(BlueprintType)
struct AURACRONUIBRIDGE_API FAuracronCrossPlatformInputConfiguration
{
    GENERATED_BODY()

    /** Contexto de mapeamento para PC */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input")
    TSoftObjectPtr<UInputMappingContext> PCInputContext;

    /** Contexto de mapeamento para Console */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input")
    TSoftObjectPtr<UInputMappingContext> ConsoleInputContext;

    /** Contexto de mapeamento para Mobile */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input")
    TSoftObjectPtr<UInputMappingContext> MobileInputContext;

    /** Sensibilidade do mouse */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float MouseSensitivity = 1.0f;

    /** Sensibilidade do gamepad */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float GamepadSensitivity = 1.5f;

    /** Sensibilidade do touch */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float TouchSensitivity = 1.2f;

    /** Inverter eixo Y */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input")
    bool bInvertYAxis = false;

    /** Usar auto-aim em mobile */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input")
    bool bUseAutoAimOnMobile = true;

    /** Intensidade do auto-aim */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AutoAimIntensity = 0.3f;

    /** Usar gestos touch */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input")
    bool bUseTouchGestures = true;

    /** Tamanho dos botÃµes touch */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input", meta = (ClampMin = "32.0", ClampMax = "128.0"))
    float TouchButtonSize = 64.0f;

    /** Opacidade dos botÃµes touch */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input", meta = (ClampMin = "0.1", ClampMax = "1.0"))
    float TouchButtonOpacity = 0.7f;

    /** Usar vibraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input")
    bool bUseVibration = true;

    /** Intensidade da vibraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cross-Platform Input", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float VibrationIntensity = 0.5f;
};

/**
 * Classe principal do Bridge para Sistema de Interface do UsuÃ¡rio
 * ResponsÃ¡vel pelo gerenciamento completo de UI/UX cross-platform
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|UI", meta = (DisplayName = "AURACRON UI Bridge", BlueprintSpawnableComponent))
class AURACRONUIBRIDGE_API UAuracronUIBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronUIBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core UI Management ===

    /**
     * Mostrar UI especÃ­fica
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Management", CallInEditor)
    bool ShowUI(EAuracronUIType UIType, bool bAnimate = true);

    /**
     * Esconder UI especÃ­fica
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Management", CallInEditor)
    bool HideUI(EAuracronUIType UIType, bool bAnimate = true);

    /**
     * Alternar visibilidade de UI
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Management", CallInEditor)
    bool ToggleUI(EAuracronUIType UIType, bool bAnimate = true);

    /**
     * Verificar se UI estÃ¡ visÃ­vel
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Management", CallInEditor)
    bool IsUIVisible(EAuracronUIType UIType) const;

    /**
     * Obter estado de UI
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Management", CallInEditor)
    EAuracronUIState GetUIState(EAuracronUIType UIType) const;

    /**
     * Definir widget para tipo de UI
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Management", CallInEditor)
    bool SetUIWidget(EAuracronUIType UIType, TSubclassOf<UCommonUserWidget> WidgetClass);

    /**
     * Obter widget de UI
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Management", CallInEditor)
    UCommonUserWidget* GetUIWidget(EAuracronUIType UIType) const;

    // === HUD Management ===

    /**
     * Atualizar barra de HP
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|HUD", CallInEditor)
    bool UpdateHealthBar(float CurrentHealth, float MaxHealth);

    /**
     * Atualizar barra de mana
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|HUD", CallInEditor)
    bool UpdateManaBar(float CurrentMana, float MaxMana);

    /**
     * Atualizar cooldown de habilidade
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|HUD", CallInEditor)
    bool UpdateAbilityCooldown(const FString& AbilitySlot, float CooldownRemaining, float MaxCooldown);

    /**
     * Mostrar indicador de dano
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|HUD", CallInEditor)
    bool ShowDamageIndicator(const FVector& WorldLocation, float DamageAmount, bool bIsCritical = false);

    /**
     * Atualizar barra de experiÃªncia
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|HUD", CallInEditor)
    bool UpdateExperienceBar(int32 CurrentXP, int32 XPToNextLevel, int32 CurrentLevel);

    /**
     * Atualizar gold
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|HUD", CallInEditor)
    bool UpdateGold(int32 CurrentGold);

    /**
     * Atualizar KDA
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|HUD", CallInEditor)
    bool UpdateKDA(int32 Kills, int32 Deaths, int32 Assists);

    // === Minimap Management ===

    /**
     * Atualizar minimapa 3D
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Minimap", CallInEditor)
    bool UpdateMinimap3D();

    /**
     * Adicionar marcador no minimapa
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Minimap", CallInEditor)
    bool AddMinimapMarker(const FVector& WorldLocation, const FString& MarkerType, const FLinearColor& MarkerColor);

    /**
     * Remover marcador do minimapa
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Minimap", CallInEditor)
    bool RemoveMinimapMarker(const FString& MarkerID);

    /**
     * Definir zoom do minimapa
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Minimap", CallInEditor)
    bool SetMinimapZoom(float ZoomLevel);

    /**
     * Alternar realm no minimapa
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Minimap", CallInEditor)
    bool ToggleMinimapRealm(int32 RealmIndex);

    // === Cross-Platform Input ===

    /**
     * Configurar input para plataforma
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Input", CallInEditor)
    bool SetupPlatformInput(EAuracronInputPlatform Platform);

    /**
     * Detectar plataforma automaticamente
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Input", CallInEditor)
    EAuracronInputPlatform DetectInputPlatform() const;

    /**
     * Processar input de UI
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Input", CallInEditor)
    bool ProcessUIInput(const FString& InputAction, const FVector2D& InputValue);

    /**
     * Configurar controles touch
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Input", CallInEditor)
    bool SetupTouchControls();

    /**
     * Mostrar/esconder controles touch
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Input", CallInEditor)
    bool ToggleTouchControls(bool bShow);

    // === Configuration Management ===

    /**
     * Aplicar configuraÃ§Ã£o de UI
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Configuration", CallInEditor)
    bool ApplyUIConfiguration(const FAuracronUIConfiguration& Configuration);

    /**
     * Salvar configuraÃ§Ãµes de UI
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Configuration", CallInEditor)
    bool SaveUISettings();

    /**
     * Carregar configuraÃ§Ãµes de UI
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Configuration", CallInEditor)
    bool LoadUISettings();

    /**
     * Resetar configuraÃ§Ãµes para padrÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON UI|Configuration", CallInEditor)
    bool ResetToDefaultSettings();

protected:
    // === Internal Management Methods ===

    /** Inicializar sistema de UI */
    bool InitializeUISystem();

    /** Configurar Common UI */
    bool SetupCommonUI();

    /** Configurar input cross-platform */
    bool SetupCrossPlatformInput();

    /** Atualizar layout responsivo */
    bool UpdateResponsiveLayout();

    /** Processar animaÃ§Ãµes de UI */
    void ProcessUIAnimations(float DeltaTime);

    /** Validar configuraÃ§Ã£o de UI */
    bool ValidateUIConfiguration(const FAuracronUIConfiguration& Configuration) const;

    /** Criar widget de UI */
    UCommonUserWidget* CreateUIWidget(EAuracronUIType UIType, TSubclassOf<UCommonUserWidget> WidgetClass);

    /** Destruir widget de UI */
    bool DestroyUIWidget(EAuracronUIType UIType);

public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ã£o geral de UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    FAuracronUIConfiguration UIConfiguration;

    /** ConfiguraÃ§Ã£o do minimapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronMinimapConfiguration MinimapConfiguration;

    /** ConfiguraÃ§Ã£o do HUD de combate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronCombatHUDConfiguration CombatHUDConfiguration;

    /** ConfiguraÃ§Ã£o de input cross-platform */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronCrossPlatformInputConfiguration CrossPlatformInputConfiguration;

    /** Widgets de UI ativos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TMap<EAuracronUIType, TObjectPtr<UCommonUserWidget>> ActiveUIWidgets;

    /** Estados de UI */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_UIStates)
    TArray<FAuracronUIStateEntry> UIStates;

    /** Plataforma de input atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_InputPlatform)
    EAuracronInputPlatform CurrentInputPlatform = EAuracronInputPlatform::Auto;

    /** ReferÃªncia ao Common Input Subsystem */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UCommonInputSubsystem> CommonInputSubsystem;

    /** ReferÃªncia ao Enhanced Input Component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UEnhancedInputComponent> EnhancedInputComponent;

private:
    // === Internal State ===

    /** Sistema inicializado */
    bool bSystemInitialized = false;

    /** Classes de widgets registradas */
    TMap<EAuracronUIType, TSubclassOf<UCommonUserWidget>> RegisteredWidgetClasses;

    /** Timer para animaÃ§Ãµes */
    FTimerHandle AnimationTimer;

    /** Timer para atualizaÃ§Ã£o do minimapa */
    FTimerHandle MinimapUpdateTimer;

    /** Mutex para thread safety */
    mutable FCriticalSection UIMutex;

    // === Replication Callbacks ===

    UFUNCTION()
    void OnRep_UIStates();

    UFUNCTION()
    void OnRep_InputPlatform();

public:
    // === Delegates ===

    /** Delegate chamado quando UI Ã© mostrada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnUIShown, EAuracronUIType, UIType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON UI|Events")
    FOnUIShown OnUIShown;

    /** Delegate chamado quando UI Ã© escondida */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnUIHidden, EAuracronUIType, UIType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON UI|Events")
    FOnUIHidden OnUIHidden;

    /** Delegate chamado quando plataforma de input muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInputPlatformChanged, EAuracronInputPlatform, OldPlatform, EAuracronInputPlatform, NewPlatform);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON UI|Events")
    FOnInputPlatformChanged OnInputPlatformChanged;

    /** Delegate chamado quando configuraÃ§Ã£o de UI Ã© atualizada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnUIConfigurationUpdated, FAuracronUIConfiguration, NewConfiguration);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON UI|Events")
    FOnUIConfigurationUpdated OnUIConfigurationUpdated;

    /** Delegate chamado quando minimapa Ã© atualizado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnMinimapUpdated);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON UI|Events")
    FOnMinimapUpdated OnMinimapUpdated;
};

