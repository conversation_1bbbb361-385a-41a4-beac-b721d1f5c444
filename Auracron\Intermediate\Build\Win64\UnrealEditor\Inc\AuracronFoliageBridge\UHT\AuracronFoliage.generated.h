// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliage.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliage_generated_h
#error "AuracronFoliage.generated.h already included, missing '#pragma once' in AuracronFoliage.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliage_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronFoliageManager;
class UPrimitiveComponent;
class UWorld;
struct FAuracronFoliageConfiguration;
struct FAuracronFoliageInstanceData;
struct FAuracronFoliageTypeData;

// ********** Begin ScriptStruct FAuracronFoliageConfiguration *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_125_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliageConfiguration;
// ********** End ScriptStruct FAuracronFoliageConfiguration ***************************************

// ********** Begin ScriptStruct FAuracronFoliageInstanceData **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_259_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliageInstanceData;
// ********** End ScriptStruct FAuracronFoliageInstanceData ****************************************

// ********** Begin ScriptStruct FAuracronFoliageTypeData ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_310_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliageTypeData;
// ********** End ScriptStruct FAuracronFoliageTypeData ********************************************

// ********** Begin Delegate FOnFoliageInstanceSpawned *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_504_DELEGATE \
static void FOnFoliageInstanceSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageInstanceSpawned, const FString& TypeId, const FString& InstanceId);


// ********** End Delegate FOnFoliageInstanceSpawned ***********************************************

// ********** Begin Delegate FOnFoliageInstanceRemoved *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_505_DELEGATE \
static void FOnFoliageInstanceRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageInstanceRemoved, const FString& TypeId, const FString& InstanceId);


// ********** End Delegate FOnFoliageInstanceRemoved ***********************************************

// ********** Begin Delegate FOnFoliageTypeRegistered **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_506_DELEGATE \
static void FOnFoliageTypeRegistered_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageTypeRegistered, const FString& TypeId, FAuracronFoliageTypeData TypeData);


// ********** End Delegate FOnFoliageTypeRegistered ************************************************

// ********** Begin Delegate FOnFoliageOptimized ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_507_DELEGATE \
static void FOnFoliageOptimized_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageOptimized, int32 OptimizedInstances);


// ********** End Delegate FOnFoliageOptimized *****************************************************

// ********** Begin Class UAuracronFoliageManager **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_379_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDrawDebugVisualization); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetInstanceStatistics); \
	DECLARE_FUNCTION(execGetMemoryUsageMB); \
	DECLARE_FUNCTION(execGetVisibleInstanceCount); \
	DECLARE_FUNCTION(execGetInstanceCountForType); \
	DECLARE_FUNCTION(execGetTotalInstanceCount); \
	DECLARE_FUNCTION(execRebuildInstancedComponents); \
	DECLARE_FUNCTION(execBatchInstances); \
	DECLARE_FUNCTION(execOptimizeInstances); \
	DECLARE_FUNCTION(execSetInstanceVisibility); \
	DECLARE_FUNCTION(execSetInstanceLOD); \
	DECLARE_FUNCTION(execUpdateCulling); \
	DECLARE_FUNCTION(execUpdateLOD); \
	DECLARE_FUNCTION(execClearAllFoliage); \
	DECLARE_FUNCTION(execClearFoliageInArea); \
	DECLARE_FUNCTION(execGenerateFoliageOnSurface); \
	DECLARE_FUNCTION(execGenerateFoliageInArea); \
	DECLARE_FUNCTION(execUpdateFoliageInstance); \
	DECLARE_FUNCTION(execGetInstancesInRadius); \
	DECLARE_FUNCTION(execGetInstancesOfType); \
	DECLARE_FUNCTION(execGetFoliageInstance); \
	DECLARE_FUNCTION(execRemoveFoliageInstance); \
	DECLARE_FUNCTION(execSpawnFoliageInstance); \
	DECLARE_FUNCTION(execUpdateFoliageType); \
	DECLARE_FUNCTION(execGetAllFoliageTypes); \
	DECLARE_FUNCTION(execGetFoliageType); \
	DECLARE_FUNCTION(execUnregisterFoliageType); \
	DECLARE_FUNCTION(execRegisterFoliageType); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_379_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_379_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageManager(UAuracronFoliageManager&&) = delete; \
	UAuracronFoliageManager(const UAuracronFoliageManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageManager) \
	NO_API virtual ~UAuracronFoliageManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_376_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_379_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_379_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_379_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h_379_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageManager;

// ********** End Class UAuracronFoliageManager ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h

// ********** Begin Enum EAuracronFoliagePlacementMode *********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGEPLACEMENTMODE(op) \
	op(EAuracronFoliagePlacementMode::Manual) \
	op(EAuracronFoliagePlacementMode::Procedural) \
	op(EAuracronFoliagePlacementMode::Hybrid) \
	op(EAuracronFoliagePlacementMode::PCGDriven) 

enum class EAuracronFoliagePlacementMode : uint8;
template<> struct TIsUEnumClass<EAuracronFoliagePlacementMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliagePlacementMode>();
// ********** End Enum EAuracronFoliagePlacementMode ***********************************************

// ********** Begin Enum EAuracronFoliageDensityMode ***********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGEDENSITYMODE(op) \
	op(EAuracronFoliageDensityMode::Uniform) \
	op(EAuracronFoliageDensityMode::Noise) \
	op(EAuracronFoliageDensityMode::Texture) \
	op(EAuracronFoliageDensityMode::Biome) \
	op(EAuracronFoliageDensityMode::Custom) 

enum class EAuracronFoliageDensityMode : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageDensityMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageDensityMode>();
// ********** End Enum EAuracronFoliageDensityMode *************************************************

// ********** Begin Enum EAuracronFoliageScalingMode ***********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGESCALINGMODE(op) \
	op(EAuracronFoliageScalingMode::Uniform) \
	op(EAuracronFoliageScalingMode::Free) \
	op(EAuracronFoliageScalingMode::LockXY) \
	op(EAuracronFoliageScalingMode::LockXZ) \
	op(EAuracronFoliageScalingMode::LockYZ) 

enum class EAuracronFoliageScalingMode : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageScalingMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageScalingMode>();
// ********** End Enum EAuracronFoliageScalingMode *************************************************

// ********** Begin Enum EAuracronFoliageInstanceState *********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGEINSTANCESTATE(op) \
	op(EAuracronFoliageInstanceState::Active) \
	op(EAuracronFoliageInstanceState::Hidden) \
	op(EAuracronFoliageInstanceState::Culled) \
	op(EAuracronFoliageInstanceState::Destroyed) \
	op(EAuracronFoliageInstanceState::Pending) 

enum class EAuracronFoliageInstanceState : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageInstanceState> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageInstanceState>();
// ********** End Enum EAuracronFoliageInstanceState ***********************************************

// ********** Begin Enum EAuracronFoliageLODMode ***************************************************
#define FOREACH_ENUM_EAURACRONFOLIAGELODMODE(op) \
	op(EAuracronFoliageLODMode::Distance) \
	op(EAuracronFoliageLODMode::ScreenSize) \
	op(EAuracronFoliageLODMode::Performance) \
	op(EAuracronFoliageLODMode::Quality) \
	op(EAuracronFoliageLODMode::Custom) 

enum class EAuracronFoliageLODMode : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageLODMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageLODMode>();
// ********** End Enum EAuracronFoliageLODMode *****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
