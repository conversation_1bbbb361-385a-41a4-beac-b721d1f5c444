// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronAbismoUmbrioBridge_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronAbismoUmbrioBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronAbismoUmbrioBridge.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronAbismoUmbrioBridge",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0xDBD5D9B0,
				0x744FEED1,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronAbismoUmbrioBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronAbismoUmbrioBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronAbismoUmbrioBridge(Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge, TEXT("/Script/AuracronAbismoUmbrioBridge"), Z_Registration_Info_UPackage__Script_AuracronAbismoUmbrioBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xDBD5D9B0, 0x744FEED1));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
