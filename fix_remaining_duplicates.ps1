# Script para corrigir duplicados restantes

Write-Host "Corrigindo duplicados restantes..."

# 1. Renomear FAuracronStreamingStatistics para FAuracronStreamingBridgeStatistics em AuracronWorldPartitionStreaming.h
$file1 = "Auracron\Source\AuracronWorldPartitionBridge\Public\AuracronWorldPartitionStreaming.h"
$content1 = Get-Content $file1 -Raw
$content1 = $content1 -replace "struct FAuracronStreamingStatistics", "struct FAuracronStreamingBridgeStatistics"
$content1 = $content1 -replace "FAuracronStreamingStatistics ", "FAuracronStreamingBridgeStatistics "
Set-Content -Path $file1 -Value $content1 -NoNewline
Write-Host "Renomeado FAuracronStreamingStatistics para FAuracronStreamingBridgeStatistics"

# 2. Renomear EAuracronLODTransitionType para EAuracronWorldPartitionLODTransitionType em AuracronWorldPartitionLOD.h
$file2 = "Auracron\Source\AuracronWorldPartitionBridge\Public\AuracronWorldPartitionLOD.h"
$content2 = Get-Content $file2 -Raw
$content2 = $content2 -replace "enum class EAuracronLODTransitionType", "enum class EAuracronWorldPartitionLODTransitionType"
$content2 = $content2 -replace "EAuracronLODTransitionType::", "EAuracronWorldPartitionLODTransitionType::"
$content2 = $content2 -replace "EAuracronLODTransitionType ", "EAuracronWorldPartitionLODTransitionType "
Set-Content -Path $file2 -Value $content2 -NoNewline
Write-Host "Renomeado EAuracronLODTransitionType para EAuracronWorldPartitionLODTransitionType"

# 3. Renomear FAuracronLODConfiguration para FAuracronWorldPartitionLODConfiguration em AuracronWorldPartitionLOD.h
$content2 = Get-Content $file2 -Raw
$content2 = $content2 -replace "struct FAuracronLODConfiguration", "struct FAuracronWorldPartitionLODConfiguration"
$content2 = $content2 -replace "FAuracronLODConfiguration ", "FAuracronWorldPartitionLODConfiguration "
Set-Content -Path $file2 -Value $content2 -NoNewline
Write-Host "Renomeado FAuracronLODConfiguration para FAuracronWorldPartitionLODConfiguration"

# 4. Renomear UAuracronPCGAttributeModifierSettings para UAuracronPCGExtendedAttributeModifierSettings em AuracronPCGElementLibraryExtended.h
$file3 = "Auracron\Source\AuracronPCGBridge\Public\AuracronPCGElementLibraryExtended.h"
$content3 = Get-Content $file3 -Raw
$content3 = $content3 -replace "class UAuracronPCGAttributeModifierSettings", "class UAuracronPCGExtendedAttributeModifierSettings"
$content3 = $content3 -replace "UAuracronPCGAttributeModifierSettings ", "UAuracronPCGExtendedAttributeModifierSettings "
Set-Content -Path $file3 -Value $content3 -NoNewline
Write-Host "Renomeado UAuracronPCGAttributeModifierSettings para UAuracronPCGExtendedAttributeModifierSettings"

# 5. Renomear EAuracronPCGLODGenerationMode para EAuracronPCGMeshLODGenerationMode em AuracronPCGMeshGeneration.h
$file4 = "Auracron\Source\AuracronPCGBridge\Public\AuracronPCGMeshGeneration.h"
$content4 = Get-Content $file4 -Raw
$content4 = $content4 -replace "enum class EAuracronPCGLODGenerationMode", "enum class EAuracronPCGMeshLODGenerationMode"
$content4 = $content4 -replace "EAuracronPCGLODGenerationMode::", "EAuracronPCGMeshLODGenerationMode::"
$content4 = $content4 -replace "EAuracronPCGLODGenerationMode ", "EAuracronPCGMeshLODGenerationMode "
Set-Content -Path $file4 -Value $content4 -NoNewline
Write-Host "Renomeado EAuracronPCGLODGenerationMode para EAuracronPCGMeshLODGenerationMode"

# 6. Renomear FAuracronPCGMeshEntry para FAuracronPCGMeshGenerationEntry em AuracronPCGMeshGeneration.h
$content4 = Get-Content $file4 -Raw
$content4 = $content4 -replace "struct FAuracronPCGMeshEntry", "struct FAuracronPCGMeshGenerationEntry"
$content4 = $content4 -replace "FAuracronPCGMeshEntry ", "FAuracronPCGMeshGenerationEntry "
Set-Content -Path $file4 -Value $content4 -NoNewline
Write-Host "Renomeado FAuracronPCGMeshEntry para FAuracronPCGMeshGenerationEntry"

Write-Host "Correções de duplicados restantes aplicadas com sucesso!"
