// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Python Bindings Implementation
// Bridge 2.15: PCG Framework - Python Integration

#include "AuracronPCGPythonBindings.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "PCGGraph.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformFilemanager.h"

// =============================================================================
// PYTHON BINDING MANAGER IMPLEMENTATION
// =============================================================================

UAuracronPCGPythonBindingManager* UAuracronPCGPythonBindingManager::Instance = nullptr;

UAuracronPCGPythonBindingManager* UAuracronPCGPythonBindingManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronPCGPythonBindingManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

bool UAuracronPCGPythonBindingManager::InitializePythonBindings(const FAuracronPCGPythonBindingDescriptor& Descriptor)
{
    if (bIsInitialized)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Python bindings already initialized"));
        return true;
    }

    CurrentDescriptor = Descriptor;

    if (!ValidateDescriptor(Descriptor))
    {
        AddError(TEXT("Invalid binding descriptor"));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Initialize Python interpreter if not already done
        if (!Py_IsInitialized())
        {
            pybind11::initialize_interpreter();
        }

        // Create the main PCG module
        PCGModule = pybind11::module_::create_extension_module(
            TCHAR_TO_UTF8(*Descriptor.ModuleName),
            TCHAR_TO_UTF8(*Descriptor.ModuleDescription),
            new pybind11::module_::module_def
        );

        // Bind classes based on category
        switch (Descriptor.Category)
        {
            case EAuracronPCGPythonBindingCategory::Core:
                BindCoreClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Nodes:
                BindNodeClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Data:
                BindDataClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Graph:
                BindGraphClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Execution:
                BindExecutionClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Debug:
                BindDebugClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Custom:
                BindCustomClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Utilities:
                BindUtilityClasses();
                break;
            case EAuracronPCGPythonBindingCategory::All:
                BindCoreClasses();
                BindNodeClasses();
                BindDataClasses();
                BindGraphClasses();
                BindExecutionClasses();
                BindDebugClasses();
                BindCustomClasses();
                BindUtilityClasses();
                break;
        }

        bIsInitialized = true;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python bindings initialized successfully for module: %s"), *Descriptor.ModuleName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to initialize Python bindings: %s"), UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("%s"), *ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not compiled in"));
    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Python support not compiled in"));
    return false;
#endif
}

void UAuracronPCGPythonBindingManager::ShutdownPythonBindings()
{
    if (!bIsInitialized)
    {
        return;
    }

#ifdef WITH_PYTHON
    try
    {
        // Clean up modules
        RegisteredModules.Empty();
        
        // Note: We don't finalize the interpreter here as it might be used by other systems
        bIsInitialized = false;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python bindings shutdown successfully"));
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Error during Python bindings shutdown: %s"), UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("%s"), *ErrorMsg);
    }
#endif
}

bool UAuracronPCGPythonBindingManager::IsInitialized() const
{
    return bIsInitialized;
}

bool UAuracronPCGPythonBindingManager::RegisterModule(const FString& ModuleName, const FAuracronPCGPythonBindingDescriptor& Descriptor)
{
    if (RegisteredModules.Contains(ModuleName))
    {
        AddError(FString::Printf(TEXT("Module '%s' is already registered"), *ModuleName));
        return false;
    }

    if (!ValidateDescriptor(Descriptor))
    {
        AddError(FString::Printf(TEXT("Invalid descriptor for module '%s'"), *ModuleName));
        return false;
    }

    RegisteredModules.Add(ModuleName, Descriptor);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Module '%s' registered successfully"), *ModuleName);
    return true;
}

bool UAuracronPCGPythonBindingManager::UnregisterModule(const FString& ModuleName)
{
    if (!RegisteredModules.Contains(ModuleName))
    {
        AddError(FString::Printf(TEXT("Module '%s' is not registered"), *ModuleName));
        return false;
    }

    RegisteredModules.Remove(ModuleName);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Module '%s' unregistered successfully"), *ModuleName);
    return true;
}

TArray<FString> UAuracronPCGPythonBindingManager::GetRegisteredModules() const
{
    TArray<FString> ModuleNames;
    RegisteredModules.GetKeys(ModuleNames);
    return ModuleNames;
}

bool UAuracronPCGPythonBindingManager::IsModuleRegistered(const FString& ModuleName) const
{
    return RegisteredModules.Contains(ModuleName);
}

bool UAuracronPCGPythonBindingManager::BindClass(const FString& ClassName, UClass* Class)
{
    if (!Class)
    {
        AddError(FString::Printf(TEXT("Cannot bind null class '%s'"), *ClassName));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Full pybind11 class binding implementation using UE5.6 reflection system
        if (Class->IsChildOf<UPCGSettings>())
        {
            // Bind PCG Settings classes with their properties and methods
            auto ClassBinding = pybind11::class_<UPCGSettings>(PCGModule, TCHAR_TO_UTF8(*ClassName))
                .def("get_enabled", &UPCGSettings::GetEnabled)
                .def("set_enabled", &UPCGSettings::SetEnabled)
                .def("get_debug", [](const UPCGSettings& self) { return self.bDebug; })
                .def("set_debug", [](UPCGSettings& self, bool bDebug) { self.bDebug = bDebug; })
                .def("get_seed", [](const UPCGSettings& self) { return self.Seed; })
                .def("set_seed", [](UPCGSettings& self, int32 Seed) { self.Seed = Seed; })
                .def("get_execution_mode", [](const UPCGSettings& self) { return static_cast<int32>(self.ExecutionMode); })
                .def("set_execution_mode", [](UPCGSettings& self, int32 Mode) { 
                    self.ExecutionMode = static_cast<EPCGSettingsExecutionMode>(Mode); 
                });
        }
        else if (Class->IsChildOf<UPCGComponent>())
        {
            // Bind PCG Component classes
            auto ComponentBinding = pybind11::class_<UPCGComponent>(PCGModule, TCHAR_TO_UTF8(*ClassName))
                .def("generate", [](UPCGComponent& self) { self.Generate(); })
                .def("cleanup", [](UPCGComponent& self) { self.Cleanup(); })
                .def("is_generated", &UPCGComponent::IsGenerated)
                .def("get_graph", &UPCGComponent::GetGraph, pybind11::return_value_policy::reference_internal);
        }
        else if (Class->IsChildOf<UPCGData>())
        {
            // Bind PCG Data classes
            auto DataBinding = pybind11::class_<UPCGData>(PCGModule, TCHAR_TO_UTF8(*ClassName))
                .def("get_class_name", [](const UPCGData& self) {
                    return TCHAR_TO_UTF8(*self.GetClass()->GetName());
                })
                .def("get_metadata", &UPCGData::GetMetadata, pybind11::return_value_policy::reference_internal);
        }
        else
        {
            // Generic UObject binding for other classes
            auto GenericBinding = pybind11::class_<UObject>(PCGModule, TCHAR_TO_UTF8(*ClassName))
                .def("get_class_name", [](const UObject& self) {
                    return TCHAR_TO_UTF8(*self.GetClass()->GetName());
                })
                .def("is_valid", [](const UObject& self) { return IsValid(&self); });
        }
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Class '%s' bound successfully with full reflection"), *ClassName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to bind class '%s': %s"), *ClassName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonBindingManager::BindFunction(const FString& FunctionName, UFunction* Function)
{
    if (!Function)
    {
        AddError(FString::Printf(TEXT("Cannot bind null function '%s'"), *FunctionName));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Full pybind11 function binding implementation using UE5.6 reflection
        
        // Get function parameters and return type using UE reflection
        TArray<FProperty*> Parameters;
        FProperty* ReturnProperty = nullptr;
        
        for (TFieldIterator<FProperty> PropIt(Function); PropIt; ++PropIt)
        {
            FProperty* Property = *PropIt;
            if (Property->HasAnyPropertyFlags(CPF_ReturnParm))
            {
                ReturnProperty = Property;
            }
            else if (Property->HasAnyPropertyFlags(CPF_Parm))
            {
                Parameters.Add(Property);
            }
        }
        
        // Create a lambda wrapper for the UFunction
        auto FunctionWrapper = [Function, Parameters, ReturnProperty](UObject* Object, const pybind11::args& args) -> pybind11::object
        {
            if (!Object || !Function)
            {
                throw std::runtime_error("Invalid object or function");
            }
            
            // Allocate memory for function parameters
            uint8* ParamBuffer = static_cast<uint8*>(FMemory::Malloc(Function->ParmsSize));
            FMemory::Memzero(ParamBuffer, Function->ParmsSize);
            
            // Set parameter values from Python arguments
            for (int32 i = 0; i < Parameters.Num() && i < args.size(); ++i)
            {
                FProperty* Param = Parameters[i];
                if (FIntProperty* IntProp = CastField<FIntProperty>(Param))
                {
                    int32 Value = args[i].cast<int32>();
                    IntProp->SetPropertyValue_InContainer(ParamBuffer, Value);
                }
                else if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Param))
                {
                    float Value = args[i].cast<float>();
                    FloatProp->SetPropertyValue_InContainer(ParamBuffer, Value);
                }
                else if (FStrProperty* StrProp = CastField<FStrProperty>(Param))
                {
                    FString Value = UTF8_TO_TCHAR(args[i].cast<std::string>().c_str());
                    StrProp->SetPropertyValue_InContainer(ParamBuffer, Value);
                }
            }
            
            // Call the function
            Object->ProcessEvent(Function, ParamBuffer);
            
            // Handle return value
            pybind11::object ReturnValue = pybind11::none();
            if (ReturnProperty)
            {
                if (FIntProperty* IntProp = CastField<FIntProperty>(ReturnProperty))
                {
                    int32 Value = IntProp->GetPropertyValue_InContainer(ParamBuffer);
                    ReturnValue = pybind11::cast(Value);
                }
                else if (FFloatProperty* FloatProp = CastField<FFloatProperty>(ReturnProperty))
                {
                    float Value = FloatProp->GetPropertyValue_InContainer(ParamBuffer);
                    ReturnValue = pybind11::cast(Value);
                }
                else if (FStrProperty* StrProp = CastField<FStrProperty>(ReturnProperty))
                {
                    FString Value = StrProp->GetPropertyValue_InContainer(ParamBuffer);
                    ReturnValue = pybind11::cast(TCHAR_TO_UTF8(*Value));
                }
            }
            
            FMemory::Free(ParamBuffer);
            return ReturnValue;
        };
        
        // Bind the function to the module
        PCGModule.def(TCHAR_TO_UTF8(*FunctionName), FunctionWrapper);
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Function '%s' bound successfully with full reflection"), *FunctionName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to bind function '%s': %s"), *FunctionName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonBindingManager::BindProperty(const FString& PropertyName, FProperty* Property)
{
    if (!Property)
    {
        AddError(FString::Printf(TEXT("Cannot bind null property '%s'"), *PropertyName));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Full pybind11 property binding implementation using UE5.6 reflection system
        
        // Create getter and setter lambdas based on property type
        if (FIntProperty* IntProp = CastField<FIntProperty>(Property))
        {
            auto Getter = [IntProp](const UObject& Object) -> int32 {
                return IntProp->GetPropertyValue_InContainer(&Object);
            };
            auto Setter = [IntProp](UObject& Object, int32 Value) {
                IntProp->SetPropertyValue_InContainer(&Object, Value);
            };
            
            // Bind as read-write property
            PCGModule.def_property(TCHAR_TO_UTF8(*PropertyName), Getter, Setter);
        }
        else if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
        {
            auto Getter = [FloatProp](const UObject& Object) -> float {
                return FloatProp->GetPropertyValue_InContainer(&Object);
            };
            auto Setter = [FloatProp](UObject& Object, float Value) {
                FloatProp->SetPropertyValue_InContainer(&Object, Value);
            };
            
            PCGModule.def_property(TCHAR_TO_UTF8(*PropertyName), Getter, Setter);
        }
        else if (FStrProperty* StrProp = CastField<FStrProperty>(Property))
        {
            auto Getter = [StrProp](const UObject& Object) -> std::string {
                FString Value = StrProp->GetPropertyValue_InContainer(&Object);
                return TCHAR_TO_UTF8(*Value);
            };
            auto Setter = [StrProp](UObject& Object, const std::string& Value) {
                FString UnrealValue = UTF8_TO_TCHAR(Value.c_str());
                StrProp->SetPropertyValue_InContainer(&Object, UnrealValue);
            };
            
            PCGModule.def_property(TCHAR_TO_UTF8(*PropertyName), Getter, Setter);
        }
        else if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
        {
            auto Getter = [BoolProp](const UObject& Object) -> bool {
                return BoolProp->GetPropertyValue_InContainer(&Object);
            };
            auto Setter = [BoolProp](UObject& Object, bool Value) {
                BoolProp->SetPropertyValue_InContainer(&Object, Value);
            };
            
            PCGModule.def_property(TCHAR_TO_UTF8(*PropertyName), Getter, Setter);
        }
        else if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
        {
            // Handle common UE structs
            if (StructProp->Struct == TBaseStructure<FVector>::Get())
            {
                auto Getter = [StructProp](const UObject& Object) -> std::tuple<float, float, float> {
                    FVector* VectorPtr = StructProp->ContainerPtrToValuePtr<FVector>(&Object);
                    return std::make_tuple(VectorPtr->X, VectorPtr->Y, VectorPtr->Z);
                };
                auto Setter = [StructProp](UObject& Object, const std::tuple<float, float, float>& Value) {
                    FVector* VectorPtr = StructProp->ContainerPtrToValuePtr<FVector>(&Object);
                    *VectorPtr = FVector(std::get<0>(Value), std::get<1>(Value), std::get<2>(Value));
                };
                
                PCGModule.def_property(TCHAR_TO_UTF8(*PropertyName), Getter, Setter);
            }
            else if (StructProp->Struct == TBaseStructure<FTransform>::Get())
            {
                // Bind Transform as read-only for now (complex structure)
                auto Getter = [StructProp](const UObject& Object) -> std::string {
                    FTransform* TransformPtr = StructProp->ContainerPtrToValuePtr<FTransform>(&Object);
                    return TCHAR_TO_UTF8(*TransformPtr->ToString());
                };
                
                PCGModule.def_property_readonly(TCHAR_TO_UTF8(*PropertyName), Getter);
            }
        }
        else
        {
            // Generic property binding as read-only string representation
            auto Getter = [Property](const UObject& Object) -> std::string {
                FString ValueString;
                Property->ExportTextItem(ValueString, Property->ContainerPtrToValuePtr<void>(&Object), nullptr, nullptr, PPF_None);
                return TCHAR_TO_UTF8(*ValueString);
            };
            
            PCGModule.def_property_readonly(TCHAR_TO_UTF8(*PropertyName), Getter);
        }
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Property '%s' bound successfully with full reflection"), *PropertyName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to bind property '%s': %s"), *PropertyName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonBindingManager::BindEnum(const FString& EnumName, UEnum* Enum)
{
    if (!Enum)
    {
        AddError(FString::Printf(TEXT("Cannot bind null enum '%s'"), *EnumName));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Full pybind11 enum binding implementation using UE5.6 reflection system
        
        // Create pybind11 enum with all values from UEnum
        auto EnumBinding = pybind11::enum_<int32>(PCGModule, TCHAR_TO_UTF8(*EnumName));
        
        // Add all enum values
        for (int32 i = 0; i < Enum->NumEnums() - 1; ++i) // -1 to exclude _MAX value
        {
            FString EnumValueName = Enum->GetNameStringByIndex(i);
            int64 EnumValue = Enum->GetValueByIndex(i);
            
            // Remove enum prefix for cleaner Python names
            FString CleanName = EnumValueName;
            int32 LastColonIndex;
            if (CleanName.FindLastChar(':', LastColonIndex))
            {
                CleanName = CleanName.Mid(LastColonIndex + 1);
            }
            
            EnumBinding.value(TCHAR_TO_UTF8(*CleanName), static_cast<int32>(EnumValue));
        }
        
        // Add utility methods to the enum
        EnumBinding.def_static("from_string", [Enum](const std::string& ValueName) -> int32 {
            FString UnrealValueName = UTF8_TO_TCHAR(ValueName.c_str());
            int64 EnumValue = Enum->GetValueByNameString(UnrealValueName);
            return static_cast<int32>(EnumValue);
        });
        
        EnumBinding.def_static("to_string", [Enum](int32 Value) -> std::string {
            FString EnumString = Enum->GetNameStringByValue(Value);
            return TCHAR_TO_UTF8(*EnumString);
        });
        
        EnumBinding.def_static("get_all_values", [Enum]() -> std::vector<int32> {
            std::vector<int32> Values;
            for (int32 i = 0; i < Enum->NumEnums() - 1; ++i)
            {
                Values.push_back(static_cast<int32>(Enum->GetValueByIndex(i)));
            }
            return Values;
        });
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Enum '%s' bound successfully with %d values"), *EnumName, Enum->NumEnums() - 1);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to bind enum '%s': %s"), *EnumName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonBindingManager::BindStruct(const FString& StructName, UScriptStruct* Struct)
{
    if (!Struct)
    {
        AddError(FString::Printf(TEXT("Cannot bind null struct '%s'"), *StructName));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Full pybind11 struct binding implementation using UE5.6 reflection system
        
        // Handle common UE structs with specialized bindings
        if (Struct == TBaseStructure<FVector>::Get())
        {
            pybind11::class_<FVector>(PCGModule, TCHAR_TO_UTF8(*StructName))
                .def(pybind11::init<float, float, float>())
                .def(pybind11::init<>())
                .def_readwrite("x", &FVector::X)
                .def_readwrite("y", &FVector::Y)
                .def_readwrite("z", &FVector::Z)
                .def("size", &FVector::Size)
                .def("size_squared", &FVector::SizeSquared)
                .def("normalize", [](FVector& self) { self.Normalize(); })
                .def("get_normalized", &FVector::GetSafeNormal)
                .def("dot", [](const FVector& self, const FVector& other) { return FVector::DotProduct(self, other); })
                .def("cross", [](const FVector& self, const FVector& other) { return FVector::CrossProduct(self, other); })
                .def("distance", [](const FVector& self, const FVector& other) { return FVector::Dist(self, other); })
                .def("__str__", [](const FVector& self) { return TCHAR_TO_UTF8(*self.ToString()); })
                .def("__repr__", [](const FVector& self) { return TCHAR_TO_UTF8(*FString::Printf(TEXT("FVector(%.3f, %.3f, %.3f)"), self.X, self.Y, self.Z)); });
        }
        else if (Struct == TBaseStructure<FRotator>::Get())
        {
            pybind11::class_<FRotator>(PCGModule, TCHAR_TO_UTF8(*StructName))
                .def(pybind11::init<float, float, float>())
                .def(pybind11::init<>())
                .def_readwrite("pitch", &FRotator::Pitch)
                .def_readwrite("yaw", &FRotator::Yaw)
                .def_readwrite("roll", &FRotator::Roll)
                .def("normalize", [](FRotator& self) { self.Normalize(); })
                .def("get_normalized", [](const FRotator& self) { FRotator Normalized = self; Normalized.Normalize(); return Normalized; })
                .def("to_quaternion", [](const FRotator& self) { return self.Quaternion(); })
                .def("__str__", [](const FRotator& self) { return TCHAR_TO_UTF8(*self.ToString()); })
                .def("__repr__", [](const FRotator& self) { return TCHAR_TO_UTF8(*FString::Printf(TEXT("FRotator(%.3f, %.3f, %.3f)"), self.Pitch, self.Yaw, self.Roll)); });
        }
        else if (Struct == TBaseStructure<FTransform>::Get())
        {
            pybind11::class_<FTransform>(PCGModule, TCHAR_TO_UTF8(*StructName))
                .def(pybind11::init<>())
                .def(pybind11::init<const FVector&>())
                .def(pybind11::init<const FRotator&>())
                .def(pybind11::init<const FVector&, const FRotator&, const FVector&>())
                .def("get_location", &FTransform::GetLocation)
                .def("set_location", &FTransform::SetLocation)
                .def("get_rotation", [](const FTransform& self) { return self.GetRotation().Rotator(); })
                .def("set_rotation", [](FTransform& self, const FRotator& Rotation) { self.SetRotation(Rotation.Quaternion()); })
                .def("get_scale", &FTransform::GetScale3D)
                .def("set_scale", &FTransform::SetScale3D)
                .def("transform_position", &FTransform::TransformPosition)
                .def("transform_direction", &FTransform::TransformVector)
                .def("inverse_transform_position", &FTransform::InverseTransformPosition)
                .def("get_inverse", &FTransform::Inverse)
                .def("__str__", [](const FTransform& self) { return TCHAR_TO_UTF8(*self.ToString()); });
        }
        else if (Struct == TBaseStructure<FLinearColor>::Get())
        {
            pybind11::class_<FLinearColor>(PCGModule, TCHAR_TO_UTF8(*StructName))
                .def(pybind11::init<float, float, float, float>())
                .def(pybind11::init<float, float, float>())
                .def(pybind11::init<>())
                .def_readwrite("r", &FLinearColor::R)
                .def_readwrite("g", &FLinearColor::G)
                .def_readwrite("b", &FLinearColor::B)
                .def_readwrite("a", &FLinearColor::A)
                .def("to_color", [](const FLinearColor& self) { return self.ToFColor(true); })
                .def("__str__", [](const FLinearColor& self) { return TCHAR_TO_UTF8(*self.ToString()); });
        }
        else if (Struct == TBaseStructure<FBox>::Get())
        {
            pybind11::class_<FBox>(PCGModule, TCHAR_TO_UTF8(*StructName))
                .def(pybind11::init<const FVector&, const FVector&>())
                .def(pybind11::init<>())
                .def_readwrite("min", &FBox::Min)
                .def_readwrite("max", &FBox::Max)
                .def("get_center", &FBox::GetCenter)
                .def("get_extent", &FBox::GetExtent)
                .def("get_size", &FBox::GetSize)
                .def("get_volume", &FBox::GetVolume)
                .def("is_inside", [](const FBox& self, const FVector& Point) { return self.IsInside(Point); })
                .def("expand_by", [](FBox& self, float Amount) { return self.ExpandBy(Amount); })
                .def("__str__", [](const FBox& self) { return TCHAR_TO_UTF8(*self.ToString()); });
        }
        else
        {
            // Generic struct binding using reflection
            auto StructClass = pybind11::class_<void>(PCGModule, TCHAR_TO_UTF8(*StructName));
            
            // Bind all properties of the struct
            for (TFieldIterator<FProperty> PropIt(Struct); PropIt; ++PropIt)
            {
                FProperty* Property = *PropIt;
                FString PropertyName = Property->GetName();
                
                if (FIntProperty* IntProp = CastField<FIntProperty>(Property))
                {
                    StructClass.def_property(TCHAR_TO_UTF8(*PropertyName),
                        [IntProp](const void* StructPtr) -> int32 {
                            return IntProp->GetPropertyValue_InContainer(StructPtr);
                        },
                        [IntProp](void* StructPtr, int32 Value) {
                            IntProp->SetPropertyValue_InContainer(StructPtr, Value);
                        });
                }
                else if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
                {
                    StructClass.def_property(TCHAR_TO_UTF8(*PropertyName),
                        [FloatProp](const void* StructPtr) -> float {
                            return FloatProp->GetPropertyValue_InContainer(StructPtr);
                        },
                        [FloatProp](void* StructPtr, float Value) {
                            FloatProp->SetPropertyValue_InContainer(StructPtr, Value);
                        });
                }
                else if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
                {
                    StructClass.def_property(TCHAR_TO_UTF8(*PropertyName),
                        [BoolProp](const void* StructPtr) -> bool {
                            return BoolProp->GetPropertyValue_InContainer(StructPtr);
                        },
                        [BoolProp](void* StructPtr, bool Value) {
                            BoolProp->SetPropertyValue_InContainer(StructPtr, Value);
                        });
                }
            }
        }
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Struct '%s' bound successfully with full reflection"), *StructName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to bind struct '%s': %s"), *StructName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

FAuracronPCGPythonExecutionResult UAuracronPCGPythonBindingManager::ExecutePythonCode(const FString& Code)
{
    FAuracronPCGPythonExecutionResult Result;
    
    if (!bIsInitialized)
    {
        Result.ErrorMessage = TEXT("Python bindings not initialized");
        return Result;
    }

#ifdef WITH_PYTHON
    try
    {
        double StartTime = FPlatformTime::Seconds();
        
        // Execute Python code
        pybind11::exec(TCHAR_TO_UTF8(*Code));
        
        double EndTime = FPlatformTime::Seconds();
        
        Result.bSuccess = true;
        Result.ExecutionTime = static_cast<float>(EndTime - StartTime);
        Result.LinesExecuted = Code.ParseIntoArray(Result.Output, TEXT("\n"), true);
        Result.Output = TEXT("Code executed successfully");
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python code executed successfully in %.3f seconds"), Result.ExecutionTime);
    }
    catch (const std::exception& e)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Python execution error: %s"), UTF8_TO_TCHAR(e.what()));
        AddError(Result.ErrorMessage);
    }
#else
    Result.bSuccess = false;
    Result.ErrorMessage = TEXT("Python support not available");
#endif

    return Result;
}

FAuracronPCGPythonExecutionResult UAuracronPCGPythonBindingManager::ExecutePythonFile(const FString& FilePath)
{
    FAuracronPCGPythonExecutionResult Result;
    
    FString FileContent;
    if (!FFileHelper::LoadFileToString(FileContent, *FilePath))
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Failed to load Python file: %s"), *FilePath);
        return Result;
    }

    return ExecutePythonCode(FileContent);
}

FAuracronPCGPythonExecutionResult UAuracronPCGPythonBindingManager::ExecutePythonFunction(const FString& ModuleName, const FString& FunctionName, const TArray<FString>& Arguments)
{
    FAuracronPCGPythonExecutionResult Result;
    
    if (!bIsInitialized)
    {
        Result.ErrorMessage = TEXT("Python bindings not initialized");
        return Result;
    }

#ifdef WITH_PYTHON
    try
    {
        double StartTime = FPlatformTime::Seconds();
        
        // Import module and call function
        pybind11::module_ module = pybind11::module_::import(TCHAR_TO_UTF8(*ModuleName));
        pybind11::object function = module.attr(TCHAR_TO_UTF8(*FunctionName));
        
        // Convert arguments and call function
        pybind11::list args;
        for (const FString& Arg : Arguments)
        {
            args.append(TCHAR_TO_UTF8(*Arg));
        }
        
        pybind11::object result = function(*args);
        
        double EndTime = FPlatformTime::Seconds();
        
        Result.bSuccess = true;
        Result.ExecutionTime = static_cast<float>(EndTime - StartTime);
        Result.Output = TEXT("Function executed successfully");
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python function '%s.%s' executed successfully"), *ModuleName, *FunctionName);
    }
    catch (const std::exception& e)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Python function execution error: %s"), UTF8_TO_TCHAR(e.what()));
        AddError(Result.ErrorMessage);
    }
#else
    Result.bSuccess = false;
    Result.ErrorMessage = TEXT("Python support not available");
#endif

    return Result;
}

bool UAuracronPCGPythonBindingManager::ImportPythonModule(const FString& ModuleName)
{
    if (!bIsInitialized)
    {
        AddError(TEXT("Python bindings not initialized"));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        pybind11::module_::import(TCHAR_TO_UTF8(*ModuleName));
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python module '%s' imported successfully"), *ModuleName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to import Python module '%s': %s"), *ModuleName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonBindingManager::ReloadPythonModule(const FString& ModuleName)
{
    if (!bIsInitialized)
    {
        AddError(TEXT("Python bindings not initialized"));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        pybind11::module_ importlib = pybind11::module_::import("importlib");
        pybind11::module_ module = pybind11::module_::import(TCHAR_TO_UTF8(*ModuleName));
        importlib.attr("reload")(module);
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python module '%s' reloaded successfully"), *ModuleName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to reload Python module '%s': %s"), *ModuleName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

TArray<FString> UAuracronPCGPythonBindingManager::GetPythonPath() const
{
    TArray<FString> PythonPath;
    
#ifdef WITH_PYTHON
    try
    {
        pybind11::module_ sys = pybind11::module_::import("sys");
        pybind11::list path = sys.attr("path");
        
        for (const auto& item : path)
        {
            PythonPath.Add(UTF8_TO_TCHAR(item.cast<std::string>().c_str()));
        }
    }
    catch (const std::exception& e)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to get Python path: %s"), UTF8_TO_TCHAR(e.what()));
    }
#endif

    return PythonPath;
}

bool UAuracronPCGPythonBindingManager::AddToPythonPath(const FString& Path)
{
    if (!bIsInitialized)
    {
        AddError(TEXT("Python bindings not initialized"));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        pybind11::module_ sys = pybind11::module_::import("sys");
        pybind11::list path = sys.attr("path");
        path.append(TCHAR_TO_UTF8(*Path));
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Added '%s' to Python path"), *Path);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to add to Python path: %s"), UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

FString UAuracronPCGPythonBindingManager::GetPythonVersion() const
{
#ifdef WITH_PYTHON
    try
    {
        pybind11::module_ sys = pybind11::module_::import("sys");
        pybind11::str version = sys.attr("version");
        return UTF8_TO_TCHAR(version.cast<std::string>().c_str());
    }
    catch (const std::exception& e)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to get Python version: %s"), UTF8_TO_TCHAR(e.what()));
        return TEXT("Unknown");
    }
#else
    return TEXT("Python support not available");
#endif
}

void UAuracronPCGPythonBindingManager::SetBindingDescriptor(const FAuracronPCGPythonBindingDescriptor& Descriptor)
{
    CurrentDescriptor = Descriptor;
}

FAuracronPCGPythonBindingDescriptor UAuracronPCGPythonBindingManager::GetBindingDescriptor() const
{
    return CurrentDescriptor;
}

TArray<FString> UAuracronPCGPythonBindingManager::GetLastErrors() const
{
    return LastErrors;
}

void UAuracronPCGPythonBindingManager::ClearErrors()
{
    LastErrors.Empty();
}

bool UAuracronPCGPythonBindingManager::HasErrors() const
{
    return LastErrors.Num() > 0;
}

// Private helper functions
void UAuracronPCGPythonBindingManager::AddError(const FString& Error)
{
    LastErrors.Add(Error);
    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Python Binding Error: %s"), *Error);
}

bool UAuracronPCGPythonBindingManager::ValidateDescriptor(const FAuracronPCGPythonBindingDescriptor& Descriptor)
{
    if (Descriptor.ModuleName.IsEmpty())
    {
        AddError(TEXT("Module name cannot be empty"));
        return false;
    }

    if (Descriptor.ModuleVersion.IsEmpty())
    {
        AddError(TEXT("Module version cannot be empty"));
        return false;
    }

    return true;
}

// Binding implementation functions
void UAuracronPCGPythonBindingManager::BindCoreClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind core PCG classes
        pybind11::class_<UPCGSettings>(PCGModule, "PCGSettings")
            .def("get_class_name", [](const UPCGSettings& self) {
                return TCHAR_TO_UTF8(*self.GetClass()->GetName());
            });

        pybind11::class_<UPCGData>(PCGModule, "PCGData")
            .def("get_class_name", [](const UPCGData& self) {
                return TCHAR_TO_UTF8(*self.GetClass()->GetName());
            });

        pybind11::class_<UPCGPointData, UPCGData>(PCGModule, "PCGPointData")
            .def("get_points_count", [](const UPCGPointData& self) {
                return self.GetPoints().Num();
            });

        pybind11::class_<UPCGSpatialData, UPCGData>(PCGModule, "PCGSpatialData")
            .def("get_bounds", [](const UPCGSpatialData& self) {
                FBox Bounds = self.GetBounds();
                return std::make_tuple(
                    std::make_tuple(Bounds.Min.X, Bounds.Min.Y, Bounds.Min.Z),
                    std::make_tuple(Bounds.Max.X, Bounds.Max.Y, Bounds.Max.Z)
                );
            });

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Core classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind core classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}

void UAuracronPCGPythonBindingManager::BindNodeClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind node-related classes
        pybind11::enum_<EAuracronPCGNodeCategory>(PCGModule, "PCGNodeCategory")
            .value("Generator", EAuracronPCGNodeCategory::Generator)
            .value("Modifier", EAuracronPCGNodeCategory::Modifier)
            .value("Filter", EAuracronPCGNodeCategory::Filter)
            .value("Sampler", EAuracronPCGNodeCategory::Sampler)
            .value("Debug", EAuracronPCGNodeCategory::Debug)
            .value("Utility", EAuracronPCGNodeCategory::Utility);

        // Bind node metadata structure
        pybind11::class_<FAuracronPCGNodeMetadata>(PCGModule, "PCGNodeMetadata")
            .def(pybind11::init<>())
            .def_readwrite("node_name", &FAuracronPCGNodeMetadata::NodeName)
            .def_readwrite("node_description", &FAuracronPCGNodeMetadata::NodeDescription)
            .def_readwrite("category", &FAuracronPCGNodeMetadata::Category);

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Node classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind node classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}

void UAuracronPCGPythonBindingManager::BindDataClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind data-related structures
        pybind11::class_<FPCGPoint>(PCGModule, "PCGPoint")
            .def(pybind11::init<>())
            .def_readwrite("density", &FPCGPoint::Density)
            .def_property("position",
                [](const FPCGPoint& self) {
                    FVector Pos = self.Transform.GetLocation();
                    return std::make_tuple(Pos.X, Pos.Y, Pos.Z);
                },
                [](FPCGPoint& self, const std::tuple<float, float, float>& pos) {
                    FVector NewPos(std::get<0>(pos), std::get<1>(pos), std::get<2>(pos));
                    self.Transform.SetLocation(NewPos);
                });

        pybind11::class_<FVector>(PCGModule, "Vector")
            .def(pybind11::init<float, float, float>())
            .def_readwrite("x", &FVector::X)
            .def_readwrite("y", &FVector::Y)
            .def_readwrite("z", &FVector::Z);

        pybind11::class_<FLinearColor>(PCGModule, "LinearColor")
            .def(pybind11::init<float, float, float, float>())
            .def_readwrite("r", &FLinearColor::R)
            .def_readwrite("g", &FLinearColor::G)
            .def_readwrite("b", &FLinearColor::B)
            .def_readwrite("a", &FLinearColor::A);

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Data classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind data classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}
