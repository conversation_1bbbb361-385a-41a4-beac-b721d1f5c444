// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Systems/AuracronMetaHumanClothingSystem.h"

#ifdef AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanClothingSystem_generated_h
#error "AuracronMetaHumanClothingSystem.generated.h already included, missing '#pragma once' in AuracronMetaHumanClothingSystem.h"
#endif
#define AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanClothingSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronMetaHumanFramework;
class UClothingAssetBase;
class UMaterialInstance;
class UMaterialInstanceDynamic;
class UMaterialInterface;
class USkeletalMesh;
class USkeletalMeshComponent;
enum class EAuracronClothingMaterial : uint8;
struct FAuracronClothingConfig;
struct FAuracronClothingPhysicsConfig;
struct FAuracronClothingResult;

// ********** Begin ScriptStruct FAuracronClothingPhysicsConfig ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_82_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronClothingPhysicsConfig;
// ********** End ScriptStruct FAuracronClothingPhysicsConfig **************************************

// ********** Begin ScriptStruct FAuracronClothingConfig *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_128_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronClothingConfig;
// ********** End ScriptStruct FAuracronClothingConfig *********************************************

// ********** Begin ScriptStruct FAuracronClothingResult *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_171_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronClothingResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronClothingResult;
// ********** End ScriptStruct FAuracronClothingResult *********************************************

// ********** Begin Delegate FAuracronClothingComplete *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_205_DELEGATE \
AURACRONMETAHUMANFRAMEWORK_API void FAuracronClothingComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronClothingComplete, FAuracronClothingResult const& Result, const FString& OperationID);


// ********** End Delegate FAuracronClothingComplete ***********************************************

// ********** Begin Class UAuracronMetaHumanClothingSystem *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_214_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetSupportedMaterialTypes); \
	DECLARE_FUNCTION(execGetSupportedClothingTypes); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execSimplifyClothingMesh); \
	DECLARE_FUNCTION(execGenerateClothingLODs); \
	DECLARE_FUNCTION(execOptimizeClothingPerformance); \
	DECLARE_FUNCTION(execValidateClothingConfiguration); \
	DECLARE_FUNCTION(execGetClothingStatistics); \
	DECLARE_FUNCTION(execAnalyzeClothingPerformance); \
	DECLARE_FUNCTION(execUpdateMaterialParameters); \
	DECLARE_FUNCTION(execApplyMaterialToClothing); \
	DECLARE_FUNCTION(execCreateClothingMaterial); \
	DECLARE_FUNCTION(execSetWindParameters); \
	DECLARE_FUNCTION(execResetClothingSimulation); \
	DECLARE_FUNCTION(execStopClothingSimulation); \
	DECLARE_FUNCTION(execStartClothingSimulation); \
	DECLARE_FUNCTION(execGetAsyncOperationResult); \
	DECLARE_FUNCTION(execIsAsyncOperationComplete); \
	DECLARE_FUNCTION(execCreateClothingAssetAsync); \
	DECLARE_FUNCTION(execUpdateClothingPhysics); \
	DECLARE_FUNCTION(execRemoveClothingFromMetaHuman); \
	DECLARE_FUNCTION(execApplyClothingToMetaHuman); \
	DECLARE_FUNCTION(execCreateClothingAsset);


AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanClothingSystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_214_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMetaHumanClothingSystem(); \
	friend struct Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanClothingSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMetaHumanClothingSystem, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronMetaHumanFramework"), Z_Construct_UClass_UAuracronMetaHumanClothingSystem_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMetaHumanClothingSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_214_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMetaHumanClothingSystem(UAuracronMetaHumanClothingSystem&&) = delete; \
	UAuracronMetaHumanClothingSystem(const UAuracronMetaHumanClothingSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMetaHumanClothingSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMetaHumanClothingSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronMetaHumanClothingSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_211_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_214_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_214_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_214_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h_214_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMetaHumanClothingSystem;

// ********** End Class UAuracronMetaHumanClothingSystem *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h

// ********** Begin Enum EAuracronClothingType *****************************************************
#define FOREACH_ENUM_EAURACRONCLOTHINGTYPE(op) \
	op(EAuracronClothingType::Shirt) \
	op(EAuracronClothingType::Pants) \
	op(EAuracronClothingType::Dress) \
	op(EAuracronClothingType::Jacket) \
	op(EAuracronClothingType::Coat) \
	op(EAuracronClothingType::Skirt) \
	op(EAuracronClothingType::Shoes) \
	op(EAuracronClothingType::Hat) \
	op(EAuracronClothingType::Gloves) \
	op(EAuracronClothingType::Scarf) \
	op(EAuracronClothingType::Cape) \
	op(EAuracronClothingType::Accessory) \
	op(EAuracronClothingType::Custom) 

enum class EAuracronClothingType : uint8;
template<> struct TIsUEnumClass<EAuracronClothingType> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronClothingType>();
// ********** End Enum EAuracronClothingType *******************************************************

// ********** Begin Enum EAuracronClothingMaterial *************************************************
#define FOREACH_ENUM_EAURACRONCLOTHINGMATERIAL(op) \
	op(EAuracronClothingMaterial::Cotton) \
	op(EAuracronClothingMaterial::Silk) \
	op(EAuracronClothingMaterial::Wool) \
	op(EAuracronClothingMaterial::Leather) \
	op(EAuracronClothingMaterial::Denim) \
	op(EAuracronClothingMaterial::Polyester) \
	op(EAuracronClothingMaterial::Linen) \
	op(EAuracronClothingMaterial::Velvet) \
	op(EAuracronClothingMaterial::Satin) \
	op(EAuracronClothingMaterial::Canvas) \
	op(EAuracronClothingMaterial::Rubber) \
	op(EAuracronClothingMaterial::Metal) \
	op(EAuracronClothingMaterial::Custom) 

enum class EAuracronClothingMaterial : uint8;
template<> struct TIsUEnumClass<EAuracronClothingMaterial> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronClothingMaterial>();
// ********** End Enum EAuracronClothingMaterial ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
