// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanFramework/Public/Systems/AuracronMetaHumanAnimationSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMetaHumanAnimationSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanAnimationSystem();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType();
AURACRONMETAHUMANFRAMEWORK_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAnimationParams();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAnimationResult();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFacialAnimConfig();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UAnimationAsset_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAnimSequence_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeleton_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanFramework();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronAnimationType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAnimationType;
static UEnum* EAuracronAnimationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAnimationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAnimationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronAnimationType"));
	}
	return Z_Registration_Info_UEnum_EAuracronAnimationType.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronAnimationType>()
{
	return EAuracronAnimationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Body.DisplayName", "Body Animation" },
		{ "Body.Name", "EAuracronAnimationType::Body" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Animation Processing Types\n */" },
#endif
		{ "Emotion.DisplayName", "Emotion Animation" },
		{ "Emotion.Name", "EAuracronAnimationType::Emotion" },
		{ "Eye.DisplayName", "Eye Animation" },
		{ "Eye.Name", "EAuracronAnimationType::Eye" },
		{ "Facial.DisplayName", "Facial Animation" },
		{ "Facial.Name", "EAuracronAnimationType::Facial" },
		{ "Gesture.DisplayName", "Gesture Animation" },
		{ "Gesture.Name", "EAuracronAnimationType::Gesture" },
		{ "Hand.DisplayName", "Hand Animation" },
		{ "Hand.Name", "EAuracronAnimationType::Hand" },
		{ "Idle.DisplayName", "Idle Animation" },
		{ "Idle.Name", "EAuracronAnimationType::Idle" },
		{ "Lip.DisplayName", "Lip Sync Animation" },
		{ "Lip.Name", "EAuracronAnimationType::Lip" },
		{ "Locomotion.DisplayName", "Locomotion Animation" },
		{ "Locomotion.Name", "EAuracronAnimationType::Locomotion" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation Processing Types" },
#endif
		{ "Transition.DisplayName", "Transition Animation" },
		{ "Transition.Name", "EAuracronAnimationType::Transition" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAnimationType::Facial", (int64)EAuracronAnimationType::Facial },
		{ "EAuracronAnimationType::Body", (int64)EAuracronAnimationType::Body },
		{ "EAuracronAnimationType::Hand", (int64)EAuracronAnimationType::Hand },
		{ "EAuracronAnimationType::Eye", (int64)EAuracronAnimationType::Eye },
		{ "EAuracronAnimationType::Lip", (int64)EAuracronAnimationType::Lip },
		{ "EAuracronAnimationType::Emotion", (int64)EAuracronAnimationType::Emotion },
		{ "EAuracronAnimationType::Gesture", (int64)EAuracronAnimationType::Gesture },
		{ "EAuracronAnimationType::Locomotion", (int64)EAuracronAnimationType::Locomotion },
		{ "EAuracronAnimationType::Idle", (int64)EAuracronAnimationType::Idle },
		{ "EAuracronAnimationType::Transition", (int64)EAuracronAnimationType::Transition },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronAnimationType",
	"EAuracronAnimationType",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType()
{
	if (!Z_Registration_Info_UEnum_EAuracronAnimationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAnimationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAnimationType.InnerSingleton;
}
// ********** End Enum EAuracronAnimationType ******************************************************

// ********** Begin Enum EAuracronAnimationQuality *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAnimationQuality;
static UEnum* EAuracronAnimationQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAnimationQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAnimationQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronAnimationQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronAnimationQuality.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronAnimationQuality>()
{
	return EAuracronAnimationQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic Quality" },
		{ "Cinematic.Name", "EAuracronAnimationQuality::Cinematic" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Animation Quality Levels\n */" },
#endif
		{ "Custom.DisplayName", "Custom Quality" },
		{ "Custom.Name", "EAuracronAnimationQuality::Custom" },
		{ "Draft.DisplayName", "Draft Quality" },
		{ "Draft.Name", "EAuracronAnimationQuality::Draft" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
		{ "Preview.DisplayName", "Preview Quality" },
		{ "Preview.Name", "EAuracronAnimationQuality::Preview" },
		{ "Production.DisplayName", "Production Quality" },
		{ "Production.Name", "EAuracronAnimationQuality::Production" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation Quality Levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAnimationQuality::Draft", (int64)EAuracronAnimationQuality::Draft },
		{ "EAuracronAnimationQuality::Preview", (int64)EAuracronAnimationQuality::Preview },
		{ "EAuracronAnimationQuality::Production", (int64)EAuracronAnimationQuality::Production },
		{ "EAuracronAnimationQuality::Cinematic", (int64)EAuracronAnimationQuality::Cinematic },
		{ "EAuracronAnimationQuality::Custom", (int64)EAuracronAnimationQuality::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronAnimationQuality",
	"EAuracronAnimationQuality",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronAnimationQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAnimationQuality.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAnimationQuality.InnerSingleton;
}
// ********** End Enum EAuracronAnimationQuality ***************************************************

// ********** Begin ScriptStruct FAuracronAnimationParams ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAnimationParams;
class UScriptStruct* FAuracronAnimationParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAnimationParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAnimationParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAnimationParams, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronAnimationParams"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAnimationParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Animation Generation Parameters\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation Generation Parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationType_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Animation type to generate */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation type to generate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quality_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quality level */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Animation" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Duration in seconds */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Duration in seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameRate_MetaData[] = {
		{ "Category", "Animation" },
		{ "ClampMax", "120" },
		{ "ClampMin", "24" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frame rate */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frame rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRootMotion_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable root motion */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable root motion" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLooping_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Loop animation */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Loop animation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAdditive_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Additive animation */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Additive animation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionScheme_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Compression settings */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Compression settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalParams_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Additional parameters as JSON */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Additional parameters as JSON" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AnimationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AnimationType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FrameRate;
	static void NewProp_bEnableRootMotion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRootMotion;
	static void NewProp_bLooping_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLooping;
	static void NewProp_bAdditive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAdditive;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompressionScheme;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalParams;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAnimationParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_AnimationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_AnimationType = { "AnimationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationParams, AnimationType), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationType_MetaData), NewProp_AnimationType_MetaData) }; // 3814732994
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationParams, Quality), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronAnimationQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quality_MetaData), NewProp_Quality_MetaData) }; // 1776661038
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationParams, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_FrameRate = { "FrameRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationParams, FrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameRate_MetaData), NewProp_FrameRate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bEnableRootMotion_SetBit(void* Obj)
{
	((FAuracronAnimationParams*)Obj)->bEnableRootMotion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bEnableRootMotion = { "bEnableRootMotion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAnimationParams), &Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bEnableRootMotion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRootMotion_MetaData), NewProp_bEnableRootMotion_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bLooping_SetBit(void* Obj)
{
	((FAuracronAnimationParams*)Obj)->bLooping = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bLooping = { "bLooping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAnimationParams), &Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bLooping_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLooping_MetaData), NewProp_bLooping_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bAdditive_SetBit(void* Obj)
{
	((FAuracronAnimationParams*)Obj)->bAdditive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bAdditive = { "bAdditive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAnimationParams), &Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bAdditive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAdditive_MetaData), NewProp_bAdditive_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_CompressionScheme = { "CompressionScheme", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationParams, CompressionScheme), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionScheme_MetaData), NewProp_CompressionScheme_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_AdditionalParams = { "AdditionalParams", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationParams, AdditionalParams), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalParams_MetaData), NewProp_AdditionalParams_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_AnimationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_AnimationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_FrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bEnableRootMotion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bLooping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_bAdditive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_CompressionScheme,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewProp_AdditionalParams,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronAnimationParams",
	Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::PropPointers),
	sizeof(FAuracronAnimationParams),
	alignof(FAuracronAnimationParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAnimationParams()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAnimationParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAnimationParams.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAnimationParams.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAnimationParams ********************************************

// ********** Begin ScriptStruct FAuracronAnimationResult ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAnimationResult;
class UScriptStruct* FAuracronAnimationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAnimationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAnimationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAnimationResult, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronAnimationResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAnimationResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Animation Processing Result\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation Processing Result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether the operation was successful */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether the operation was successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Result message */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Result message" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedAnimation_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generated animation asset */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generated animation asset" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingTimeMS_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Processing time in milliseconds */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processing time in milliseconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputPath_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Output file path */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output file path" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationStats_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Animation statistics */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation statistics" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedAnimation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProcessingTimeMS;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputPath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AnimationStats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAnimationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronAnimationResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAnimationResult), &Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationResult, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_GeneratedAnimation = { "GeneratedAnimation", nullptr, (EPropertyFlags)0x0114000000000014, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationResult, GeneratedAnimation), Z_Construct_UClass_UAnimationAsset_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedAnimation_MetaData), NewProp_GeneratedAnimation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_ProcessingTimeMS = { "ProcessingTimeMS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationResult, ProcessingTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingTimeMS_MetaData), NewProp_ProcessingTimeMS_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_OutputPath = { "OutputPath", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationResult, OutputPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputPath_MetaData), NewProp_OutputPath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_AnimationStats = { "AnimationStats", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnimationResult, AnimationStats), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationStats_MetaData), NewProp_AnimationStats_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_GeneratedAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_ProcessingTimeMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_OutputPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewProp_AnimationStats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronAnimationResult",
	Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::PropPointers),
	sizeof(FAuracronAnimationResult),
	alignof(FAuracronAnimationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAnimationResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAnimationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAnimationResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAnimationResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAnimationResult ********************************************

// ********** Begin ScriptStruct FAuracronFacialAnimConfig *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFacialAnimConfig;
class UScriptStruct* FAuracronFacialAnimConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFacialAnimConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFacialAnimConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFacialAnimConfig, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronFacialAnimConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFacialAnimConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Facial Animation Configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Facial Animation Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioFilePath_MetaData[] = {
		{ "Category", "Facial" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio file for lip sync */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio file for lip sync" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LipSyncText_MetaData[] = {
		{ "Category", "Facial" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Text for lip sync */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Text for lip sync" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmotionIntensity_MetaData[] = {
		{ "Category", "Facial" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Emotion intensity */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Emotion intensity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlinkFrequency_MetaData[] = {
		{ "Category", "Facial" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Eye blink frequency */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eye blink frequency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeLookTarget_MetaData[] = {
		{ "Category", "Facial" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Eye look target */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eye look target" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMicroExpressions_MetaData[] = {
		{ "Category", "Facial" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable micro expressions */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable micro expressions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioFilePath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LipSyncText;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EmotionIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlinkFrequency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EyeLookTarget;
	static void NewProp_bEnableMicroExpressions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMicroExpressions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFacialAnimConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_AudioFilePath = { "AudioFilePath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFacialAnimConfig, AudioFilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioFilePath_MetaData), NewProp_AudioFilePath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_LipSyncText = { "LipSyncText", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFacialAnimConfig, LipSyncText), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LipSyncText_MetaData), NewProp_LipSyncText_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_EmotionIntensity = { "EmotionIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFacialAnimConfig, EmotionIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmotionIntensity_MetaData), NewProp_EmotionIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_BlinkFrequency = { "BlinkFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFacialAnimConfig, BlinkFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlinkFrequency_MetaData), NewProp_BlinkFrequency_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_EyeLookTarget = { "EyeLookTarget", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFacialAnimConfig, EyeLookTarget), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeLookTarget_MetaData), NewProp_EyeLookTarget_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_bEnableMicroExpressions_SetBit(void* Obj)
{
	((FAuracronFacialAnimConfig*)Obj)->bEnableMicroExpressions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_bEnableMicroExpressions = { "bEnableMicroExpressions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFacialAnimConfig), &Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_bEnableMicroExpressions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMicroExpressions_MetaData), NewProp_bEnableMicroExpressions_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_AudioFilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_LipSyncText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_EmotionIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_BlinkFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_EyeLookTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewProp_bEnableMicroExpressions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronFacialAnimConfig",
	Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::PropPointers),
	sizeof(FAuracronFacialAnimConfig),
	alignof(FAuracronFacialAnimConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFacialAnimConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFacialAnimConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFacialAnimConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFacialAnimConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFacialAnimConfig *******************************************

// ********** Begin Delegate FAuracronAnimationComplete ********************************************
struct Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronAnimationComplete_Parms
	{
		FAuracronAnimationResult Result;
		FString OperationID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronAnimationComplete_Parms, Result), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 911803630
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronAnimationComplete_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::NewProp_OperationID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework, nullptr, "AuracronAnimationComplete__DelegateSignature", Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronAnimationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronAnimationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FAuracronAnimationComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronAnimationComplete, FAuracronAnimationResult const& Result, const FString& OperationID)
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronAnimationComplete_Parms
	{
		FAuracronAnimationResult Result;
		FString OperationID;
	};
	_Script_AuracronMetaHumanFramework_eventAuracronAnimationComplete_Parms Parms;
	Parms.Result=Result;
	Parms.OperationID=OperationID;
	AuracronAnimationComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FAuracronAnimationComplete **********************************************

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function AnalyzeAnimationQuality *******
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventAnalyzeAnimationQuality_Parms
	{
		UAnimationAsset* Animation;
		FAuracronAnimationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Analyze animation quality\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analyze animation quality" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Animation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::NewProp_Animation = { "Animation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventAnalyzeAnimationQuality_Parms, Animation), Z_Construct_UClass_UAnimationAsset_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventAnalyzeAnimationQuality_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(0, nullptr) }; // 911803630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::NewProp_Animation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "AnalyzeAnimationQuality", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::AuracronMetaHumanAnimationSystem_eventAnalyzeAnimationQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::AuracronMetaHumanAnimationSystem_eventAnalyzeAnimationQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execAnalyzeAnimationQuality)
{
	P_GET_OBJECT(UAnimationAsset,Z_Param_Animation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAnimationResult*)Z_Param__Result=P_THIS->AnalyzeAnimationQuality(Z_Param_Animation);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function AnalyzeAnimationQuality *********

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function ApplyRealtimeFacialAnimation **
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventApplyRealtimeFacialAnimation_Parms
	{
		USkeletalMeshComponent* MeshComponent;
		TMap<FString,float> BlendShapeWeights;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|RealTime" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Apply real-time facial animation\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply real-time facial animation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendShapeWeights_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendShapeWeights_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlendShapeWeights_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BlendShapeWeights;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventApplyRealtimeFacialAnimation_Parms, MeshComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_BlendShapeWeights_ValueProp = { "BlendShapeWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_BlendShapeWeights_Key_KeyProp = { "BlendShapeWeights_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_BlendShapeWeights = { "BlendShapeWeights", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventApplyRealtimeFacialAnimation_Parms, BlendShapeWeights), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendShapeWeights_MetaData), NewProp_BlendShapeWeights_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanAnimationSystem_eventApplyRealtimeFacialAnimation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanAnimationSystem_eventApplyRealtimeFacialAnimation_Parms), &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_BlendShapeWeights_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_BlendShapeWeights_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_BlendShapeWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "ApplyRealtimeFacialAnimation", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::AuracronMetaHumanAnimationSystem_eventApplyRealtimeFacialAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::AuracronMetaHumanAnimationSystem_eventApplyRealtimeFacialAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execApplyRealtimeFacialAnimation)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MeshComponent);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_BlendShapeWeights);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyRealtimeFacialAnimation(Z_Param_MeshComponent,Z_Param_Out_BlendShapeWeights);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function ApplyRealtimeFacialAnimation ****

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function BlendAnimations ***************
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventBlendAnimations_Parms
	{
		TArray<UAnimSequence*> Animations;
		TArray<float> Weights;
		FAuracronAnimationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Blending" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Blend animations\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blend animations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Animations_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weights_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Animations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Animations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Weights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Weights;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::NewProp_Animations_Inner = { "Animations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UAnimSequence_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::NewProp_Animations = { "Animations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventBlendAnimations_Parms, Animations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Animations_MetaData), NewProp_Animations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::NewProp_Weights_Inner = { "Weights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::NewProp_Weights = { "Weights", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventBlendAnimations_Parms, Weights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weights_MetaData), NewProp_Weights_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventBlendAnimations_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(0, nullptr) }; // 911803630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::NewProp_Animations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::NewProp_Animations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::NewProp_Weights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::NewProp_Weights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "BlendAnimations", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::AuracronMetaHumanAnimationSystem_eventBlendAnimations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::AuracronMetaHumanAnimationSystem_eventBlendAnimations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execBlendAnimations)
{
	P_GET_TARRAY_REF(UAnimSequence*,Z_Param_Out_Animations);
	P_GET_TARRAY_REF(float,Z_Param_Out_Weights);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAnimationResult*)Z_Param__Result=P_THIS->BlendAnimations(Z_Param_Out_Animations,Z_Param_Out_Weights);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function BlendAnimations *****************

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function CreateAnimationMontage ********
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventCreateAnimationMontage_Parms
	{
		TArray<UAnimSequence*> Animations;
		TArray<float> SectionLengths;
		FAuracronAnimationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Blending" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create animation montage\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create animation montage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Animations_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectionLengths_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Animations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Animations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SectionLengths_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SectionLengths;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::NewProp_Animations_Inner = { "Animations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UAnimSequence_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::NewProp_Animations = { "Animations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventCreateAnimationMontage_Parms, Animations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Animations_MetaData), NewProp_Animations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::NewProp_SectionLengths_Inner = { "SectionLengths", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::NewProp_SectionLengths = { "SectionLengths", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventCreateAnimationMontage_Parms, SectionLengths), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectionLengths_MetaData), NewProp_SectionLengths_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventCreateAnimationMontage_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(0, nullptr) }; // 911803630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::NewProp_Animations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::NewProp_Animations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::NewProp_SectionLengths_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::NewProp_SectionLengths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "CreateAnimationMontage", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::AuracronMetaHumanAnimationSystem_eventCreateAnimationMontage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::AuracronMetaHumanAnimationSystem_eventCreateAnimationMontage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execCreateAnimationMontage)
{
	P_GET_TARRAY_REF(UAnimSequence*,Z_Param_Out_Animations);
	P_GET_TARRAY_REF(float,Z_Param_Out_SectionLengths);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAnimationResult*)Z_Param__Result=P_THIS->CreateAnimationMontage(Z_Param_Out_Animations,Z_Param_Out_SectionLengths);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function CreateAnimationMontage **********

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function CreateBlendSpace **************
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventCreateBlendSpace_Parms
	{
		TArray<UAnimSequence*> Animations;
		TArray<FVector2D> BlendParameters;
		FAuracronAnimationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Blending" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create blend space from animations\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create blend space from animations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Animations_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendParameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Animations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Animations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlendParameters_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::NewProp_Animations_Inner = { "Animations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UAnimSequence_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::NewProp_Animations = { "Animations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventCreateBlendSpace_Parms, Animations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Animations_MetaData), NewProp_Animations_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::NewProp_BlendParameters_Inner = { "BlendParameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::NewProp_BlendParameters = { "BlendParameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventCreateBlendSpace_Parms, BlendParameters), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendParameters_MetaData), NewProp_BlendParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventCreateBlendSpace_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(0, nullptr) }; // 911803630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::NewProp_Animations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::NewProp_Animations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::NewProp_BlendParameters_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::NewProp_BlendParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "CreateBlendSpace", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::AuracronMetaHumanAnimationSystem_eventCreateBlendSpace_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::AuracronMetaHumanAnimationSystem_eventCreateBlendSpace_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execCreateBlendSpace)
{
	P_GET_TARRAY_REF(UAnimSequence*,Z_Param_Out_Animations);
	P_GET_TARRAY_REF(FVector2D,Z_Param_Out_BlendParameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAnimationResult*)Z_Param__Result=P_THIS->CreateBlendSpace(Z_Param_Out_Animations,Z_Param_Out_BlendParameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function CreateBlendSpace ****************

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function GenerateAnimation *************
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventGenerateAnimation_Parms
	{
		FAuracronAnimationParams Params;
		USkeletalMesh* TargetMesh;
		FAuracronAnimationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate animation from parameters\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate animation from parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Params_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Params;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::NewProp_Params = { "Params", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateAnimation_Parms, Params), Z_Construct_UScriptStruct_FAuracronAnimationParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Params_MetaData), NewProp_Params_MetaData) }; // 1115310666
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::NewProp_TargetMesh = { "TargetMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateAnimation_Parms, TargetMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateAnimation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(0, nullptr) }; // 911803630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::NewProp_Params,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::NewProp_TargetMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "GenerateAnimation", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::AuracronMetaHumanAnimationSystem_eventGenerateAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::AuracronMetaHumanAnimationSystem_eventGenerateAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execGenerateAnimation)
{
	P_GET_STRUCT_REF(FAuracronAnimationParams,Z_Param_Out_Params);
	P_GET_OBJECT(USkeletalMesh,Z_Param_TargetMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAnimationResult*)Z_Param__Result=P_THIS->GenerateAnimation(Z_Param_Out_Params,Z_Param_TargetMesh);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function GenerateAnimation ***************

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function GenerateAnimationAsync ********
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventGenerateAnimationAsync_Parms
	{
		FAuracronAnimationParams Params;
		USkeletalMesh* TargetMesh;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate animation asynchronously\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate animation asynchronously" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Params_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Params;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetMesh;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::NewProp_Params = { "Params", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateAnimationAsync_Parms, Params), Z_Construct_UScriptStruct_FAuracronAnimationParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Params_MetaData), NewProp_Params_MetaData) }; // 1115310666
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::NewProp_TargetMesh = { "TargetMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateAnimationAsync_Parms, TargetMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateAnimationAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::NewProp_Params,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::NewProp_TargetMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "GenerateAnimationAsync", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::AuracronMetaHumanAnimationSystem_eventGenerateAnimationAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::AuracronMetaHumanAnimationSystem_eventGenerateAnimationAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execGenerateAnimationAsync)
{
	P_GET_STRUCT_REF(FAuracronAnimationParams,Z_Param_Out_Params);
	P_GET_OBJECT(USkeletalMesh,Z_Param_TargetMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateAnimationAsync(Z_Param_Out_Params,Z_Param_TargetMesh);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function GenerateAnimationAsync **********

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function GenerateFacialAnimation *******
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimation_Parms
	{
		FAuracronFacialAnimConfig Config;
		USkeletalMesh* TargetMesh;
		FAuracronAnimationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate facial animation\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate facial animation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimation_Parms, Config), Z_Construct_UScriptStruct_FAuracronFacialAnimConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 2228497188
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::NewProp_TargetMesh = { "TargetMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimation_Parms, TargetMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(0, nullptr) }; // 911803630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::NewProp_TargetMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "GenerateFacialAnimation", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execGenerateFacialAnimation)
{
	P_GET_STRUCT_REF(FAuracronFacialAnimConfig,Z_Param_Out_Config);
	P_GET_OBJECT(USkeletalMesh,Z_Param_TargetMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAnimationResult*)Z_Param__Result=P_THIS->GenerateFacialAnimation(Z_Param_Out_Config,Z_Param_TargetMesh);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function GenerateFacialAnimation *********

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function GenerateFacialAnimationAsync **
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimationAsync_Parms
	{
		FAuracronFacialAnimConfig Config;
		USkeletalMesh* TargetMesh;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate facial animation asynchronously\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate facial animation asynchronously" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetMesh;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimationAsync_Parms, Config), Z_Construct_UScriptStruct_FAuracronFacialAnimConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 2228497188
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::NewProp_TargetMesh = { "TargetMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimationAsync_Parms, TargetMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimationAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::NewProp_TargetMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "GenerateFacialAnimationAsync", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimationAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::AuracronMetaHumanAnimationSystem_eventGenerateFacialAnimationAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execGenerateFacialAnimationAsync)
{
	P_GET_STRUCT_REF(FAuracronFacialAnimConfig,Z_Param_Out_Config);
	P_GET_OBJECT(USkeletalMesh,Z_Param_TargetMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateFacialAnimationAsync(Z_Param_Out_Config,Z_Param_TargetMesh);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function GenerateFacialAnimationAsync ****

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function GetAnimationStatistics ********
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventGetAnimationStatistics_Parms
	{
		UAnimationAsset* Animation;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get animation statistics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get animation statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Animation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::NewProp_Animation = { "Animation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGetAnimationStatistics_Parms, Animation), Z_Construct_UClass_UAnimationAsset_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGetAnimationStatistics_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::NewProp_Animation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "GetAnimationStatistics", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::AuracronMetaHumanAnimationSystem_eventGetAnimationStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::AuracronMetaHumanAnimationSystem_eventGetAnimationStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execGetAnimationStatistics)
{
	P_GET_OBJECT(UAnimationAsset,Z_Param_Animation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetAnimationStatistics(Z_Param_Animation);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function GetAnimationStatistics **********

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function GetAsyncOperationResult *******
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventGetAsyncOperationResult_Parms
	{
		FString OperationID;
		FAuracronAnimationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get async operation result\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get async operation result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGetAsyncOperationResult_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGetAsyncOperationResult_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(0, nullptr) }; // 911803630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::NewProp_OperationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "GetAsyncOperationResult", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::AuracronMetaHumanAnimationSystem_eventGetAsyncOperationResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::AuracronMetaHumanAnimationSystem_eventGetAsyncOperationResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execGetAsyncOperationResult)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OperationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAnimationResult*)Z_Param__Result=P_THIS->GetAsyncOperationResult(Z_Param_OperationID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function GetAsyncOperationResult *********

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function GetSupportedAnimationTypes ****
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventGetSupportedAnimationTypes_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get supported animation types\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get supported animation types" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventGetSupportedAnimationTypes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "GetSupportedAnimationTypes", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::AuracronMetaHumanAnimationSystem_eventGetSupportedAnimationTypes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::AuracronMetaHumanAnimationSystem_eventGetSupportedAnimationTypes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execGetSupportedAnimationTypes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetSupportedAnimationTypes();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function GetSupportedAnimationTypes ******

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function Initialize ********************
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventInitialize_Parms
	{
		UAuracronMetaHumanFramework* InFramework;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize animation system\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize animation system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InFramework;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::NewProp_InFramework = { "InFramework", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventInitialize_Parms, InFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanAnimationSystem_eventInitialize_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanAnimationSystem_eventInitialize_Parms), &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::NewProp_InFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "Initialize", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::AuracronMetaHumanAnimationSystem_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::AuracronMetaHumanAnimationSystem_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execInitialize)
{
	P_GET_OBJECT(UAuracronMetaHumanFramework,Z_Param_InFramework);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Initialize(Z_Param_InFramework);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function Initialize **********************

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function IsAsyncOperationComplete ******
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventIsAsyncOperationComplete_Parms
	{
		FString OperationID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if async operation is complete\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if async operation is complete" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventIsAsyncOperationComplete_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanAnimationSystem_eventIsAsyncOperationComplete_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanAnimationSystem_eventIsAsyncOperationComplete_Parms), &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::NewProp_OperationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "IsAsyncOperationComplete", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::AuracronMetaHumanAnimationSystem_eventIsAsyncOperationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::AuracronMetaHumanAnimationSystem_eventIsAsyncOperationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execIsAsyncOperationComplete)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OperationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAsyncOperationComplete(Z_Param_OperationID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function IsAsyncOperationComplete ********

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function IsInitialized *****************
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if animation system is initialized\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if animation system is initialized" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanAnimationSystem_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanAnimationSystem_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::AuracronMetaHumanAnimationSystem_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::AuracronMetaHumanAnimationSystem_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function IsInitialized *******************

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function OptimizeAnimation *************
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventOptimizeAnimation_Parms
	{
		UAnimationAsset* Animation;
		FAuracronAnimationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Optimize animation\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize animation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Animation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::NewProp_Animation = { "Animation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventOptimizeAnimation_Parms, Animation), Z_Construct_UClass_UAnimationAsset_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventOptimizeAnimation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(0, nullptr) }; // 911803630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::NewProp_Animation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "OptimizeAnimation", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::AuracronMetaHumanAnimationSystem_eventOptimizeAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::AuracronMetaHumanAnimationSystem_eventOptimizeAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execOptimizeAnimation)
{
	P_GET_OBJECT(UAnimationAsset,Z_Param_Animation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAnimationResult*)Z_Param__Result=P_THIS->OptimizeAnimation(Z_Param_Animation);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function OptimizeAnimation ***************

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function RetargetAnimation *************
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventRetargetAnimation_Parms
	{
		UAnimationAsset* SourceAnimation;
		USkeleton* TargetSkeleton;
		FAuracronAnimationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Retarget animation to different skeleton\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Retarget animation to different skeleton" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceAnimation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetSkeleton;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::NewProp_SourceAnimation = { "SourceAnimation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventRetargetAnimation_Parms, SourceAnimation), Z_Construct_UClass_UAnimationAsset_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::NewProp_TargetSkeleton = { "TargetSkeleton", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventRetargetAnimation_Parms, TargetSkeleton), Z_Construct_UClass_USkeleton_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventRetargetAnimation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(0, nullptr) }; // 911803630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::NewProp_SourceAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::NewProp_TargetSkeleton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "RetargetAnimation", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::AuracronMetaHumanAnimationSystem_eventRetargetAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::AuracronMetaHumanAnimationSystem_eventRetargetAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execRetargetAnimation)
{
	P_GET_OBJECT(UAnimationAsset,Z_Param_SourceAnimation);
	P_GET_OBJECT(USkeleton,Z_Param_TargetSkeleton);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAnimationResult*)Z_Param__Result=P_THIS->RetargetAnimation(Z_Param_SourceAnimation,Z_Param_TargetSkeleton);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function RetargetAnimation ***************

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function Shutdown **********************
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Shutdown animation system\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shutdown animation system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function Shutdown ************************

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function TriggerFacialExpression *******
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventTriggerFacialExpression_Parms
	{
		USkeletalMeshComponent* MeshComponent;
		FString ExpressionName;
		float Intensity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|RealTime" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Trigger facial expression\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Trigger facial expression" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExpressionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExpressionName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventTriggerFacialExpression_Parms, MeshComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::NewProp_ExpressionName = { "ExpressionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventTriggerFacialExpression_Parms, ExpressionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExpressionName_MetaData), NewProp_ExpressionName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventTriggerFacialExpression_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanAnimationSystem_eventTriggerFacialExpression_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanAnimationSystem_eventTriggerFacialExpression_Parms), &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::NewProp_ExpressionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "TriggerFacialExpression", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::AuracronMetaHumanAnimationSystem_eventTriggerFacialExpression_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::AuracronMetaHumanAnimationSystem_eventTriggerFacialExpression_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execTriggerFacialExpression)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MeshComponent);
	P_GET_PROPERTY(FStrProperty,Z_Param_ExpressionName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TriggerFacialExpression(Z_Param_MeshComponent,Z_Param_ExpressionName,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function TriggerFacialExpression *********

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function UpdateEyeLookTarget ***********
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventUpdateEyeLookTarget_Parms
	{
		USkeletalMeshComponent* MeshComponent;
		FVector LookTarget;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|RealTime" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Update eye look target\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update eye look target" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LookTarget_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LookTarget;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventUpdateEyeLookTarget_Parms, MeshComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::NewProp_LookTarget = { "LookTarget", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventUpdateEyeLookTarget_Parms, LookTarget), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LookTarget_MetaData), NewProp_LookTarget_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanAnimationSystem_eventUpdateEyeLookTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanAnimationSystem_eventUpdateEyeLookTarget_Parms), &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::NewProp_LookTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "UpdateEyeLookTarget", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::AuracronMetaHumanAnimationSystem_eventUpdateEyeLookTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::AuracronMetaHumanAnimationSystem_eventUpdateEyeLookTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execUpdateEyeLookTarget)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MeshComponent);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_LookTarget);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateEyeLookTarget(Z_Param_MeshComponent,Z_Param_Out_LookTarget);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function UpdateEyeLookTarget *************

// ********** Begin Class UAuracronMetaHumanAnimationSystem Function ValidateMetaHumanAnimation ****
struct Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics
{
	struct AuracronMetaHumanAnimationSystem_eventValidateMetaHumanAnimation_Parms
	{
		UAnimationAsset* Animation;
		USkeletalMesh* MetaHumanMesh;
		FAuracronAnimationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Animation|Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validate animation for MetaHuman\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate animation for MetaHuman" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Animation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MetaHumanMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::NewProp_Animation = { "Animation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventValidateMetaHumanAnimation_Parms, Animation), Z_Construct_UClass_UAnimationAsset_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::NewProp_MetaHumanMesh = { "MetaHumanMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventValidateMetaHumanAnimation_Parms, MetaHumanMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanAnimationSystem_eventValidateMetaHumanAnimation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAnimationResult, METADATA_PARAMS(0, nullptr) }; // 911803630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::NewProp_Animation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::NewProp_MetaHumanMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, nullptr, "ValidateMetaHumanAnimation", Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::AuracronMetaHumanAnimationSystem_eventValidateMetaHumanAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::AuracronMetaHumanAnimationSystem_eventValidateMetaHumanAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanAnimationSystem::execValidateMetaHumanAnimation)
{
	P_GET_OBJECT(UAnimationAsset,Z_Param_Animation);
	P_GET_OBJECT(USkeletalMesh,Z_Param_MetaHumanMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAnimationResult*)Z_Param__Result=P_THIS->ValidateMetaHumanAnimation(Z_Param_Animation,Z_Param_MetaHumanMesh);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanAnimationSystem Function ValidateMetaHumanAnimation ******

// ********** Begin Class UAuracronMetaHumanAnimationSystem ****************************************
void UAuracronMetaHumanAnimationSystem::StaticRegisterNativesUAuracronMetaHumanAnimationSystem()
{
	UClass* Class = UAuracronMetaHumanAnimationSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AnalyzeAnimationQuality", &UAuracronMetaHumanAnimationSystem::execAnalyzeAnimationQuality },
		{ "ApplyRealtimeFacialAnimation", &UAuracronMetaHumanAnimationSystem::execApplyRealtimeFacialAnimation },
		{ "BlendAnimations", &UAuracronMetaHumanAnimationSystem::execBlendAnimations },
		{ "CreateAnimationMontage", &UAuracronMetaHumanAnimationSystem::execCreateAnimationMontage },
		{ "CreateBlendSpace", &UAuracronMetaHumanAnimationSystem::execCreateBlendSpace },
		{ "GenerateAnimation", &UAuracronMetaHumanAnimationSystem::execGenerateAnimation },
		{ "GenerateAnimationAsync", &UAuracronMetaHumanAnimationSystem::execGenerateAnimationAsync },
		{ "GenerateFacialAnimation", &UAuracronMetaHumanAnimationSystem::execGenerateFacialAnimation },
		{ "GenerateFacialAnimationAsync", &UAuracronMetaHumanAnimationSystem::execGenerateFacialAnimationAsync },
		{ "GetAnimationStatistics", &UAuracronMetaHumanAnimationSystem::execGetAnimationStatistics },
		{ "GetAsyncOperationResult", &UAuracronMetaHumanAnimationSystem::execGetAsyncOperationResult },
		{ "GetSupportedAnimationTypes", &UAuracronMetaHumanAnimationSystem::execGetSupportedAnimationTypes },
		{ "Initialize", &UAuracronMetaHumanAnimationSystem::execInitialize },
		{ "IsAsyncOperationComplete", &UAuracronMetaHumanAnimationSystem::execIsAsyncOperationComplete },
		{ "IsInitialized", &UAuracronMetaHumanAnimationSystem::execIsInitialized },
		{ "OptimizeAnimation", &UAuracronMetaHumanAnimationSystem::execOptimizeAnimation },
		{ "RetargetAnimation", &UAuracronMetaHumanAnimationSystem::execRetargetAnimation },
		{ "Shutdown", &UAuracronMetaHumanAnimationSystem::execShutdown },
		{ "TriggerFacialExpression", &UAuracronMetaHumanAnimationSystem::execTriggerFacialExpression },
		{ "UpdateEyeLookTarget", &UAuracronMetaHumanAnimationSystem::execUpdateEyeLookTarget },
		{ "ValidateMetaHumanAnimation", &UAuracronMetaHumanAnimationSystem::execValidateMetaHumanAnimation },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronMetaHumanAnimationSystem;
UClass* UAuracronMetaHumanAnimationSystem::GetPrivateStaticClass()
{
	using TClass = UAuracronMetaHumanAnimationSystem;
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanAnimationSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMetaHumanAnimationSystem"),
			Z_Registration_Info_UClass_UAuracronMetaHumanAnimationSystem.InnerSingleton,
			StaticRegisterNativesUAuracronMetaHumanAnimationSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanAnimationSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_NoRegister()
{
	return UAuracronMetaHumanAnimationSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|MetaHuman|Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AURACRON MetaHuman Animation System\n * Advanced animation processing and generation system for UE 5.6\n */" },
#endif
		{ "IncludePath", "Systems/AuracronMetaHumanAnimationSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AURACRON MetaHuman Animation System\nAdvanced animation processing and generation system for UE 5.6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAnimationComplete_MetaData[] = {
		{ "Category", "AURACRON MetaHuman|Animation|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when an async animation operation completes */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when an async animation operation completes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerFramework_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Internal State ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Internal State ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastErrorMessage_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanAnimationSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAnimationComplete;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwnerFramework;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LastErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_AnalyzeAnimationQuality, "AnalyzeAnimationQuality" }, // 354665815
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ApplyRealtimeFacialAnimation, "ApplyRealtimeFacialAnimation" }, // 2082008218
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_BlendAnimations, "BlendAnimations" }, // 2023469590
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateAnimationMontage, "CreateAnimationMontage" }, // 2274192994
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_CreateBlendSpace, "CreateBlendSpace" }, // 1330365237
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimation, "GenerateAnimation" }, // 303450368
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateAnimationAsync, "GenerateAnimationAsync" }, // 2438471588
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimation, "GenerateFacialAnimation" }, // 1408079038
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GenerateFacialAnimationAsync, "GenerateFacialAnimationAsync" }, // 1294715094
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAnimationStatistics, "GetAnimationStatistics" }, // 2401963983
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetAsyncOperationResult, "GetAsyncOperationResult" }, // 3952475685
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_GetSupportedAnimationTypes, "GetSupportedAnimationTypes" }, // 3679793307
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Initialize, "Initialize" }, // 3777607231
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsAsyncOperationComplete, "IsAsyncOperationComplete" }, // 4124591868
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_IsInitialized, "IsInitialized" }, // 2540698659
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_OptimizeAnimation, "OptimizeAnimation" }, // 2384479986
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_RetargetAnimation, "RetargetAnimation" }, // 2456923115
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_Shutdown, "Shutdown" }, // 187550938
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_TriggerFacialExpression, "TriggerFacialExpression" }, // 498118928
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_UpdateEyeLookTarget, "UpdateEyeLookTarget" }, // 1098433577
		{ &Z_Construct_UFunction_UAuracronMetaHumanAnimationSystem_ValidateMetaHumanAnimation, "ValidateMetaHumanAnimation" }, // 3661991830
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronMetaHumanAnimationSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::NewProp_OnAnimationComplete = { "OnAnimationComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanAnimationSystem, OnAnimationComplete), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronAnimationComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAnimationComplete_MetaData), NewProp_OnAnimationComplete_MetaData) }; // 295807220
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::NewProp_OwnerFramework = { "OwnerFramework", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanAnimationSystem, OwnerFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerFramework_MetaData), NewProp_OwnerFramework_MetaData) };
void Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronMetaHumanAnimationSystem*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMetaHumanAnimationSystem), &Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::NewProp_LastErrorMessage = { "LastErrorMessage", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanAnimationSystem, LastErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastErrorMessage_MetaData), NewProp_LastErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::NewProp_OnAnimationComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::NewProp_OwnerFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::NewProp_LastErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::ClassParams = {
	&UAuracronMetaHumanAnimationSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronMetaHumanAnimationSystem()
{
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanAnimationSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronMetaHumanAnimationSystem.OuterSingleton, Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanAnimationSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronMetaHumanAnimationSystem);
// ********** End Class UAuracronMetaHumanAnimationSystem ******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h__Script_AuracronMetaHumanFramework_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronAnimationType_StaticEnum, TEXT("EAuracronAnimationType"), &Z_Registration_Info_UEnum_EAuracronAnimationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3814732994U) },
		{ EAuracronAnimationQuality_StaticEnum, TEXT("EAuracronAnimationQuality"), &Z_Registration_Info_UEnum_EAuracronAnimationQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1776661038U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronAnimationParams::StaticStruct, Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics::NewStructOps, TEXT("AuracronAnimationParams"), &Z_Registration_Info_UScriptStruct_FAuracronAnimationParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAnimationParams), 1115310666U) },
		{ FAuracronAnimationResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics::NewStructOps, TEXT("AuracronAnimationResult"), &Z_Registration_Info_UScriptStruct_FAuracronAnimationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAnimationResult), 911803630U) },
		{ FAuracronFacialAnimConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics::NewStructOps, TEXT("AuracronFacialAnimConfig"), &Z_Registration_Info_UScriptStruct_FAuracronFacialAnimConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFacialAnimConfig), 2228497188U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronMetaHumanAnimationSystem, UAuracronMetaHumanAnimationSystem::StaticClass, TEXT("UAuracronMetaHumanAnimationSystem"), &Z_Registration_Info_UClass_UAuracronMetaHumanAnimationSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronMetaHumanAnimationSystem), 3198581440U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h__Script_AuracronMetaHumanFramework_142876655(TEXT("/Script/AuracronMetaHumanFramework"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
