# Script para renomear tipos duplicados

Write-Host "Corrigindo nomes duplicados..."

# 1. Renomear EConstraintType para EAuracronConstraintType em AuracronRigTransformation.h
$file1 = "Auracron\Source\AuracronMetaHumanBridge\Public\AuracronRigTransformation.h"
$content1 = Get-Content $file1 -Raw
$content1 = $content1 -replace "enum class EConstraintType", "enum class EAuracronConstraintType"
$content1 = $content1 -replace "EConstraintType::", "EAuracronConstraintType::"
$content1 = $content1 -replace "EConstraintType ", "EAuracronConstraintType "
Set-Content -Path $file1 -Value $content1 -NoNewline
Write-Host "Renomeado EConstraintType para EAuracronConstraintType"

# 2. Renomear FConstraintData para FAuracronConstraintData em AuracronRigTransformation.h
$content1 = Get-Content $file1 -Raw
$content1 = $content1 -replace "struct FConstraintData", "struct FAuracronConstraintData"
$content1 = $content1 -replace "FConstraintData ", "FAuracronConstraintData "
Set-Content -Path $file1 -Value $content1 -NoNewline
Write-Host "Renomeado FConstraintData para FAuracronConstraintData"

# 3. Renomear ETextureType para EAuracronTextureType em AuracronTextureGeneration.h
$file2 = "Auracron\Source\AuracronMetaHumanBridge\Public\AuracronTextureGeneration.h"
$content2 = Get-Content $file2 -Raw
$content2 = $content2 -replace "enum class ETextureType", "enum class EAuracronTextureType"
$content2 = $content2 -replace "ETextureType::", "EAuracronTextureType::"
$content2 = $content2 -replace "ETextureType ", "EAuracronTextureType "
Set-Content -Path $file2 -Value $content2 -NoNewline
Write-Host "Renomeado ETextureType para EAuracronTextureType"

# 4. Renomear FMetaHumanMeshData para FAuracronMetaHumanMeshData em AuracronMetaHumanBridge.h
$file3 = "Auracron\Source\AuracronMetaHumanBridge\Public\AuracronMetaHumanBridge.h"
$content3 = Get-Content $file3 -Raw
$content3 = $content3 -replace "struct FMetaHumanMeshData", "struct FAuracronMetaHumanMeshData"
$content3 = $content3 -replace "FMetaHumanMeshData ", "FAuracronMetaHumanMeshData "
Set-Content -Path $file3 -Value $content3 -NoNewline
Write-Host "Renomeado FMetaHumanMeshData para FAuracronMetaHumanMeshData"

# 5. Renomear ETransitionType para EAuracronVerticalTransitionType em AuracronVerticalTransitionsBridge.h
$file4 = "Auracron\Source\AuracronVerticalTransitionsBridge\Public\AuracronVerticalTransitionsBridge.h"
$content4 = Get-Content $file4 -Raw
$content4 = $content4 -replace "enum class ETransitionType", "enum class EAuracronVerticalTransitionType"
$content4 = $content4 -replace "ETransitionType::", "EAuracronVerticalTransitionType::"
$content4 = $content4 -replace "ETransitionType ", "EAuracronVerticalTransitionType "
Set-Content -Path $file4 -Value $content4 -NoNewline
Write-Host "Renomeado ETransitionType para EAuracronVerticalTransitionType"

# 6. Renomear ERealmType para EAuracronAdaptiveRealmType em AuracronAdaptiveCreaturesBridge.h
$file5 = "Auracron\Source\AuracronAdaptiveCreaturesBridge\Public\AuracronAdaptiveCreaturesBridge.h"
$content5 = Get-Content $file5 -Raw
$content5 = $content5 -replace "enum class ERealmType", "enum class EAuracronAdaptiveRealmType"
$content5 = $content5 -replace "ERealmType::", "EAuracronAdaptiveRealmType::"
$content5 = $content5 -replace "ERealmType ", "EAuracronAdaptiveRealmType "
Set-Content -Path $file5 -Value $content5 -NoNewline
Write-Host "Renomeado ERealmType para EAuracronAdaptiveRealmType"

Write-Host "Correções de nomes duplicados aplicadas com sucesso!"
