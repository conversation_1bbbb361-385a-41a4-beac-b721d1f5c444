// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Interface do Usuário Bridge Implementation

#include "AuracronUIBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/HUD.h"
#include "Blueprint/UserWidget.h"
#include "UMG.h"
#include "CommonUI/Public/CommonUserWidget.h"
#include "CommonUI/Public/CommonActivatableWidget.h"
#include "CommonUI/Public/CommonButtonBase.h"
#include "CommonUI/Public/CommonTextBlock.h"
#include "CommonUI/Public/CommonProgressBar.h"
#include "CommonUI/Public/CommonWidgetSwitcher.h"
#include "CommonInput/Public/CommonInputSubsystem.h"
#include "CommonInput/Public/CommonInputSettings.h"
#include "Components/CanvasPanel.h"
#include "Components/VerticalBox.h"
#include "Components/HorizontalBox.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/ProgressBar.h"
#include "Components/Slider.h"
#include "Animation/WidgetAnimation.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/ActorChannel.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputMappingContext.h"
#include "InputAction.h"
#include "Kismet/GameplayStatics.h"
#include "DrawDebugHelpers.h"

UAuracronUIBridge::UAuracronUIBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.033f; // 30 FPS para UI responsiva
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurações padrão de UI
    UIConfiguration.UIScale = 1.0f;
    UIConfiguration.UIOpacity = 1.0f;
    UIConfiguration.InputPlatform = EAuracronInputPlatform::Auto;
    UIConfiguration.bUseUIAnimations = true;
    UIConfiguration.AnimationSpeed = 1.0f;
    UIConfiguration.bUseUIParticleEffects = true;
    UIConfiguration.bUseUISounds = true;
    UIConfiguration.UISoundVolume = 0.8f;
    UIConfiguration.bUseHapticFeedback = true;
    UIConfiguration.HapticIntensity = 0.5f;
    UIConfiguration.bHighContrastMode = false;
    UIConfiguration.bColorBlindSupport = false;
    UIConfiguration.ColorBlindnessType = TEXT("None");
    UIConfiguration.FontScale = 1.0f;
    UIConfiguration.bScreenReaderSupport = false;
    UIConfiguration.UILanguage = TEXT("en");
    UIConfiguration.UIRegion = TEXT("US");
    UIConfiguration.bUseResponsiveLayout = true;
    UIConfiguration.ReferenceResolution = FVector2D(1920.0f, 1080.0f);
    UIConfiguration.bUseSafeZones = true;
    UIConfiguration.SafeZoneMargin = 0.05f;
    
    // Configurações padrão do minimapa
    MinimapConfiguration.MinimapSize = FVector2D(200.0f, 200.0f);
    MinimapConfiguration.MinimapPosition = FVector2D(0.85f, 0.15f);
    MinimapConfiguration.MinimapZoom = 1.0f;
    MinimapConfiguration.MinimapRotation = 0.0f;
    MinimapConfiguration.bShowAllRealms = true;
    MinimapConfiguration.bShowAlliedPlayers = true;
    MinimapConfiguration.bShowEnemyPlayers = false;
    MinimapConfiguration.bShowObjectives = true;
    MinimapConfiguration.bShowWards = true;
    MinimapConfiguration.bUseTeamColors = true;
    MinimapConfiguration.AlliedTeamColor = FLinearColor::Blue;
    MinimapConfiguration.EnemyTeamColor = FLinearColor::Red;
    MinimapConfiguration.ObjectiveColor = FLinearColor::Yellow;
    MinimapConfiguration.bRealTimeUpdate = true;
    MinimapConfiguration.UpdateFrequency = 10;
    MinimapConfiguration.bUseFogOfWar = true;
    MinimapConfiguration.VisionRadius = 1500.0f;
    
    // Configurações padrão do HUD de combate
    CombatHUDConfiguration.bShowHealthBar = true;
    CombatHUDConfiguration.bShowManaBar = true;
    CombatHUDConfiguration.bShowAbilityCooldowns = true;
    CombatHUDConfiguration.bShowDamageIndicators = true;
    CombatHUDConfiguration.bShowHealingIndicators = true;
    CombatHUDConfiguration.bShowStatusEffects = true;
    CombatHUDConfiguration.bShowExperienceBar = true;
    CombatHUDConfiguration.bShowGold = true;
    CombatHUDConfiguration.bShowKDA = true;
    CombatHUDConfiguration.bShowMatchTimer = true;
    CombatHUDConfiguration.HealthBarPosition = FVector2D(0.1f, 0.9f);
    CombatHUDConfiguration.HealthBarSize = FVector2D(300.0f, 20.0f);
    CombatHUDConfiguration.ManaBarPosition = FVector2D(0.1f, 0.95f);
    CombatHUDConfiguration.ManaBarSize = FVector2D(300.0f, 15.0f);
    CombatHUDConfiguration.AbilitiesPosition = FVector2D(0.5f, 0.95f);
    CombatHUDConfiguration.AbilityIconSize = 64.0f;
    CombatHUDConfiguration.AbilitySpacing = 8.0f;
    CombatHUDConfiguration.bUseCompactLayout = false;
    CombatHUDConfiguration.bShowTooltips = true;
    CombatHUDConfiguration.TooltipDelay = 0.5f;
    CombatHUDConfiguration.bUseTeamColors = true;
    CombatHUDConfiguration.HealthColor = FLinearColor::Green;
    CombatHUDConfiguration.ManaColor = FLinearColor::Blue;
    CombatHUDConfiguration.ExperienceColor = FLinearColor::Yellow;
    CombatHUDConfiguration.CooldownColor = FLinearColor(0.2f, 0.2f, 0.2f, 0.8f);
    
    // Configurações padrão de input cross-platform
    CrossPlatformInputConfiguration.MouseSensitivity = 1.0f;
    CrossPlatformInputConfiguration.GamepadSensitivity = 1.5f;
    CrossPlatformInputConfiguration.TouchSensitivity = 1.2f;
    CrossPlatformInputConfiguration.bInvertYAxis = false;
    CrossPlatformInputConfiguration.bUseAutoAimOnMobile = true;
    CrossPlatformInputConfiguration.AutoAimIntensity = 0.3f;
    CrossPlatformInputConfiguration.bUseTouchGestures = true;
    CrossPlatformInputConfiguration.TouchButtonSize = 64.0f;
    CrossPlatformInputConfiguration.TouchButtonOpacity = 0.7f;
    CrossPlatformInputConfiguration.bUseVibration = true;
    CrossPlatformInputConfiguration.VibrationIntensity = 0.5f;
}

void UAuracronUIBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Interface do Usuário"));

    // Obter referências aos subsistemas
    if (UWorld* World = GetWorld())
    {
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            CommonInputSubsystem = UCommonInputSubsystem::Get(PC->GetLocalPlayer());
            if (!CommonInputSubsystem)
            {
                UE_LOG(LogTemp, Error, TEXT("AURACRON: CommonInputSubsystem não encontrado"));
            }

            if (APawn* ControlledPawn = PC->GetPawn())
            {
                EnhancedInputComponent = Cast<UEnhancedInputComponent>(ControlledPawn->GetComponentByClass(UEnhancedInputComponent::StaticClass()));
                if (!EnhancedInputComponent)
                {
                    UE_LOG(LogTemp, Warning, TEXT("AURACRON: EnhancedInputComponent não encontrado"));
                }
            }
        }
    }

    // Inicializar sistema
    bSystemInitialized = InitializeUISystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers para atualizações
        GetWorld()->GetTimerManager().SetTimer(
            AnimationTimer,
            [this]()
            {
                ProcessUIAnimations(0.033f); // 30 FPS
            },
            0.033f,
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            MinimapUpdateTimer,
            [this]()
            {
                UpdateMinimap3D();
            },
            1.0f / MinimapConfiguration.UpdateFrequency,
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Interface do Usuário inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Interface do Usuário"));
    }
}

void UAuracronUIBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(AnimationTimer);
        GetWorld()->GetTimerManager().ClearTimer(MinimapUpdateTimer);
    }
    
    // Destruir todos os widgets ativos
    for (auto& WidgetPair : ActiveUIWidgets)
    {
        if (IsValid(WidgetPair.Value))
        {
            UCommonUserWidget* Widget = WidgetPair.Value;
            
            // Se for um CommonActivatableWidget, desativa primeiro
            if (UCommonActivatableWidget* ActivatableWidget = Cast<UCommonActivatableWidget>(Widget))
            {
                ActivatableWidget->DeactivateWidget();
            }
            
            // Remove do viewport se estiver adicionado diretamente
            if (Widget->IsInViewport())
            {
                Widget->RemoveFromViewport();
            }
            
            // Remove do parent se tiver um
            if (Widget->GetParent())
            {
                Widget->RemoveFromParent();
            }
            
            // Para animações ativas
            Widget->StopAllAnimations();
            
            // Marca para garbage collection
            Widget->MarkAsGarbage();
            
            UE_LOG(LogTemp, Log, TEXT("Widget %s destruído com sucesso"), *UEnum::GetValueAsString(WidgetPair.Key));
        }
    }
    ActiveUIWidgets.Empty();
    
    // Force garbage collection para limpar widgets imediatamente
    if (GEngine)
    {
        GEngine->ForceGarbageCollection(true);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronUIBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronUIBridge, UIConfiguration);
    DOREPLIFETIME(UAuracronUIBridge, UIStates);
    DOREPLIFETIME(UAuracronUIBridge, CurrentInputPlatform);
}

void UAuracronUIBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar animações de UI
    ProcessUIAnimations(DeltaTime);
    
    // Atualizar layout responsivo se necessário
    if (UIConfiguration.bUseResponsiveLayout)
    {
        UpdateResponsiveLayout();
    }
}

// === Core UI Management ===

bool UAuracronUIBridge::ShowUI(EAuracronUIType UIType, bool bAnimate)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema de UI não inicializado"));
        return false;
    }

    if (UIType == EAuracronUIType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tipo de UI inválido"));
        return false;
    }

    // Verificar se já está visível
    if (IsUIVisible(UIType))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: UI já está visível: %d"), (int32)UIType);
        return false;
    }

    // Obter ou criar widget
    UCommonUserWidget* Widget = GetUIWidget(UIType);
    if (!Widget)
    {
        // Tentar criar widget se tiver classe registrada
        if (RegisteredWidgetClasses.Contains(UIType))
        {
            Widget = CreateUIWidget(UIType, RegisteredWidgetClasses[UIType]);
        }
        
        if (!Widget)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar widget para UI: %d"), (int32)UIType);
            return false;
        }
    }

    // Mostrar widget
    Widget->SetVisibility(ESlateVisibility::Visible);
    Widget->AddToViewport();

    // Atualizar estado
    UIStates.Add(UIType, EAuracronUIState::Visible);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: UI mostrada: %d"), (int32)UIType);

    // Broadcast evento
    OnUIShown.Broadcast(UIType);

    return true;
}

bool UAuracronUIBridge::HideUI(EAuracronUIType UIType, bool bAnimate)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema de UI não inicializado"));
        return false;
    }

    if (!IsUIVisible(UIType))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: UI já está escondida: %d"), (int32)UIType);
        return false;
    }

    UCommonUserWidget* Widget = GetUIWidget(UIType);
    if (!Widget)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Widget não encontrado para UI: %d"), (int32)UIType);
        return false;
    }

    // Esconder widget
    Widget->SetVisibility(ESlateVisibility::Hidden);
    Widget->RemoveFromParent();

    // Atualizar estado
    UIStates.Add(UIType, EAuracronUIState::Hidden);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: UI escondida: %d"), (int32)UIType);

    // Broadcast evento
    OnUIHidden.Broadcast(UIType);

    return true;
}

bool UAuracronUIBridge::ToggleUI(EAuracronUIType UIType, bool bAnimate)
{
    if (IsUIVisible(UIType))
    {
        return HideUI(UIType, bAnimate);
    }
    else
    {
        return ShowUI(UIType, bAnimate);
    }
}

bool UAuracronUIBridge::IsUIVisible(EAuracronUIType UIType) const
{
    const EAuracronUIState* State = UIStates.Find(UIType);
    return State && *State == EAuracronUIState::Visible;
}

EAuracronUIState UAuracronUIBridge::GetUIState(EAuracronUIType UIType) const
{
    const EAuracronUIState* State = UIStates.Find(UIType);
    return State ? *State : EAuracronUIState::Hidden;
}

bool UAuracronUIBridge::SetUIWidget(EAuracronUIType UIType, TSubclassOf<UCommonUserWidget> WidgetClass)
{
    if (!WidgetClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Classe de widget inválida"));
        return false;
    }

    RegisteredWidgetClasses.Add(UIType, WidgetClass);
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Widget registrado para UI: %d"), (int32)UIType);

    return true;
}

UCommonUserWidget* UAuracronUIBridge::GetUIWidget(EAuracronUIType UIType) const
{
    const TObjectPtr<UCommonUserWidget>* Widget = ActiveUIWidgets.Find(UIType);
    return Widget ? Widget->Get() : nullptr;
}

// === HUD Management ===

bool UAuracronUIBridge::UpdateHealthBar(float CurrentHealth, float MaxHealth)
{
    if (!bSystemInitialized || MaxHealth <= 0.0f)
    {
        return false;
    }

    UCommonUserWidget* HUDWidget = GetUIWidget(EAuracronUIType::InGameHUD);
    if (!HUDWidget)
    {
        return false;
    }

    // Encontrar barra de HP no widget
    UProgressBar* HealthBar = Cast<UProgressBar>(HUDWidget->GetWidgetFromName(TEXT("HealthBar")));
    if (HealthBar)
    {
        float HealthPercentage = FMath::Clamp(CurrentHealth / MaxHealth, 0.0f, 1.0f);
        HealthBar->SetPercent(HealthPercentage);

        // Atualizar cor baseada na porcentagem
        FLinearColor HealthColor = FLinearColor::Lerp(FLinearColor::Red, CombatHUDConfiguration.HealthColor, HealthPercentage);
        HealthBar->SetFillColorAndOpacity(HealthColor);

        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Barra de HP atualizada: %.2f/%.2f (%.1f%%)"), CurrentHealth, MaxHealth, HealthPercentage * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronUIBridge::UpdateManaBar(float CurrentMana, float MaxMana)
{
    if (!bSystemInitialized || MaxMana <= 0.0f)
    {
        return false;
    }

    UCommonUserWidget* HUDWidget = GetUIWidget(EAuracronUIType::InGameHUD);
    if (!HUDWidget)
    {
        return false;
    }

    UProgressBar* ManaBar = Cast<UProgressBar>(HUDWidget->GetWidgetFromName(TEXT("ManaBar")));
    if (ManaBar)
    {
        float ManaPercentage = FMath::Clamp(CurrentMana / MaxMana, 0.0f, 1.0f);
        ManaBar->SetPercent(ManaPercentage);
        ManaBar->SetFillColorAndOpacity(CombatHUDConfiguration.ManaColor);

        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Barra de mana atualizada: %.2f/%.2f (%.1f%%)"), CurrentMana, MaxMana, ManaPercentage * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronUIBridge::UpdateAbilityCooldown(const FString& AbilitySlot, float CooldownRemaining, float MaxCooldown)
{
    if (!bSystemInitialized || MaxCooldown <= 0.0f)
    {
        return false;
    }

    UCommonUserWidget* HUDWidget = GetUIWidget(EAuracronUIType::InGameHUD);
    if (!HUDWidget)
    {
        return false;
    }

    FString WidgetName = FString::Printf(TEXT("%sAbilityButton"), *AbilitySlot);
    UWidget* AbilityWidget = HUDWidget->GetWidgetFromName(*WidgetName);

    if (UButton* AbilityButton = Cast<UButton>(AbilityWidget))
    {
        // Atualizar visual do cooldown
        bool bOnCooldown = CooldownRemaining > 0.0f;
        AbilityButton->SetIsEnabled(!bOnCooldown);

        if (bOnCooldown)
        {
            float CooldownPercentage = CooldownRemaining / MaxCooldown;
            
            // Implementar overlay de cooldown visual
            // Procurar por um overlay de cooldown existente ou criar um novo
            FString OverlayName = FString::Printf(TEXT("%sCooldownOverlay"), *AbilitySlot);
            UWidget* OverlayWidget = HUDWidget->GetWidgetFromName(*OverlayName);
            
            if (!OverlayWidget)
            {
                // Criar um novo overlay de cooldown se não existir
                if (UOverlay* ParentOverlay = Cast<UOverlay>(AbilityButton->GetParent()))
                {
                    // Criar uma imagem circular para o overlay de cooldown
                    UImage* CooldownOverlay = CreateWidget<UImage>(GetWorld(), UImage::StaticClass());
                    if (CooldownOverlay)
                    {
                        CooldownOverlay->SetBrushFromTexture(LoadObject<UTexture2D>(nullptr, TEXT("/Engine/EngineResources/WhiteSquareTexture.WhiteSquareTexture")));
                        CooldownOverlay->SetColorAndOpacity(FLinearColor(0.0f, 0.0f, 0.0f, 0.7f)); // Overlay escuro semi-transparente
                        CooldownOverlay->SetRenderTransformPivot(FVector2D(0.5f, 0.5f));
                        
                        // Configurar material para criar efeito circular de cooldown
                        if (UMaterialInterface* CooldownMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/EngineMaterials/Widget_CircularThrobber.Widget_CircularThrobber")))
                        {
                            UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(CooldownMaterial, CooldownOverlay);
                            if (DynamicMaterial)
                            {
                                DynamicMaterial->SetScalarParameterValue(TEXT("Percent"), 1.0f - CooldownPercentage);
                                CooldownOverlay->SetBrushFromMaterial(DynamicMaterial);
                            }
                        }
                        
                        // Adicionar ao overlay parent com o mesmo slot do botão
                        UOverlaySlot* OverlaySlot = ParentOverlay->AddChildToOverlay(CooldownOverlay);
                        if (OverlaySlot)
                        {
                            OverlaySlot->SetHorizontalAlignment(HAlign_Fill);
                            OverlaySlot->SetVerticalAlignment(VAlign_Fill);
                        }
                        
                        // Definir nome para referência futura
                        CooldownOverlay->SetName(*OverlayName);
                        OverlayWidget = CooldownOverlay;
                    }
                }
            }
            
            // Atualizar o overlay existente
            if (UImage* CooldownImage = Cast<UImage>(OverlayWidget))
            {
                CooldownImage->SetVisibility(ESlateVisibility::HitTestInvisible);
                
                // Atualizar o material dinâmico com a nova porcentagem
                if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(CooldownImage->GetBrush().GetResourceObject()))
                {
                    DynamicMaterial->SetScalarParameterValue(TEXT("Percent"), 1.0f - CooldownPercentage);
                }
                
                // Adicionar texto de tempo restante
                FString CooldownText = FString::Printf(TEXT("%.1fs"), CooldownRemaining);
                if (UTextBlock* CooldownTextWidget = Cast<UTextBlock>(HUDWidget->GetWidgetFromName(*FString::Printf(TEXT("%sCooldownText"), *AbilitySlot))))
                {
                    CooldownTextWidget->SetText(FText::FromString(CooldownText));
                    CooldownTextWidget->SetVisibility(ESlateVisibility::HitTestInvisible);
                }
            }
            
            UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Cooldown %s: %.2f/%.2f (%.1f%%) - Visual overlay updated"), *AbilitySlot, CooldownRemaining, MaxCooldown, CooldownPercentage * 100.0f);
        }
        else
        {
            // Esconder o overlay quando não há cooldown
            FString OverlayName = FString::Printf(TEXT("%sCooldownOverlay"), *AbilitySlot);
            if (UWidget* OverlayWidget = HUDWidget->GetWidgetFromName(*OverlayName))
            {
                OverlayWidget->SetVisibility(ESlateVisibility::Collapsed);
            }
            
            // Esconder texto de cooldown
            FString TextName = FString::Printf(TEXT("%sCooldownText"), *AbilitySlot);
            if (UWidget* TextWidget = HUDWidget->GetWidgetFromName(*TextName))
            {
                TextWidget->SetVisibility(ESlateVisibility::Collapsed);
            }
        }

        return true;
    }

    return false;
}
