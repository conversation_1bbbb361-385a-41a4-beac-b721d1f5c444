// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGCustomNodeSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGCustomNodeSystem_generated_h
#error "AuracronPCGCustomNodeSystem.generated.h already included, missing '#pragma once' in AuracronPCGCustomNodeSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGCustomNodeSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronPCGCustomNodeBuilder;
class UAuracronPCGCustomNodeRegistry;
class UBlueprint;
class UObject;
class UPCGSettings;
enum class EAuracronPCGCustomNodeExecutionMode : uint8;
enum class EAuracronPCGCustomNodeTemplateType : uint8;
enum class EAuracronPCGCustomNodeValidationLevel : uint8;
enum class EAuracronPCGCustomParameterType : uint8;
enum class EAuracronPCGNodeCategory : uint8;
enum class EPCGDataType : uint32;
struct FAuracronPCGCustomNodeTemplate;
struct FAuracronPCGCustomParameterDescriptor;
struct FAuracronPCGCustomPinDescriptor;
struct FLinearColor;

// ********** Begin ScriptStruct FAuracronPCGCustomParameterDescriptor *****************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_103_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGCustomParameterDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGCustomParameterDescriptor;
// ********** End ScriptStruct FAuracronPCGCustomParameterDescriptor *******************************

// ********** Begin ScriptStruct FAuracronPCGCustomPinDescriptor ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_178_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGCustomPinDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGCustomPinDescriptor;
// ********** End ScriptStruct FAuracronPCGCustomPinDescriptor *************************************

// ********** Begin ScriptStruct FAuracronPCGCustomNodeTemplate ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_236_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGCustomNodeTemplate;
// ********** End ScriptStruct FAuracronPCGCustomNodeTemplate **************************************

// ********** Begin Class UAuracronPCGCustomNodeFactory ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_322_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetAvailableTemplateFiles); \
	DECLARE_FUNCTION(execLoadTemplateFromFile); \
	DECLARE_FUNCTION(execSaveTemplateToFile); \
	DECLARE_FUNCTION(execCompileBlueprintNode); \
	DECLARE_FUNCTION(execCreateBlueprintFromTemplate); \
	DECLARE_FUNCTION(execGetTemplateValidationErrors); \
	DECLARE_FUNCTION(execValidateTemplate); \
	DECLARE_FUNCTION(execGetTemplate); \
	DECLARE_FUNCTION(execGetRegisteredTemplates); \
	DECLARE_FUNCTION(execUnregisterCustomNodeTemplate); \
	DECLARE_FUNCTION(execRegisterCustomNodeTemplate); \
	DECLARE_FUNCTION(execCreateCustomNodeInstance); \
	DECLARE_FUNCTION(execCreateCustomNodeClass);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeFactory_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_322_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCustomNodeFactory(); \
	friend struct Z_Construct_UClass_UAuracronPCGCustomNodeFactory_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeFactory_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCustomNodeFactory, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCustomNodeFactory_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCustomNodeFactory)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_322_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGCustomNodeFactory(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCustomNodeFactory(UAuracronPCGCustomNodeFactory&&) = delete; \
	UAuracronPCGCustomNodeFactory(const UAuracronPCGCustomNodeFactory&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCustomNodeFactory); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCustomNodeFactory); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGCustomNodeFactory) \
	NO_API virtual ~UAuracronPCGCustomNodeFactory();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_319_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_322_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_322_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_322_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_322_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCustomNodeFactory;

// ********** End Class UAuracronPCGCustomNodeFactory **********************************************

// ********** Begin Class UAuracronPCGCustomNodeBuilder ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_383_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execReset); \
	DECLARE_FUNCTION(execBuildNodeInstance); \
	DECLARE_FUNCTION(execBuildNodeClass); \
	DECLARE_FUNCTION(execBuildTemplate); \
	DECLARE_FUNCTION(execSetVersion); \
	DECLARE_FUNCTION(execSetAuthor); \
	DECLARE_FUNCTION(execSetValidationLevel); \
	DECLARE_FUNCTION(execSetNativeClassName); \
	DECLARE_FUNCTION(execSetBlueprintImplementation); \
	DECLARE_FUNCTION(execSetExecutionFunction); \
	DECLARE_FUNCTION(execSetExecutionMode); \
	DECLARE_FUNCTION(execAddOutputPin); \
	DECLARE_FUNCTION(execAddInputPin); \
	DECLARE_FUNCTION(execAddParameter); \
	DECLARE_FUNCTION(execSetNodeColor); \
	DECLARE_FUNCTION(execAddTag); \
	DECLARE_FUNCTION(execSetCategory); \
	DECLARE_FUNCTION(execSetTemplateType); \
	DECLARE_FUNCTION(execSetDescription); \
	DECLARE_FUNCTION(execSetDisplayName); \
	DECLARE_FUNCTION(execSetNodeName);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_383_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCustomNodeBuilder(); \
	friend struct Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCustomNodeBuilder, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCustomNodeBuilder_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCustomNodeBuilder)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_383_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCustomNodeBuilder(UAuracronPCGCustomNodeBuilder&&) = delete; \
	UAuracronPCGCustomNodeBuilder(const UAuracronPCGCustomNodeBuilder&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCustomNodeBuilder); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCustomNodeBuilder); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGCustomNodeBuilder) \
	NO_API virtual ~UAuracronPCGCustomNodeBuilder();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_380_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_383_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_383_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_383_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_383_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCustomNodeBuilder;

// ********** End Class UAuracronPCGCustomNodeBuilder **********************************************

// ********** Begin Class UAuracronPCGCustomNodeRegistry *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_469_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execClearRegistry); \
	DECLARE_FUNCTION(execRefreshRegistry); \
	DECLARE_FUNCTION(execValidateAllTemplates); \
	DECLARE_FUNCTION(execGetNodeInstance); \
	DECLARE_FUNCTION(execRegisterNodeInstance); \
	DECLARE_FUNCTION(execCreateNodeInstance); \
	DECLARE_FUNCTION(execGetTemplateNames); \
	DECLARE_FUNCTION(execGetTemplatesByType); \
	DECLARE_FUNCTION(execGetTemplatesByCategory); \
	DECLARE_FUNCTION(execGetAllTemplates); \
	DECLARE_FUNCTION(execGetTemplate); \
	DECLARE_FUNCTION(execIsTemplateRegistered); \
	DECLARE_FUNCTION(execUnregisterTemplate); \
	DECLARE_FUNCTION(execRegisterTemplate); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_469_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCustomNodeRegistry(); \
	friend struct Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCustomNodeRegistry, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCustomNodeRegistry_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCustomNodeRegistry)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_469_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGCustomNodeRegistry(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCustomNodeRegistry(UAuracronPCGCustomNodeRegistry&&) = delete; \
	UAuracronPCGCustomNodeRegistry(const UAuracronPCGCustomNodeRegistry&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCustomNodeRegistry); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCustomNodeRegistry); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGCustomNodeRegistry) \
	NO_API virtual ~UAuracronPCGCustomNodeRegistry();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_466_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_469_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_469_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_469_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_469_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCustomNodeRegistry;

// ********** End Class UAuracronPCGCustomNodeRegistry *********************************************

// ********** Begin Class UAuracronPCGCustomNodeUtils **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_541_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGenerateTemplatesFromDirectory); \
	DECLARE_FUNCTION(execGenerateTemplateFromClass); \
	DECLARE_FUNCTION(execGenerateTemplateFromBlueprint); \
	DECLARE_FUNCTION(execStringToExecutionMode); \
	DECLARE_FUNCTION(execExecutionModeToString); \
	DECLARE_FUNCTION(execStringToParameterType); \
	DECLARE_FUNCTION(execParameterTypeToString); \
	DECLARE_FUNCTION(execSanitizeNodeName); \
	DECLARE_FUNCTION(execSanitizePinName); \
	DECLARE_FUNCTION(execSanitizeParameterName); \
	DECLARE_FUNCTION(execValidateNodeName); \
	DECLARE_FUNCTION(execValidatePinName); \
	DECLARE_FUNCTION(execValidateParameterName); \
	DECLARE_FUNCTION(execCreateOutputPin); \
	DECLARE_FUNCTION(execCreateInputPin); \
	DECLARE_FUNCTION(execCreateEnumParameter); \
	DECLARE_FUNCTION(execCreateColorParameter); \
	DECLARE_FUNCTION(execCreateVectorParameter); \
	DECLARE_FUNCTION(execCreateStringParameter); \
	DECLARE_FUNCTION(execCreateBoolParameter); \
	DECLARE_FUNCTION(execCreateIntParameter); \
	DECLARE_FUNCTION(execCreateFloatParameter); \
	DECLARE_FUNCTION(execCreateBasicTemplate);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_541_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCustomNodeUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGCustomNodeUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCustomNodeUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCustomNodeUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCustomNodeUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_541_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGCustomNodeUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCustomNodeUtils(UAuracronPCGCustomNodeUtils&&) = delete; \
	UAuracronPCGCustomNodeUtils(const UAuracronPCGCustomNodeUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCustomNodeUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCustomNodeUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGCustomNodeUtils) \
	NO_API virtual ~UAuracronPCGCustomNodeUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_538_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_541_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_541_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_541_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h_541_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCustomNodeUtils;

// ********** End Class UAuracronPCGCustomNodeUtils ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeSystem_h

// ********** Begin Enum EAuracronPCGCustomParameterType *******************************************
#define FOREACH_ENUM_EAURACRONPCGCUSTOMPARAMETERTYPE(op) \
	op(EAuracronPCGCustomParameterType::Boolean) \
	op(EAuracronPCGCustomParameterType::Integer) \
	op(EAuracronPCGCustomParameterType::Float) \
	op(EAuracronPCGCustomParameterType::String) \
	op(EAuracronPCGCustomParameterType::Vector) \
	op(EAuracronPCGCustomParameterType::Rotator) \
	op(EAuracronPCGCustomParameterType::Transform) \
	op(EAuracronPCGCustomParameterType::Color) \
	op(EAuracronPCGCustomParameterType::Object) \
	op(EAuracronPCGCustomParameterType::Class) \
	op(EAuracronPCGCustomParameterType::Enum) \
	op(EAuracronPCGCustomParameterType::Struct) \
	op(EAuracronPCGCustomParameterType::Array) \
	op(EAuracronPCGCustomParameterType::Map) \
	op(EAuracronPCGCustomParameterType::Set) \
	op(EAuracronPCGCustomParameterType::Delegate) \
	op(EAuracronPCGCustomParameterType::Custom) 

enum class EAuracronPCGCustomParameterType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCustomParameterType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCustomParameterType>();
// ********** End Enum EAuracronPCGCustomParameterType *********************************************

// ********** Begin Enum EAuracronPCGCustomNodeExecutionMode ***************************************
#define FOREACH_ENUM_EAURACRONPCGCUSTOMNODEEXECUTIONMODE(op) \
	op(EAuracronPCGCustomNodeExecutionMode::Synchronous) \
	op(EAuracronPCGCustomNodeExecutionMode::Asynchronous) \
	op(EAuracronPCGCustomNodeExecutionMode::Threaded) \
	op(EAuracronPCGCustomNodeExecutionMode::GPU) \
	op(EAuracronPCGCustomNodeExecutionMode::Distributed) \
	op(EAuracronPCGCustomNodeExecutionMode::Custom) 

enum class EAuracronPCGCustomNodeExecutionMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCustomNodeExecutionMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCustomNodeExecutionMode>();
// ********** End Enum EAuracronPCGCustomNodeExecutionMode *****************************************

// ********** Begin Enum EAuracronPCGCustomNodeTemplateType ****************************************
#define FOREACH_ENUM_EAURACRONPCGCUSTOMNODETEMPLATETYPE(op) \
	op(EAuracronPCGCustomNodeTemplateType::Generator) \
	op(EAuracronPCGCustomNodeTemplateType::Modifier) \
	op(EAuracronPCGCustomNodeTemplateType::Filter) \
	op(EAuracronPCGCustomNodeTemplateType::Sampler) \
	op(EAuracronPCGCustomNodeTemplateType::Transformer) \
	op(EAuracronPCGCustomNodeTemplateType::Analyzer) \
	op(EAuracronPCGCustomNodeTemplateType::Utility) \
	op(EAuracronPCGCustomNodeTemplateType::Debug) \
	op(EAuracronPCGCustomNodeTemplateType::Custom) 

enum class EAuracronPCGCustomNodeTemplateType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCustomNodeTemplateType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCustomNodeTemplateType>();
// ********** End Enum EAuracronPCGCustomNodeTemplateType ******************************************

// ********** Begin Enum EAuracronPCGCustomNodeValidationLevel *************************************
#define FOREACH_ENUM_EAURACRONPCGCUSTOMNODEVALIDATIONLEVEL(op) \
	op(EAuracronPCGCustomNodeValidationLevel::None) \
	op(EAuracronPCGCustomNodeValidationLevel::Basic) \
	op(EAuracronPCGCustomNodeValidationLevel::Standard) \
	op(EAuracronPCGCustomNodeValidationLevel::Strict) \
	op(EAuracronPCGCustomNodeValidationLevel::Custom) 

enum class EAuracronPCGCustomNodeValidationLevel : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCustomNodeValidationLevel> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCustomNodeValidationLevel>();
// ********** End Enum EAuracronPCGCustomNodeValidationLevel ***************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
