// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageProcedural.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageProcedural_generated_h
#error "AuracronFoliageProcedural.generated.h already included, missing '#pragma once' in AuracronFoliageProcedural.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageProcedural_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronFoliageProceduralManager;
class UPCGComponent;
class UPCGGraph;
class UWorld;
struct FAuracronDensityMapConfiguration;
struct FAuracronPlacementRuleConfiguration;
struct FAuracronProceduralPlacementConfiguration;
struct FAuracronProceduralPlacementResult;

// ********** Begin ScriptStruct FAuracronDensityMapConfiguration **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_135_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDensityMapConfiguration;
// ********** End ScriptStruct FAuracronDensityMapConfiguration ************************************

// ********** Begin ScriptStruct FAuracronSlopeFilterConfiguration *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_232_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSlopeFilterConfiguration;
// ********** End ScriptStruct FAuracronSlopeFilterConfiguration ***********************************

// ********** Begin ScriptStruct FAuracronHeightConstraintConfiguration ****************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_293_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronHeightConstraintConfiguration;
// ********** End ScriptStruct FAuracronHeightConstraintConfiguration ******************************

// ********** Begin ScriptStruct FAuracronPlacementRuleConfiguration *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_354_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPlacementRuleConfiguration;
// ********** End ScriptStruct FAuracronPlacementRuleConfiguration *********************************

// ********** Begin ScriptStruct FAuracronProceduralPlacementConfiguration *************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_438_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronProceduralPlacementConfiguration;
// ********** End ScriptStruct FAuracronProceduralPlacementConfiguration ***************************

// ********** Begin ScriptStruct FAuracronProceduralPlacementResult ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_532_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronProceduralPlacementResult;
// ********** End ScriptStruct FAuracronProceduralPlacementResult **********************************

// ********** Begin Delegate FOnProceduralGenerationCompleted **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_721_DELEGATE \
static void FOnProceduralGenerationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnProceduralGenerationCompleted, const FString& GenerationId, FAuracronProceduralPlacementResult Result);


// ********** End Delegate FOnProceduralGenerationCompleted ****************************************

// ********** Begin Delegate FOnDensityMapUpdated **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_722_DELEGATE \
static void FOnDensityMapUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnDensityMapUpdated, FBox UpdatedArea, float AverageDensity);


// ********** End Delegate FOnDensityMapUpdated ****************************************************

// ********** Begin Delegate FOnPlacementRuleAdded *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_723_DELEGATE \
static void FOnPlacementRuleAdded_DelegateWrapper(const FMulticastScriptDelegate& OnPlacementRuleAdded, const FString& RuleId, FAuracronPlacementRuleConfiguration Rule);


// ********** End Delegate FOnPlacementRuleAdded ***************************************************

// ********** Begin Delegate FOnPlacementRuleRemoved ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_724_DELEGATE \
static void FOnPlacementRuleRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnPlacementRuleRemoved, const FString& RuleId);


// ********** End Delegate FOnPlacementRuleRemoved *************************************************

// ********** Begin Class UAuracronFoliageProceduralManager ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_591_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDrawDebugVisualization); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetGenerationStatistics); \
	DECLARE_FUNCTION(execGetAverageDensity); \
	DECLARE_FUNCTION(execGetTotalGeneratedPoints); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetPCGPointData); \
	DECLARE_FUNCTION(execExecutePCGGraph); \
	DECLARE_FUNCTION(execCreatePCGComponent); \
	DECLARE_FUNCTION(execClearProceduralPlacement); \
	DECLARE_FUNCTION(execGenerateTransformsInArea); \
	DECLARE_FUNCTION(execGenerateProceduralPlacementAsync); \
	DECLARE_FUNCTION(execGenerateProceduralPlacement); \
	DECLARE_FUNCTION(execEvaluatePlacementRules); \
	DECLARE_FUNCTION(execGetAllPlacementRules); \
	DECLARE_FUNCTION(execGetPlacementRule); \
	DECLARE_FUNCTION(execRemovePlacementRule); \
	DECLARE_FUNCTION(execAddPlacementRule); \
	DECLARE_FUNCTION(execApplyNoiseToPoints); \
	DECLARE_FUNCTION(execGenerateNoiseBasedPoints); \
	DECLARE_FUNCTION(execSampleNoiseAtLocation); \
	DECLARE_FUNCTION(execGetRelativeHeight); \
	DECLARE_FUNCTION(execFilterPointsByHeight); \
	DECLARE_FUNCTION(execPassesHeightConstraints); \
	DECLARE_FUNCTION(execFilterPointsBySlope); \
	DECLARE_FUNCTION(execPassesSlopeFilter); \
	DECLARE_FUNCTION(execCalculateSlopeAtLocation); \
	DECLARE_FUNCTION(execClearDensityMap); \
	DECLARE_FUNCTION(execUpdateDensityMap); \
	DECLARE_FUNCTION(execSampleDensityInArea); \
	DECLARE_FUNCTION(execSampleDensityAtLocation); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageProceduralManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_591_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageProceduralManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageProceduralManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageProceduralManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageProceduralManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageProceduralManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_591_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageProceduralManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageProceduralManager(UAuracronFoliageProceduralManager&&) = delete; \
	UAuracronFoliageProceduralManager(const UAuracronFoliageProceduralManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageProceduralManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageProceduralManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageProceduralManager) \
	NO_API virtual ~UAuracronFoliageProceduralManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_588_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_591_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_591_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_591_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h_591_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageProceduralManager;

// ********** End Class UAuracronFoliageProceduralManager ******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h

// ********** Begin Enum EAuracronDensityMapType ***************************************************
#define FOREACH_ENUM_EAURACRONDENSITYMAPTYPE(op) \
	op(EAuracronDensityMapType::Uniform) \
	op(EAuracronDensityMapType::Texture) \
	op(EAuracronDensityMapType::Noise) \
	op(EAuracronDensityMapType::Landscape) \
	op(EAuracronDensityMapType::Spline) \
	op(EAuracronDensityMapType::Custom) 

enum class EAuracronDensityMapType : uint8;
template<> struct TIsUEnumClass<EAuracronDensityMapType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronDensityMapType>();
// ********** End Enum EAuracronDensityMapType *****************************************************

// ********** Begin Enum EAuracronSlopeFilterMode **************************************************
#define FOREACH_ENUM_EAURACRONSLOPEFILTERMODE(op) \
	op(EAuracronSlopeFilterMode::None) \
	op(EAuracronSlopeFilterMode::MinMax) \
	op(EAuracronSlopeFilterMode::Curve) \
	op(EAuracronSlopeFilterMode::Adaptive) \
	op(EAuracronSlopeFilterMode::Layered) 

enum class EAuracronSlopeFilterMode : uint8;
template<> struct TIsUEnumClass<EAuracronSlopeFilterMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronSlopeFilterMode>();
// ********** End Enum EAuracronSlopeFilterMode ****************************************************

// ********** Begin Enum EAuracronHeightConstraintMode *********************************************
#define FOREACH_ENUM_EAURACRONHEIGHTCONSTRAINTMODE(op) \
	op(EAuracronHeightConstraintMode::None) \
	op(EAuracronHeightConstraintMode::Absolute) \
	op(EAuracronHeightConstraintMode::Relative) \
	op(EAuracronHeightConstraintMode::SeaLevel) \
	op(EAuracronHeightConstraintMode::Terrain) \
	op(EAuracronHeightConstraintMode::Custom) 

enum class EAuracronHeightConstraintMode : uint8;
template<> struct TIsUEnumClass<EAuracronHeightConstraintMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronHeightConstraintMode>();
// ********** End Enum EAuracronHeightConstraintMode ***********************************************

// ********** Begin Enum EAuracronNoiseDistributionType ********************************************
#define FOREACH_ENUM_EAURACRONNOISEDISTRIBUTIONTYPE(op) \
	op(EAuracronNoiseDistributionType::Perlin) \
	op(EAuracronNoiseDistributionType::Simplex) \
	op(EAuracronNoiseDistributionType::Ridged) \
	op(EAuracronNoiseDistributionType::Voronoi) \
	op(EAuracronNoiseDistributionType::Fractal) \
	op(EAuracronNoiseDistributionType::Custom) 

enum class EAuracronNoiseDistributionType : uint8;
template<> struct TIsUEnumClass<EAuracronNoiseDistributionType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronNoiseDistributionType>();
// ********** End Enum EAuracronNoiseDistributionType **********************************************

// ********** Begin Enum EAuracronPlacementRuleType ************************************************
#define FOREACH_ENUM_EAURACRONPLACEMENTRULETYPE(op) \
	op(EAuracronPlacementRuleType::Distance) \
	op(EAuracronPlacementRuleType::Density) \
	op(EAuracronPlacementRuleType::Biome) \
	op(EAuracronPlacementRuleType::Material) \
	op(EAuracronPlacementRuleType::Exclusion) \
	op(EAuracronPlacementRuleType::Custom) 

enum class EAuracronPlacementRuleType : uint8;
template<> struct TIsUEnumClass<EAuracronPlacementRuleType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronPlacementRuleType>();
// ********** End Enum EAuracronPlacementRuleType **************************************************

// ********** Begin Enum EAuracronPlacementPriority ************************************************
#define FOREACH_ENUM_EAURACRONPLACEMENTPRIORITY(op) \
	op(EAuracronPlacementPriority::VeryLow) \
	op(EAuracronPlacementPriority::Low) \
	op(EAuracronPlacementPriority::Normal) \
	op(EAuracronPlacementPriority::High) \
	op(EAuracronPlacementPriority::VeryHigh) \
	op(EAuracronPlacementPriority::Critical) 

enum class EAuracronPlacementPriority : uint8;
template<> struct TIsUEnumClass<EAuracronPlacementPriority> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronPlacementPriority>();
// ********** End Enum EAuracronPlacementPriority **************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
