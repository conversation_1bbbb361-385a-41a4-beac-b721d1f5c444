// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageCollision.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageCollision_generated_h
#error "AuracronFoliageCollision.generated.h already included, missing '#pragma once' in AuracronFoliageCollision.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageCollision_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronFoliageCollisionManager;
class UHierarchicalInstancedStaticMeshComponent;
class UStaticMesh;
class UWorld;
enum class EAuracronFoliageCollisionType : uint8;
enum class EAuracronPhysicsInteractionType : uint8;
enum class EAuracronTramplingEffect : uint8;
struct FAuracronCollisionMeshData;
struct FAuracronCollisionPerformanceData;
struct FAuracronDestructibleFoliageData;
struct FAuracronFoliageCollisionConfiguration;
struct FAuracronTramplingEffectData;
struct FHitResult;

// ********** Begin ScriptStruct FAuracronFoliageCollisionConfiguration ****************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_123_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliageCollisionConfiguration;
// ********** End ScriptStruct FAuracronFoliageCollisionConfiguration ******************************

// ********** Begin ScriptStruct FAuracronCollisionMeshData ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_253_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCollisionMeshData;
// ********** End ScriptStruct FAuracronCollisionMeshData ******************************************

// ********** Begin ScriptStruct FAuracronDestructibleFoliageData **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_328_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDestructibleFoliageData;
// ********** End ScriptStruct FAuracronDestructibleFoliageData ************************************

// ********** Begin ScriptStruct FAuracronTramplingEffectData **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_407_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTramplingEffectData;
// ********** End ScriptStruct FAuracronTramplingEffectData ****************************************

// ********** Begin ScriptStruct FAuracronCollisionPerformanceData *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_490_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCollisionPerformanceData;
// ********** End ScriptStruct FAuracronCollisionPerformanceData ***********************************

// ********** Begin Delegate FOnCollisionMeshGenerated *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_696_DELEGATE \
static void FOnCollisionMeshGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnCollisionMeshGenerated, const FString& CollisionMeshId, FAuracronCollisionMeshData MeshData);


// ********** End Delegate FOnCollisionMeshGenerated ***********************************************

// ********** Begin Delegate FOnFoliageDestroyed ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_697_DELEGATE \
static void FOnFoliageDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageDestroyed, const FString& DestructibleId);


// ********** End Delegate FOnFoliageDestroyed *****************************************************

// ********** Begin Delegate FOnTramplingEffectCreated *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_698_DELEGATE \
static void FOnTramplingEffectCreated_DelegateWrapper(const FMulticastScriptDelegate& OnTramplingEffectCreated, const FString& TramplingId, FAuracronTramplingEffectData TramplingData);


// ********** End Delegate FOnTramplingEffectCreated ***********************************************

// ********** Begin Delegate FOnPhysicsInteraction *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_699_DELEGATE \
static void FOnPhysicsInteraction_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsInteraction, const FString& FoliageInstanceId);


// ********** End Delegate FOnPhysicsInteraction ***************************************************

// ********** Begin Class UAuracronFoliageCollisionManager *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_552_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogCollisionStatistics); \
	DECLARE_FUNCTION(execDrawDebugCollisionMeshes); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetDestructibleCount); \
	DECLARE_FUNCTION(execGetActiveCollisionCount); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execGetPerformanceData); \
	DECLARE_FUNCTION(execSphereTraceAgainstFoliage); \
	DECLARE_FUNCTION(execLineTraceAgainstFoliage); \
	DECLARE_FUNCTION(execGetFoliageInstancesInBox); \
	DECLARE_FUNCTION(execGetFoliageInstancesInRadius); \
	DECLARE_FUNCTION(execUpdateCollisionBasedOnLOD); \
	DECLARE_FUNCTION(execSetCollisionLOD); \
	DECLARE_FUNCTION(execOptimizeCollisionForDistance); \
	DECLARE_FUNCTION(execGetAllTramplingEffects); \
	DECLARE_FUNCTION(execGetTramplingEffect); \
	DECLARE_FUNCTION(execApplyTramplingToArea); \
	DECLARE_FUNCTION(execRemoveTramplingEffect); \
	DECLARE_FUNCTION(execUpdateTramplingEffect); \
	DECLARE_FUNCTION(execCreateTramplingEffect); \
	DECLARE_FUNCTION(execGetAllDestructibleFoliage); \
	DECLARE_FUNCTION(execGetDestructibleFoliage); \
	DECLARE_FUNCTION(execRegenerateFoliageInstance); \
	DECLARE_FUNCTION(execDestroyFoliageInstance); \
	DECLARE_FUNCTION(execUpdateDestructibleFoliage); \
	DECLARE_FUNCTION(execCreateDestructibleFoliage); \
	DECLARE_FUNCTION(execEnablePhysicsForFoliageType); \
	DECLARE_FUNCTION(execApplyPhysicsToFoliageInstance); \
	DECLARE_FUNCTION(execGetFoliagePhysicsType); \
	DECLARE_FUNCTION(execSetFoliagePhysicsType); \
	DECLARE_FUNCTION(execGetAllCollisionMeshes); \
	DECLARE_FUNCTION(execGetCollisionMesh); \
	DECLARE_FUNCTION(execRemoveCollisionMesh); \
	DECLARE_FUNCTION(execUpdateCollisionMesh); \
	DECLARE_FUNCTION(execGenerateCollisionMesh); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageCollisionManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_552_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageCollisionManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageCollisionManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageCollisionManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageCollisionManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageCollisionManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_552_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageCollisionManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageCollisionManager(UAuracronFoliageCollisionManager&&) = delete; \
	UAuracronFoliageCollisionManager(const UAuracronFoliageCollisionManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageCollisionManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageCollisionManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageCollisionManager) \
	NO_API virtual ~UAuracronFoliageCollisionManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_549_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_552_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_552_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_552_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h_552_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageCollisionManager;

// ********** End Class UAuracronFoliageCollisionManager *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h

// ********** Begin Enum EAuracronFoliageCollisionType *********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGECOLLISIONTYPE(op) \
	op(EAuracronFoliageCollisionType::NoCollision) \
	op(EAuracronFoliageCollisionType::QueryOnly) \
	op(EAuracronFoliageCollisionType::PhysicsOnly) \
	op(EAuracronFoliageCollisionType::CollisionEnabled) \
	op(EAuracronFoliageCollisionType::Destructible) \
	op(EAuracronFoliageCollisionType::Interactive) \
	op(EAuracronFoliageCollisionType::Trampling) \
	op(EAuracronFoliageCollisionType::Custom) 

enum class EAuracronFoliageCollisionType : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageCollisionType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageCollisionType>();
// ********** End Enum EAuracronFoliageCollisionType ***********************************************

// ********** Begin Enum EAuracronPhysicsInteractionType *******************************************
#define FOREACH_ENUM_EAURACRONPHYSICSINTERACTIONTYPE(op) \
	op(EAuracronPhysicsInteractionType::Static) \
	op(EAuracronPhysicsInteractionType::Kinematic) \
	op(EAuracronPhysicsInteractionType::Simulated) \
	op(EAuracronPhysicsInteractionType::Destructible) \
	op(EAuracronPhysicsInteractionType::Trampling) \
	op(EAuracronPhysicsInteractionType::WindInteraction) \
	op(EAuracronPhysicsInteractionType::Custom) 

enum class EAuracronPhysicsInteractionType : uint8;
template<> struct TIsUEnumClass<EAuracronPhysicsInteractionType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronPhysicsInteractionType>();
// ********** End Enum EAuracronPhysicsInteractionType *********************************************

// ********** Begin Enum EAuracronDestructibleBehavior *********************************************
#define FOREACH_ENUM_EAURACRONDESTRUCTIBLEBEHAVIOR(op) \
	op(EAuracronDestructibleBehavior::None) \
	op(EAuracronDestructibleBehavior::Break) \
	op(EAuracronDestructibleBehavior::Shatter) \
	op(EAuracronDestructibleBehavior::Crumble) \
	op(EAuracronDestructibleBehavior::Burn) \
	op(EAuracronDestructibleBehavior::Dissolve) \
	op(EAuracronDestructibleBehavior::Explode) \
	op(EAuracronDestructibleBehavior::Custom) 

enum class EAuracronDestructibleBehavior : uint8;
template<> struct TIsUEnumClass<EAuracronDestructibleBehavior> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronDestructibleBehavior>();
// ********** End Enum EAuracronDestructibleBehavior ***********************************************

// ********** Begin Enum EAuracronTramplingEffect **************************************************
#define FOREACH_ENUM_EAURACRONTRAMPLINGEFFECT(op) \
	op(EAuracronTramplingEffect::None) \
	op(EAuracronTramplingEffect::Bend) \
	op(EAuracronTramplingEffect::Flatten) \
	op(EAuracronTramplingEffect::Crush) \
	op(EAuracronTramplingEffect::Damage) \
	op(EAuracronTramplingEffect::Destroy) \
	op(EAuracronTramplingEffect::Recover) \
	op(EAuracronTramplingEffect::Custom) 

enum class EAuracronTramplingEffect : uint8;
template<> struct TIsUEnumClass<EAuracronTramplingEffect> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronTramplingEffect>();
// ********** End Enum EAuracronTramplingEffect ****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
