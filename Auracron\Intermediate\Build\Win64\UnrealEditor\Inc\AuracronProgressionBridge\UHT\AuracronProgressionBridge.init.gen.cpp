// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronProgressionBridge_init() {}
	AURACRONPROGRESSIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature();
	AURACRONPROGRESSIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature();
	AURACRONPROGRESSIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature();
	AURACRONPROGRESSIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature();
	AURACRONPROGRESSIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronProgressionBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronProgressionBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronProgressionBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnAccountLevelUp__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnChampionMasteryLevelUp__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnMilestoneCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnProgressionSynced__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronProgressionBridge_OnRewardGranted__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronProgressionBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x7B1A0EBB,
				0x753C4A72,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronProgressionBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronProgressionBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronProgressionBridge(Z_Construct_UPackage__Script_AuracronProgressionBridge, TEXT("/Script/AuracronProgressionBridge"), Z_Registration_Info_UPackage__Script_AuracronProgressionBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x7B1A0EBB, 0x753C4A72));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
