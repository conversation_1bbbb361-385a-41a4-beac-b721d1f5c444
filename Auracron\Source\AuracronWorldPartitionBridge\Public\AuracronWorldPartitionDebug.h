// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Debugging Tools Header
// Bridge 3.12: World Partition - Debugging Tools

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"

// Debug visualization includes for UE5.6
#include "DrawDebugHelpers.h"
#include "Engine/Canvas.h"
#include "Engine/Engine.h"
#include "Debug/DebugDrawService.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "Stats/Stats.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "GameFramework/Actor.h"
#include "GameFramework/HUD.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"
#include "Math/Box.h"

#include "AuracronWorldPartitionDebug.generated.h"

// Forward declarations
class UAuracronWorldPartitionDebugManager;

// =============================================================================
// DEBUG TYPES AND ENUMS
// =============================================================================

UENUM(BlueprintType)
enum class EAuracronCellStreamingState : uint8
{
    Unloaded            UMETA(DisplayName = "Unloaded"),
    Loading             UMETA(DisplayName = "Loading"),
    Loaded              UMETA(DisplayName = "Loaded"),
    Unloading           UMETA(DisplayName = "Unloading"),
    Error               UMETA(DisplayName = "Error")
};

// Debug visualization modes
UENUM(BlueprintType)
enum class EAuracronDebugVisualizationMode : uint8
{
    None                    UMETA(DisplayName = "None"),
    CellBounds              UMETA(DisplayName = "Cell Bounds"),
    StreamingStates         UMETA(DisplayName = "Streaming States"),
    LoadingProgress         UMETA(DisplayName = "Loading Progress"),
    PerformanceMetrics      UMETA(DisplayName = "Performance Metrics"),
    ActorDistribution       UMETA(DisplayName = "Actor Distribution"),
    MemoryUsage             UMETA(DisplayName = "Memory Usage"),
    All                     UMETA(DisplayName = "All")
};

// Debug info levels
UENUM(BlueprintType)
enum class EAuracronDebugInfoLevel : uint8
{
    Basic                   UMETA(DisplayName = "Basic"),
    Detailed                UMETA(DisplayName = "Detailed"),
    Verbose                 UMETA(DisplayName = "Verbose"),
    Expert                  UMETA(DisplayName = "Expert")
};

// Debug color schemes
UENUM(BlueprintType)
enum class EAuracronDebugColorScheme : uint8
{
    Default                 UMETA(DisplayName = "Default"),
    HighContrast            UMETA(DisplayName = "High Contrast"),
    ColorBlind              UMETA(DisplayName = "Color Blind Friendly"),
    Monochrome              UMETA(DisplayName = "Monochrome")
};

// =============================================================================
// DEBUG CONFIGURATION
// =============================================================================

/**
 * Debug Configuration
 * Configuration settings for World Partition debugging tools
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronDebugConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableDebugVisualization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableStreamingDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnablePerformanceProfiler = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableWorldPartitionInspector = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    EAuracronDebugVisualizationMode VisualizationMode = EAuracronDebugVisualizationMode::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    EAuracronDebugInfoLevel InfoLevel = EAuracronDebugInfoLevel::Basic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    EAuracronDebugColorScheme ColorScheme = EAuracronDebugColorScheme::Default;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    float DebugDrawDistance = 50000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    float DebugTextScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bShowCellBounds = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bShowStreamingStates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bShowLoadingProgress = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bShowPerformanceMetrics = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bShowActorCounts = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bShowMemoryUsage = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float ProfilerUpdateInterval = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxProfilerSamples = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableDetailedProfiling = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inspector")
    bool bShowInspectorWindow = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inspector")
    bool bAutoRefreshInspector = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inspector")
    float InspectorRefreshRate = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    bool bEnableDebugLogging = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    bool bLogStreamingEvents = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    bool bLogPerformanceWarnings = true;

    FAuracronDebugConfiguration()
    {
        bEnableDebugVisualization = false;
        bEnableStreamingDebug = false;
        bEnablePerformanceProfiler = false;
        bEnableWorldPartitionInspector = false;
        VisualizationMode = EAuracronDebugVisualizationMode::None;
        InfoLevel = EAuracronDebugInfoLevel::Basic;
        ColorScheme = EAuracronDebugColorScheme::Default;
        DebugDrawDistance = 50000.0f;
        DebugTextScale = 1.0f;
        bShowCellBounds = true;
        bShowStreamingStates = true;
        bShowLoadingProgress = false;
        bShowPerformanceMetrics = false;
        bShowActorCounts = true;
        bShowMemoryUsage = false;
        ProfilerUpdateInterval = 1.0f;
        MaxProfilerSamples = 100;
        bEnableDetailedProfiling = false;
        bShowInspectorWindow = false;
        bAutoRefreshInspector = true;
        InspectorRefreshRate = 2.0f;
        bEnableDebugLogging = false;
        bLogStreamingEvents = false;
        bLogPerformanceWarnings = true;
    }
};

// =============================================================================
// DEBUG CELL INFO
// =============================================================================

/**
 * Debug Cell Info
 * Debug information for a single world partition cell
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronDebugCellInfo
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Cell Info")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Cell Info")
    FBox CellBounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Cell Info")
    EAuracronCellStreamingState StreamingState = EAuracronCellStreamingState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Cell Info")
    float LoadingProgress = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Cell Info")
    int32 ActorCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Cell Info")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Cell Info")
    float DistanceToViewer = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Cell Info")
    FDateTime LastAccessTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Cell Info")
    TArray<FString> ContainedActors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Cell Info")
    TMap<FString, float> PerformanceMetrics;

    FAuracronDebugCellInfo()
    {
        StreamingState = EAuracronCellStreamingState::Unloaded;
        LoadingProgress = 0.0f;
        ActorCount = 0;
        MemoryUsageMB = 0.0f;
        DistanceToViewer = 0.0f;
        LastAccessTime = FDateTime::Now();
    }
};

// =============================================================================
// DEBUG PERFORMANCE DATA
// =============================================================================

/**
 * Debug Performance Data
 * Performance profiling data for World Partition
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronDebugPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Performance")
    float FrameTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Performance")
    float StreamingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Performance")
    float LoadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Performance")
    float UnloadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Performance")
    int32 LoadedCells = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Performance")
    int32 StreamingCells = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Performance")
    float TotalMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Performance")
    float StreamingBandwidthMBps = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Performance")
    int32 TotalActors = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Performance")
    FDateTime Timestamp;

    FAuracronDebugPerformanceData()
    {
        FrameTime = 0.0f;
        StreamingTime = 0.0f;
        LoadingTime = 0.0f;
        UnloadingTime = 0.0f;
        LoadedCells = 0;
        StreamingCells = 0;
        TotalMemoryUsageMB = 0.0f;
        StreamingBandwidthMBps = 0.0f;
        TotalActors = 0;
        Timestamp = FDateTime::Now();
    }
};

// =============================================================================
// WORLD PARTITION DEBUG MANAGER
// =============================================================================

/**
 * World Partition Debug Manager
 * Central manager for World Partition debugging tools
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronWorldPartitionDebugManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    static UAuracronWorldPartitionDebugManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void Initialize(const FAuracronDebugConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void Tick(float DeltaTime);

    // Debug visualization
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void SetVisualizationMode(EAuracronDebugVisualizationMode Mode);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    EAuracronDebugVisualizationMode GetVisualizationMode() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void DrawDebugVisualization(UWorld* World) const;

    // Cell debugging
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void DrawCellBounds(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void DrawStreamingStates(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void DrawLoadingProgress(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    FAuracronDebugCellInfo GetCellDebugInfo(const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    TArray<FAuracronDebugCellInfo> GetAllCellDebugInfo() const;

    // Streaming debugging
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void EnableStreamingDebug(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    bool IsStreamingDebugEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void LogStreamingEvent(const FString& EventType, const FString& CellId, const FString& Details);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    TArray<FString> GetStreamingEventLog() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void ClearStreamingEventLog();

    // Performance profiling
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void EnablePerformanceProfiler(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    bool IsPerformanceProfilerEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void StartProfiling();

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void StopProfiling();

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    FAuracronDebugPerformanceData GetCurrentPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    TArray<FAuracronDebugPerformanceData> GetPerformanceHistory(int32 MaxSamples = 100) const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void DrawPerformanceMetrics(UWorld* World) const;

    // World Partition Inspector
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void EnableWorldPartitionInspector(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    bool IsWorldPartitionInspectorEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void RefreshInspectorData();

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    TMap<FString, FString> GetInspectorData() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void DrawInspectorWindow(UWorld* World) const;

    // Debug commands
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void ExecuteDebugCommand(const FString& Command);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    TArray<FString> GetAvailableDebugCommands() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    FString GetDebugCommandHelp(const FString& Command) const;

    // Debug utilities
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void TakeDebugSnapshot();

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    bool SaveDebugSnapshot(const FString& FilePath) const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    bool LoadDebugSnapshot(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void ExportDebugData(const FString& FilePath) const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void SetConfiguration(const FAuracronDebugConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    FAuracronDebugConfiguration GetConfiguration() const;

    // Debug info levels
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void SetDebugInfoLevel(EAuracronDebugInfoLevel Level);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    EAuracronDebugInfoLevel GetDebugInfoLevel() const;

    // Color schemes
    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    void SetColorScheme(EAuracronDebugColorScheme Scheme);

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    EAuracronDebugColorScheme GetColorScheme() const;

    UFUNCTION(BlueprintCallable, Category = "Debug Manager")
    FColor GetDebugColor(const FString& ColorName) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDebugEvent, FString, EventType, FString, Details);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPerformanceAlert, FAuracronDebugPerformanceData, PerformanceData);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnStreamingEvent, FString, CellId, FString, EventDetails);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDebugEvent OnDebugEvent;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPerformanceAlert OnPerformanceAlert;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnStreamingEvent OnStreamingEvent;

private:
    static UAuracronWorldPartitionDebugManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronDebugConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Debug data
    TArray<FAuracronDebugCellInfo> CellDebugInfo;
    TArray<FAuracronDebugPerformanceData> PerformanceHistory;
    TArray<FString> StreamingEventLog;
    TMap<FString, FString> InspectorData;

    // Profiling state
    bool bIsProfiling = false;
    FDateTime ProfilingStartTime;
    float LastProfilerUpdate = 0.0f;
    float LastInspectorRefresh = 0.0f;

    // Thread safety
    mutable FCriticalSection DebugLock;

    // Internal functions
    void UpdateDebugData(float DeltaTime);
    void CollectCellDebugInfo();
    void CollectPerformanceData();
    void UpdateInspectorData();
    void ValidateConfiguration();
    FColor GetColorForStreamingState(EAuracronCellStreamingState State) const;
    FColor GetColorForLoadingProgress(float Progress) const;
    void DrawDebugText(UWorld* World, const FVector& Location, const FString& Text, const FColor& Color, float Scale = 1.0f) const;
    void ProcessDebugCommand(const FString& Command);
    void TrimPerformanceHistory();
    void TrimStreamingEventLog();
};
