// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronProgressionBridge.h"

#ifdef AURACRONPROGRESSIONBRIDGE_AuracronProgressionBridge_generated_h
#error "AuracronProgressionBridge.generated.h already included, missing '#pragma once' in AuracronProgressionBridge.h"
#endif
#define AURACRONPROGRESSIONBRIDGE_AuracronProgressionBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FAuracronAccountProgression;
struct FAuracronChampionMastery;
struct FAuracronProgressionMilestone;
struct FAuracronRealmMastery;
struct FAuracronReward;

// ********** Begin ScriptStruct FAuracronReward ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_104_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronReward_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronReward;
// ********** End ScriptStruct FAuracronReward *****************************************************

// ********** Begin ScriptStruct FAuracronAccountProgression ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_169_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAccountProgression_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAccountProgression;
// ********** End ScriptStruct FAuracronAccountProgression *****************************************

// ********** Begin ScriptStruct FAuracronChampionMastery ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_262_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChampionMastery_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChampionMastery;
// ********** End ScriptStruct FAuracronChampionMastery ********************************************

// ********** Begin ScriptStruct FAuracronRealmMastery *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_331_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRealmMastery_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRealmMastery;
// ********** End ScriptStruct FAuracronRealmMastery ***********************************************

// ********** Begin ScriptStruct FAuracronProgressionMilestone *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_388_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronProgressionMilestone_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronProgressionMilestone;
// ********** End ScriptStruct FAuracronProgressionMilestone ***************************************

// ********** Begin ScriptStruct FAuracronChampionMasteryEntry *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_457_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChampionMasteryEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChampionMasteryEntry;
// ********** End ScriptStruct FAuracronChampionMasteryEntry ***************************************

// ********** Begin ScriptStruct FAuracronRealmMasteryEntry ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_482_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRealmMasteryEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRealmMasteryEntry;
// ********** End ScriptStruct FAuracronRealmMasteryEntry ******************************************

// ********** Begin Delegate FOnAccountLevelUp *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_802_DELEGATE \
static void FOnAccountLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnAccountLevelUp, int32 OldLevel, int32 NewLevel);


// ********** End Delegate FOnAccountLevelUp *******************************************************

// ********** Begin Delegate FOnChampionMasteryLevelUp *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_807_DELEGATE \
static void FOnChampionMasteryLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnChampionMasteryLevelUp, const FString& ChampionID, int32 OldLevel, int32 NewLevel);


// ********** End Delegate FOnChampionMasteryLevelUp ***********************************************

// ********** Begin Delegate FOnMilestoneCompleted *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_812_DELEGATE \
static void FOnMilestoneCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnMilestoneCompleted, const FString& MilestoneID, const TArray<FAuracronReward>& Rewards);


// ********** End Delegate FOnMilestoneCompleted ***************************************************

// ********** Begin Delegate FOnRewardGranted ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_817_DELEGATE \
static void FOnRewardGranted_DelegateWrapper(const FMulticastScriptDelegate& OnRewardGranted, FAuracronReward Reward);


// ********** End Delegate FOnRewardGranted ********************************************************

// ********** Begin Delegate FOnProgressionSynced **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_822_DELEGATE \
static void FOnProgressionSynced_DelegateWrapper(const FMulticastScriptDelegate& OnProgressionSynced, bool bSuccess);


// ********** End Delegate FOnProgressionSynced ****************************************************

// ********** Begin Class UAuracronProgressionBridge ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_508_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_PendingRewards); \
	DECLARE_FUNCTION(execBackupProgression); \
	DECLARE_FUNCTION(execSyncWithFirebase); \
	DECLARE_FUNCTION(execLoadProgressionFromCloud); \
	DECLARE_FUNCTION(execSaveProgressionToCloud); \
	DECLARE_FUNCTION(execClaimAllRewards); \
	DECLARE_FUNCTION(execClaimReward); \
	DECLARE_FUNCTION(execGetPendingRewards); \
	DECLARE_FUNCTION(execGrantReward); \
	DECLARE_FUNCTION(execCheckMilestoneProgress); \
	DECLARE_FUNCTION(execGetCompletedMilestones); \
	DECLARE_FUNCTION(execGetAvailableMilestones); \
	DECLARE_FUNCTION(execCompleteMilestone); \
	DECLARE_FUNCTION(execDiscoverRealmSecret); \
	DECLARE_FUNCTION(execRegisterTimeInRealm); \
	DECLARE_FUNCTION(execGetRealmMastery); \
	DECLARE_FUNCTION(execGainRealmMasteryPoints); \
	DECLARE_FUNCTION(execLevelUpChampionMastery); \
	DECLARE_FUNCTION(execGetAllChampionMasteries); \
	DECLARE_FUNCTION(execGetChampionMastery); \
	DECLARE_FUNCTION(execGainChampionMasteryPoints); \
	DECLARE_FUNCTION(execCalculateExperienceForLevel); \
	DECLARE_FUNCTION(execGetAccountProgression); \
	DECLARE_FUNCTION(execAddPremiumCurrency); \
	DECLARE_FUNCTION(execRemoveGold); \
	DECLARE_FUNCTION(execAddGold); \
	DECLARE_FUNCTION(execLevelUpAccount); \
	DECLARE_FUNCTION(execGainAccountExperience);


AURACRONPROGRESSIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronProgressionBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_508_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronProgressionBridge(); \
	friend struct Z_Construct_UClass_UAuracronProgressionBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPROGRESSIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronProgressionBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronProgressionBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronProgressionBridge"), Z_Construct_UClass_UAuracronProgressionBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronProgressionBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		CurrentAccountProgression=NETFIELD_REP_START, \
		ChampionMasteries, \
		RealmMasteries, \
		PendingRewards, \
		NETFIELD_REP_END=PendingRewards	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_508_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronProgressionBridge(UAuracronProgressionBridge&&) = delete; \
	UAuracronProgressionBridge(const UAuracronProgressionBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronProgressionBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronProgressionBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronProgressionBridge) \
	NO_API virtual ~UAuracronProgressionBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_505_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_508_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_508_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_508_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h_508_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronProgressionBridge;

// ********** End Class UAuracronProgressionBridge *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronProgressionBridge_Public_AuracronProgressionBridge_h

// ********** Begin Enum EAuracronProgressionType **************************************************
#define FOREACH_ENUM_EAURACRONPROGRESSIONTYPE(op) \
	op(EAuracronProgressionType::None) \
	op(EAuracronProgressionType::AccountLevel) \
	op(EAuracronProgressionType::ChampionMastery) \
	op(EAuracronProgressionType::RealmMastery) \
	op(EAuracronProgressionType::SeasonalRank) \
	op(EAuracronProgressionType::BattlePass) \
	op(EAuracronProgressionType::Achievement) \
	op(EAuracronProgressionType::Collection) \
	op(EAuracronProgressionType::Milestone) 

enum class EAuracronProgressionType : uint8;
template<> struct TIsUEnumClass<EAuracronProgressionType> { enum { Value = true }; };
template<> AURACRONPROGRESSIONBRIDGE_API UEnum* StaticEnum<EAuracronProgressionType>();
// ********** End Enum EAuracronProgressionType ****************************************************

// ********** Begin Enum EAuracronRewardType *******************************************************
#define FOREACH_ENUM_EAURACRONREWARDTYPE(op) \
	op(EAuracronRewardType::None) \
	op(EAuracronRewardType::Experience) \
	op(EAuracronRewardType::Gold) \
	op(EAuracronRewardType::PremiumCurrency) \
	op(EAuracronRewardType::Champion) \
	op(EAuracronRewardType::Skin) \
	op(EAuracronRewardType::Emote) \
	op(EAuracronRewardType::Icon) \
	op(EAuracronRewardType::Border) \
	op(EAuracronRewardType::Title) \
	op(EAuracronRewardType::Ward) \
	op(EAuracronRewardType::Recall) \
	op(EAuracronRewardType::Sigilo) \
	op(EAuracronRewardType::Boost) \
	op(EAuracronRewardType::Chest) \
	op(EAuracronRewardType::Key) \
	op(EAuracronRewardType::Essence) 

enum class EAuracronRewardType : uint8;
template<> struct TIsUEnumClass<EAuracronRewardType> { enum { Value = true }; };
template<> AURACRONPROGRESSIONBRIDGE_API UEnum* StaticEnum<EAuracronRewardType>();
// ********** End Enum EAuracronRewardType *********************************************************

// ********** Begin Enum EAuracronRewardRarity *****************************************************
#define FOREACH_ENUM_EAURACRONREWARDRARITY(op) \
	op(EAuracronRewardRarity::Common) \
	op(EAuracronRewardRarity::Uncommon) \
	op(EAuracronRewardRarity::Rare) \
	op(EAuracronRewardRarity::Epic) \
	op(EAuracronRewardRarity::Legendary) \
	op(EAuracronRewardRarity::Mythic) \
	op(EAuracronRewardRarity::Ultimate) 

enum class EAuracronRewardRarity : uint8;
template<> struct TIsUEnumClass<EAuracronRewardRarity> { enum { Value = true }; };
template<> AURACRONPROGRESSIONBRIDGE_API UEnum* StaticEnum<EAuracronRewardRarity>();
// ********** End Enum EAuracronRewardRarity *******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
