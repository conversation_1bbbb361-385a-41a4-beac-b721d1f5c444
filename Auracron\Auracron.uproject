﻿{
    "FileVersion":  3,
    "EngineAssociation":  "5.6",
    "Category":  "Games",
    "Description":  "AURACRON - Revolutionary MOBA 5v5 with dynamic multidimensional maps and procedural content generation",
    "Modules":  [
                    {
                        "Name":  "Auracron",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronPCGBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronMetaHumanBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronWorldPartitionBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronFoliageBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronLumenBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronAbismoUmbrioBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronAdaptiveCreaturesBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronVerticalTransitionsBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronQABridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronMetaHumanFramework",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronAnalyticsBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronAudioBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronVFXBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronPhysicsBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronNaniteBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronVoiceBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronAntiCheatBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronTutorialBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronLoreBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronMonetizationBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    },
                    {
                        "Name":  "AuracronEOSBridge",
                        "Type":  "Runtime",
                        "LoadingPhase":  "Default"
                    }
                ],
    "Plugins":  [
                    {
                        "Name":  "ModelingToolsEditorMode",
                        "Enabled":  true,
                        "TargetAllowList":  [
                                                "Editor"
                                            ]
                    },
                    {
                        "Name":  "PCG",
                        "Enabled":  true
                    },
                    {
                        "Name":  "PCGGeometryScriptInterop",
                        "Enabled":  true
                    },
                    {
                        "Name":  "MetaHuman",
                        "Enabled":  true
                    },
                    {
                        "Name":  "PythonScriptPlugin",
                        "Enabled":  true
                    },
                    {
                        "Name":  "EditorScriptingUtilities",
                        "Enabled":  true
                    },
                    {
                        "Name":  "SequencerScripting",
                        "Enabled":  true
                    },
                    {
                        "Name":  "OnlineSubsystem",
                        "Enabled":  true
                    },
                    {
                        "Name":  "OnlineSubsystemUtils",
                        "Enabled":  true
                    },
                    {
                        "Name":  "GameplayAbilities",
                        "Enabled":  true
                    },
                    {
                        "Name":  "EnhancedInput",
                        "Enabled":  true
                    },
                    {
                        "Name":  "CommonUI",
                        "Enabled":  true
                    },
                    {
                        "Name":  "Niagara",
                        "Enabled":  true
                    },
                    {
                        "Name":  "WaveTable",
                        "Enabled":  true
                    },
                    {
                        "Name":  "Synthesis",
                        "Enabled":  true
                    },
                    {
                        "Name":  "OnlineSubsystemEOS",
                        "Enabled":  true
                    },
                    {
                        "Name":  "EOSShared",
                        "Enabled":  true
                    },
                    {
                        "Name":  "SocketSubsystemEOS",
                        "Enabled":  true
                    },
                    {
                        "Name":  "GeometryScripting",
                        "Enabled":  true
                    },
                    {
                        "Name":  "GeometryProcessing",
                        "Enabled":  true
                    },
                    {
                        "Name":  "MeshModelingToolset",
                        "Enabled":  true
                    },
                    {
                        "Name":  "MassGameplay",
                        "Enabled":  true
                    },
                    {
                        "Name":  "MassAI",
                        "Enabled":  true
                    },
                    {
                        "Name":  "StateTree",
                        "Enabled":  true
                    },
                    {
                        "Name":  "GameplayStateTree",
                        "Enabled":  true
                    },
                    {
                        "Name":  "StructUtils",
                        "Enabled":  true
                    },
                    {
                        "Name":  "SmartObjects",
                        "Enabled":  true
                    },
                    {
                        "Name":  "ZoneGraph",
                        "Enabled":  true
                    },
                    {
                        "Name":  "ZoneGraphAnnotations",
                        "Enabled":  true
                    },
                    {
                        "Name":  "AnalyticsMulticast",
                        "Enabled":  true
                    },
                    {
                        "Name":  "ModularGameplay",
                        "Enabled":  true
                    },
                    {
                        "Name":  "ReplicationGraph",
                        "Enabled":  true
                    },
                    {
                        "Name":  "NetcodeUnitTest",
                        "Enabled":  true
                    },
                    {
                        "Name":  "RawInput",
                        "Enabled":  true
                    },
                    {
                        "Name":  "GPULightmass",
                        "Enabled":  true
                    },
                    {
                        "Name":  "HairStrands",
                        "Enabled":  true
                    },
                    {
                        "Name":  "LiveLink",
                        "Enabled":  true
                    },
                    {
                        "Name":  "AudioSynesthesia",
                        "Enabled":  true
                    },
                    {
                        "Name":  "RigLogic",
                        "Enabled":  true
                    },
                    {
                        "Name":  "Takes",
                        "Enabled":  true
                    },
                    {
                        "Name":  "RemoteControl",
                        "Enabled":  true
                    },
                    {
                        "Name":  "VirtualCamera",
                        "Enabled":  true
                    },
                    {
                        "Name":  "ProxyLODPlugin",
                        "Enabled":  true
                    },
                    {
                        "Name":  "ChaosVehiclesPlugin",
                        "Enabled":  true
                    },
                    {
                        "Name":  "Water",
                        "Enabled":  true
                    },
                    {
                        "Name":  "SignificanceManager",
                        "Enabled":  true
                    },
                    {
                        "Name":  "StylusInput",
                        "Enabled":  true
                    }
                ],
    "TargetPlatforms":  [
                            "Windows",
                            "Android",
                            "IOS"
                        ]
}
