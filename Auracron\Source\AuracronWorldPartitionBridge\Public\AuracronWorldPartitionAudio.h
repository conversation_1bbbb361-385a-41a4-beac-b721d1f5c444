// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Audio Streaming Header
// Bridge 3.9: World Partition - Audio Streaming

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"

// Audio includes for UE5.6
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Sound/SoundCue.h"
#include "Sound/SoundWave.h"
#include "Sound/AmbientSound.h"
#include "AudioDevice.h"
#include "AudioDeviceManager.h"
#include "MetasoundSource.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "GameFramework/Actor.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"
#include "Math/Box.h"

#include "AuracronWorldPartitionAudio.generated.h"

// Forward declarations
class UAuracronWorldPartitionAudioManager;
class UAudioComponent;
class USoundBase;
class AAmbientSound;

// =============================================================================
// AUDIO TYPES AND ENUMS
// =============================================================================

// Audio streaming states
UENUM(BlueprintType)
enum class EAuracronAudioStreamingState : uint8
{
    Unloaded                UMETA(DisplayName = "Unloaded"),
    Loading                 UMETA(DisplayName = "Loading"),
    Loaded                  UMETA(DisplayName = "Loaded"),
    Playing                 UMETA(DisplayName = "Playing"),
    Paused                  UMETA(DisplayName = "Paused"),
    Unloading               UMETA(DisplayName = "Unloading"),
    Failed                  UMETA(DisplayName = "Failed")
};

// Audio LOD levels
UENUM(BlueprintType)
enum class EAuracronAudioLODLevel : uint8
{
    LOD0                    UMETA(DisplayName = "LOD 0 (Highest)"),
    LOD1                    UMETA(DisplayName = "LOD 1"),
    LOD2                    UMETA(DisplayName = "LOD 2"),
    LOD3                    UMETA(DisplayName = "LOD 3"),
    LOD4                    UMETA(DisplayName = "LOD 4 (Lowest)")
};

// Audio types
UENUM(BlueprintType)
enum class EAuracronWorldPartitionAudioType : uint8
{
    Ambient                 UMETA(DisplayName = "Ambient"),
    Music                   UMETA(DisplayName = "Music"),
    SFX                     UMETA(DisplayName = "Sound Effects"),
    Voice                   UMETA(DisplayName = "Voice"),
    UI                      UMETA(DisplayName = "User Interface"),
    Foley                   UMETA(DisplayName = "Foley")
};

// Audio spatialization types
UENUM(BlueprintType)
enum class EAuracronAudioSpatialization : uint8
{
    None                    UMETA(DisplayName = "None (2D)"),
    Simple                  UMETA(DisplayName = "Simple 3D"),
    Binaural                UMETA(DisplayName = "Binaural"),
    Surround                UMETA(DisplayName = "Surround"),
    Atmos                   UMETA(DisplayName = "Dolby Atmos")
};

// =============================================================================
// AUDIO CONFIGURATION
// =============================================================================

/**
 * Audio Configuration
 * Configuration settings for audio streaming in world partition
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronWorldPartitionAudioConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    bool bEnableAudioStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    bool bEnableAudioLOD = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    bool bEnable3DAudio = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    bool bEnableMetaSounds = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float AudioStreamingDistance = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float AudioUnloadingDistance = 15000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    int32 MaxConcurrentAudioSources = 32;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float BaseLODDistance = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float LODDistanceMultiplier = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 MaxLODLevel = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    float MasterVolume = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    float AmbientVolume = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    float MusicVolume = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    float SFXVolume = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxAudioMemoryUsageMB = 256.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAudioCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAudioCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatialization")
    EAuracronAudioSpatialization DefaultSpatialization = EAuracronAudioSpatialization::Simple;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatialization")
    float MaxAudibleDistance = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatialization")
    float FalloffDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableAudioDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogAudioOperations = false;

    FAuracronAudioConfiguration()
    {
        bEnableAudioStreaming = true;
        bEnableAudioLOD = true;
        bEnable3DAudio = true;
        bEnableMetaSounds = true;
        AudioStreamingDistance = 10000.0f;
        AudioUnloadingDistance = 15000.0f;
        MaxConcurrentAudioSources = 32;
        BaseLODDistance = 500.0f;
        LODDistanceMultiplier = 2.0f;
        MaxLODLevel = 4;
        MasterVolume = 1.0f;
        AmbientVolume = 0.8f;
        MusicVolume = 0.7f;
        SFXVolume = 1.0f;
        MaxAudioMemoryUsageMB = 256.0f;
        bEnableAudioCaching = true;
        bEnableAudioCulling = true;
        DefaultSpatialization = EAuracronAudioSpatialization::Simple;
        MaxAudibleDistance = 5000.0f;
        FalloffDistance = 1000.0f;
        bEnableAudioDebug = false;
        bLogAudioOperations = false;
    }
};

// =============================================================================
// AUDIO DESCRIPTOR
// =============================================================================

/**
 * Audio Descriptor
 * Descriptor for audio sources and their properties
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronAudioDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    FString AudioId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    FString AudioName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    FString SoundAssetPath;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    EAuracronWorldPartitionAudioType AudioType = EAuracronWorldPartitionAudioType::Ambient;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    EAuracronAudioSpatialization Spatialization = EAuracronAudioSpatialization::Simple;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    EAuracronAudioStreamingState StreamingState = EAuracronAudioStreamingState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    EAuracronAudioLODLevel CurrentLODLevel = EAuracronAudioLODLevel::LOD0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    FVector Location = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    float Volume = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    float Pitch = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    float MaxAudibleDistance = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    float FalloffDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    bool bIsLooping = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    bool bAutoPlay = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    bool bIs3D = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    bool bIsEnabled = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Descriptor")
    FDateTime LastAccessTime;

    FAuracronAudioDescriptor()
    {
        AudioType = EAuracronWorldPartitionAudioType::Ambient;
        Spatialization = EAuracronAudioSpatialization::Simple;
        StreamingState = EAuracronAudioStreamingState::Unloaded;
        CurrentLODLevel = EAuracronAudioLODLevel::LOD0;
        Location = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        Volume = 1.0f;
        Pitch = 1.0f;
        MaxAudibleDistance = 5000.0f;
        FalloffDistance = 1000.0f;
        bIsLooping = false;
        bAutoPlay = true;
        bIs3D = true;
        MemoryUsageMB = 0.0f;
        bIsEnabled = true;
        CreationTime = FDateTime::Now();
        LastAccessTime = CreationTime;
    }
};

// =============================================================================
// AUDIO STATISTICS
// =============================================================================

/**
 * Audio Statistics
 * Performance and usage statistics for audio system
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronAudioStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalAudioSources = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LoadedAudioSources = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 PlayingAudioSources = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 StreamingAudioSources = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float TotalMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageLoadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LODTransitions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 FailedOperations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AudioEfficiency = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronAudioStatistics()
    {
        TotalAudioSources = 0;
        LoadedAudioSources = 0;
        PlayingAudioSources = 0;
        StreamingAudioSources = 0;
        TotalMemoryUsageMB = 0.0f;
        AverageLoadingTime = 0.0f;
        LODTransitions = 0;
        FailedOperations = 0;
        AudioEfficiency = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// WORLD PARTITION AUDIO MANAGER
// =============================================================================

/**
 * World Partition Audio Manager
 * Central manager for audio streaming in world partition
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronWorldPartitionAudioManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    static UAuracronWorldPartitionAudioManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    void Initialize(const FAuracronAudioConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    void Tick(float DeltaTime);

    // Audio source creation and management
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    FString CreateAudioSource(const FString& SoundAssetPath, const FVector& Location, EAuracronWorldPartitionAudioType AudioType = EAuracronWorldPartitionAudioType::Ambient);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool RemoveAudioSource(const FString& AudioId);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    FAuracronAudioDescriptor GetAudioDescriptor(const FString& AudioId) const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    TArray<FAuracronAudioDescriptor> GetAllAudioSources() const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    TArray<FString> GetAudioIds() const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool DoesAudioSourceExist(const FString& AudioId) const;

    // Audio streaming
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool LoadAudioSource(const FString& AudioId);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool UnloadAudioSource(const FString& AudioId);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    EAuracronAudioStreamingState GetAudioStreamingState(const FString& AudioId) const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    TArray<FString> GetLoadedAudioSources() const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    TArray<FString> GetStreamingAudioSources() const;

    // Audio playback
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool PlayAudioSource(const FString& AudioId);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool StopAudioSource(const FString& AudioId);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool PauseAudioSource(const FString& AudioId);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool ResumeAudioSource(const FString& AudioId);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    TArray<FString> GetPlayingAudioSources() const;

    // Audio properties
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool SetAudioVolume(const FString& AudioId, float Volume);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool SetAudioPitch(const FString& AudioId, float Pitch);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool SetAudioLocation(const FString& AudioId, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool SetAudioRotation(const FString& AudioId, const FRotator& Rotation);

    // Audio LOD
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool SetAudioLOD(const FString& AudioId, EAuracronAudioLODLevel LODLevel);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    EAuracronAudioLODLevel GetAudioLOD(const FString& AudioId) const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    void UpdateDistanceBasedLODs(const FVector& ListenerLocation);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    EAuracronAudioLODLevel CalculateLODForDistance(float Distance) const;

    // Audio queries
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    TArray<FAuracronAudioDescriptor> GetAudioSourcesInRadius(const FVector& Location, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    TArray<FAuracronAudioDescriptor> GetAudioSourcesByType(EAuracronWorldPartitionAudioType AudioType) const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    TArray<FAuracronAudioDescriptor> GetAudibleAudioSources(const FVector& ListenerLocation) const;

    // Cell integration
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    TArray<FString> GetAudioSourcesInCell(const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    FString GetAudioSourceCell(const FString& AudioId) const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool MoveAudioSourceToCell(const FString& AudioId, const FString& CellId);

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    void SetConfiguration(const FAuracronAudioConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    FAuracronWorldPartitionAudioConfiguration GetConfiguration() const;

    // Statistics and monitoring
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    FAuracronAudioStatistics GetAudioStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    int32 GetTotalAudioSourceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    int32 GetLoadedAudioSourceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    int32 GetPlayingAudioSourceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    float GetTotalMemoryUsage() const;

    // Debug and utilities
    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    void EnableAudioDebug(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    bool IsAudioDebugEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    void LogAudioState() const;

    UFUNCTION(BlueprintCallable, Category = "Audio Manager")
    void DrawDebugAudioInfo(UWorld* World) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAudioLoaded, FString, AudioId, bool, bSuccess);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAudioUnloaded, FString, AudioId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAudioStarted, FString, AudioId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAudioStopped, FString, AudioId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAudioLODChanged, FString, AudioId, EAuracronAudioLODLevel, NewLOD);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnAudioLoaded OnAudioLoaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnAudioUnloaded OnAudioUnloaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnAudioStarted OnAudioStarted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnAudioStopped OnAudioStopped;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnAudioLODChanged OnAudioLODChanged;

private:
    static UAuracronWorldPartitionAudioManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronWorldPartitionAudioConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Audio data
    TMap<FString, FAuracronAudioDescriptor> AudioDescriptors;
    TMap<FString, TWeakObjectPtr<UAudioComponent>> AudioComponents;
    TMap<FString, FString> AudioToCellMap; // AudioId -> CellId
    TMap<FString, TSet<FString>> CellToAudioMap; // CellId -> AudioIds

    // Statistics
    FAuracronAudioStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection AudioLock;

    // Internal functions
    void UpdateStatistics();
    FString GenerateAudioId(const FString& SoundAssetPath) const;
    bool ValidateAudioId(const FString& AudioId) const;
    void OnAudioLoadedInternal(const FString& AudioId, bool bSuccess);
    void OnAudioUnloadedInternal(const FString& AudioId);
    void ValidateConfiguration();
    void UpdateAudioCellMapping(const FString& AudioId);
    bool CreateAudioComponent(const FString& AudioId, const FString& SoundAssetPath);
    bool IsAudioAudible(const FAuracronAudioDescriptor& Audio, const FVector& ListenerLocation) const;
};
