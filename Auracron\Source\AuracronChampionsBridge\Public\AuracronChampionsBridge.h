// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de CampeÃµes Bridge
// IntegraÃ§Ã£o C++ para sistema de campeÃµes com MetaHuman e APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "ModularGameplay/Public/GameFrameworkComponent.h"
#include "GameplayAbilities/Public/AbilitySystemComponent.h"
#include "GameplayAbilities/Public/GameplayAbility.h"
#include "GameplayAbilities/Public/GameplayEffect.h"
#include "GameplayAbilities/Public/AttributeSet.h"
#include "GameplayTags/Classes/GameplayTagContainer.h"
#include "Animation/AnimInstance.h"
#include "Animation/AnimMontage.h"
#include "Animation/AnimBlueprint.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/SkeletalMesh.h"
#include "Materials/MaterialInterface.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "Components/TimelineComponent.h"
#include "Curves/CurveFloat.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "Sound/SoundCue.h"
#include "MetasoundSource.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputMappingContext.h"
#include "InputAction.h"
#include "ControlRig.h"
#include "Rigs/RigHierarchy.h"
#include "Units/RigUnit.h"
#include "AuracronChampionsBridge.generated.h"

// Forward Declarations
class UAbilitySystemComponent;
class UGameplayAbility;
class UGameplayEffect;
class UAttributeSet;
class UAnimInstance;
class UAnimMontage;
class USkeletalMesh;
class UMaterialInterface;
class UNiagaraSystem;
class UNiagaraComponent;
class UEnhancedInputComponent;
class UInputMappingContext;
class UInputAction;

/**
 * EnumeraÃ§Ã£o para tipos de CampeÃµes
 */
UENUM(BlueprintType)
enum class EAuracronChampionType : uint8
{
    None        UMETA(DisplayName = "None"),
    Tank        UMETA(DisplayName = "Tank"),
    Damage      UMETA(DisplayName = "Damage"),
    Support     UMETA(DisplayName = "Support"),
    Assassin    UMETA(DisplayName = "Assassin"),
    Mage        UMETA(DisplayName = "Mage"),
    Marksman    UMETA(DisplayName = "Marksman")
};

/**
 * EnumeraÃ§Ã£o para raridade de CampeÃµes
 */
UENUM(BlueprintType)
enum class EAuracronChampionRarity : uint8
{
    Common      UMETA(DisplayName = "Common"),
    Uncommon    UMETA(DisplayName = "Uncommon"),
    Rare        UMETA(DisplayName = "Rare"),
    Epic        UMETA(DisplayName = "Epic"),
    Legendary   UMETA(DisplayName = "Legendary"),
    Mythic      UMETA(DisplayName = "Mythic")
};

/**
 * EnumeraÃ§Ã£o para estados do CampeÃ£o
 */
UENUM(BlueprintType)
enum class EAuracronChampionState : uint8
{
    Idle            UMETA(DisplayName = "Idle"),
    Moving          UMETA(DisplayName = "Moving"),
    Attacking       UMETA(DisplayName = "Attacking"),
    CastingAbility  UMETA(DisplayName = "Casting Ability"),
    Stunned         UMETA(DisplayName = "Stunned"),
    Dead            UMETA(DisplayName = "Dead"),
    Respawning      UMETA(DisplayName = "Respawning"),
    Channeling      UMETA(DisplayName = "Channeling"),
    Invisible       UMETA(DisplayName = "Invisible"),
    Invulnerable    UMETA(DisplayName = "Invulnerable")
};

/**
 * Estrutura para atributos base de um CampeÃ£o
 */
USTRUCT(BlueprintType)
struct AURACRONCHAMPIONSBRIDGE_API FAuracronChampionBaseAttributes
{
    GENERATED_BODY()

    /** Pontos de Vida mÃ¡ximos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "100.0", ClampMax = "5000.0"))
    float MaxHealth = 1000.0f;

    /** Pontos de Vida atuais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "5000.0"))
    float CurrentHealth = 1000.0f;

    /** Mana mÃ¡xima */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "50.0", ClampMax = "2000.0"))
    float MaxMana = 500.0f;

    /** Mana atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "2000.0"))
    float CurrentMana = 500.0f;

    /** Dano de Ataque */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "10.0", ClampMax = "500.0"))
    float AttackDamage = 80.0f;

    /** Poder de Habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "10.0", ClampMax = "500.0"))
    float AbilityPower = 60.0f;

    /** Armadura */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "300.0"))
    float Armor = 30.0f;

    /** ResistÃªncia MÃ¡gica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "300.0"))
    float MagicResistance = 25.0f;

    /** Velocidade de Movimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "100.0", ClampMax = "1000.0"))
    float MovementSpeed = 350.0f;

    /** Velocidade de Ataque */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.5", ClampMax = "3.0"))
    float AttackSpeed = 1.0f;

    /** Alcance de Ataque */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "100.0", ClampMax = "800.0"))
    float AttackRange = 150.0f;

    /** Chance de CrÃ­tico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float CriticalChance = 0.05f;

    /** Dano CrÃ­tico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "1.0", ClampMax = "5.0"))
    float CriticalDamage = 2.0f;

    /** RegeneraÃ§Ã£o de HP por segundo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "50.0"))
    float HealthRegeneration = 5.0f;

    /** RegeneraÃ§Ã£o de Mana por segundo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "30.0"))
    float ManaRegeneration = 8.0f;

    /** ReduÃ§Ã£o de Cooldown */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "0.8"))
    float CooldownReduction = 0.0f;

    /** PenetraÃ§Ã£o de Armadura */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "100.0"))
    float ArmorPenetration = 0.0f;

    /** PenetraÃ§Ã£o MÃ¡gica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "100.0"))
    float MagicPenetration = 0.0f;

    /** Roubo de Vida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float LifeSteal = 0.0f;

    /** Roubo de Mana */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float ManaSteal = 0.0f;

    /** Tenacidade (resistÃªncia a CC) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Attributes", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Tenacity = 0.0f;
};

/**
 * Estrutura para habilidades do CampeÃ£o
 */
USTRUCT(BlueprintType)
struct AURACRONCHAMPIONSBRIDGE_API FAuracronChampionAbilities
{
    GENERATED_BODY()

    /** Habilidade Passiva */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Abilities")
    TSoftClassPtr<UGameplayAbility> PassiveAbility;

    /** Habilidade Q */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Abilities")
    TSoftClassPtr<UGameplayAbility> QAbility;

    /** Habilidade W */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Abilities")
    TSoftClassPtr<UGameplayAbility> WAbility;

    /** Habilidade E */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Abilities")
    TSoftClassPtr<UGameplayAbility> EAbility;

    /** Habilidade R (Ultimate) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Abilities")
    TSoftClassPtr<UGameplayAbility> RAbility;

    /** Habilidades de Sigilo (quando fusionado) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Abilities")
    TArray<TSoftClassPtr<UGameplayAbility>> SigiloAbilities;

    /** Cooldowns das habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Abilities")
    TMap<FString, float> AbilityCooldowns;

    /** Custos de mana das habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Abilities")
    TMap<FString, float> AbilityManaCosts;

    /** NÃ­veis das habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Abilities")
    TMap<FString, int32> AbilityLevels;

    /** Pontos de habilidade disponÃ­veis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Abilities", meta = (ClampMin = "0", ClampMax = "18"))
    int32 AvailableAbilityPoints = 0;
};

/**
 * Estrutura para configuraÃ§Ã£o visual do CampeÃ£o (MetaHuman)
 */
USTRUCT(BlueprintType)
struct AURACRONCHAMPIONSBRIDGE_API FAuracronChampionVisualConfig
{
    GENERATED_BODY()

    /** Mesh do MetaHuman */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    TSoftObjectPtr<USkeletalMesh> MetaHumanMesh;

    /** Animation Blueprint */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    TSoftClassPtr<UAnimInstance> AnimationBlueprint;

    /** Control Rig para customizaÃ§Ã£o facial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    TSoftObjectPtr<UControlRig> FacialControlRig;

    /** IK Rig para movimento procedural */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    TSoftObjectPtr<UIKRigDefinition> IKRigDefinition;

    /** Materiais do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    TArray<TSoftObjectPtr<UMaterialInterface>> ChampionMaterials;

    /** Texturas de skin alternativas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    TArray<TSoftObjectPtr<UTexture2D>> AlternativeSkins;

    /** Efeitos de partÃ­culas do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    TArray<TSoftObjectPtr<UNiagaraSystem>> ChampionParticleEffects;

    /** Escala do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration", meta = (ClampMin = "0.5", ClampMax = "2.0"))
    float ChampionScale = 1.0f;

    /** Cor primÃ¡ria do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    FLinearColor PrimaryColor = FLinearColor::White;

    /** Cor secundÃ¡ria do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    FLinearColor SecondaryColor = FLinearColor::Gray;

    /** Cor dos olhos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    FLinearColor EyeColor = FLinearColor::Blue;

    /** Cor do cabelo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    FLinearColor HairColor = FLinearColor::Black;

    /** Cor da pele */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    FLinearColor SkinColor = FLinearColor(0.8f, 0.6f, 0.4f, 1.0f);

    /** Usa MetaHuman */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    bool bUseMetaHuman = true;

    /** Permite customizaÃ§Ã£o facial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    bool bAllowFacialCustomization = true;

    /** Suporta skins alternativas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Configuration")
    bool bSupportsAlternativeSkins = true;
};

/**
 * Estrutura para configuraÃ§Ã£o de movimento do CampeÃ£o
 */
USTRUCT(BlueprintType)
struct AURACRONCHAMPIONSBRIDGE_API FAuracronChampionMovementConfig
{
    GENERATED_BODY()

    /** Velocidade mÃ¡xima de caminhada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "100.0", ClampMax = "1000.0"))
    float MaxWalkSpeed = 350.0f;

    /** Velocidade mÃ¡xima de corrida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "200.0", ClampMax = "1500.0"))
    float MaxRunSpeed = 600.0f;

    /** AceleraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "500.0", ClampMax = "5000.0"))
    float Acceleration = 2048.0f;

    /** DesaceleraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "500.0", ClampMax = "5000.0"))
    float Deceleration = 2048.0f;

    /** ForÃ§a de pulo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "200.0", ClampMax = "1000.0"))
    float JumpZVelocity = 420.0f;

    /** Controle aÃ©reo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AirControl = 0.2f;

    /** Multiplicador de gravidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "0.1", ClampMax = "3.0"))
    float GravityScale = 1.0f;

    /** Atrito no solo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "0.0", ClampMax = "20.0"))
    float GroundFriction = 8.0f;

    /** Atrito de frenagem */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "0.0", ClampMax = "20.0"))
    float BrakingFriction = 2.0f;

    /** Pode voar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration")
    bool bCanFly = false;

    /** Pode nadar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration")
    bool bCanSwim = true;

    /** Pode escalar paredes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration")
    bool bCanClimbWalls = false;

    /** Permite dash */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration")
    bool bAllowDash = true;

    /** DistÃ¢ncia do dash */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "100.0", ClampMax = "1000.0"))
    float DashDistance = 400.0f;

    /** Cooldown do dash */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration", meta = (ClampMin = "1.0", ClampMax = "30.0"))
    float DashCooldown = 8.0f;
};

/**
 * Estrutura para configuraÃ§Ã£o de input do CampeÃ£o
 */
USTRUCT(BlueprintType)
struct AURACRONCHAMPIONSBRIDGE_API FAuracronChampionInputConfig
{
    GENERATED_BODY()

    /** Contexto de mapeamento de input */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputMappingContext> InputMappingContext;

    /** AÃ§Ã£o de movimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputAction> MoveAction;

    /** AÃ§Ã£o de olhar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputAction> LookAction;

    /** AÃ§Ã£o de pulo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputAction> JumpAction;

    /** AÃ§Ã£o de ataque bÃ¡sico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputAction> BasicAttackAction;

    /** AÃ§Ã£o de habilidade Q */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputAction> QAbilityAction;

    /** AÃ§Ã£o de habilidade W */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputAction> WAbilityAction;

    /** AÃ§Ã£o de habilidade E */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputAction> EAbilityAction;

    /** AÃ§Ã£o de habilidade R */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputAction> RAbilityAction;

    /** AÃ§Ã£o de dash */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputAction> DashAction;

    /** AÃ§Ã£o de recall */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    TSoftObjectPtr<UInputAction> RecallAction;

    /** Sensibilidade do mouse */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float MouseSensitivity = 1.0f;

    /** Sensibilidade do gamepad */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float GamepadSensitivity = 1.5f;

    /** Inverter eixo Y */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    bool bInvertYAxis = false;

    /** Suporte a touch (mobile) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input Configuration")
    bool bSupportTouch = true;
};

/**
 * Estrutura completa de configuraÃ§Ã£o de um CampeÃ£o
 */
USTRUCT(BlueprintType)
struct AURACRONCHAMPIONSBRIDGE_API FAuracronChampionConfiguration
{
    GENERATED_BODY()

    /** ID Ãºnico do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    FString ChampionID;

    /** Nome do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    FText ChampionName;

    /** DescriÃ§Ã£o do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    FText ChampionDescription;

    /** Tipo do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    EAuracronChampionType ChampionType = EAuracronChampionType::None;

    /** Raridade do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    EAuracronChampionRarity ChampionRarity = EAuracronChampionRarity::Common;

    /** Atributos base */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    FAuracronChampionBaseAttributes BaseAttributes;

    /** Habilidades */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    FAuracronChampionAbilities Abilities;

    /** ConfiguraÃ§Ã£o visual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    FAuracronChampionVisualConfig VisualConfig;

    /** ConfiguraÃ§Ã£o de movimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    FAuracronChampionMovementConfig MovementConfig;

    /** ConfiguraÃ§Ã£o de input */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    FAuracronChampionInputConfig InputConfig;

    /** NÃ­vel do CampeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration", meta = (ClampMin = "1", ClampMax = "18"))
    int32 ChampionLevel = 1;

    /** ExperiÃªncia atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration", meta = (ClampMin = "0"))
    int32 CurrentExperience = 0;

    /** ExperiÃªncia para prÃ³ximo nÃ­vel */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration", meta = (ClampMin = "100"))
    int32 ExperienceToNextLevel = 280;

    /** Custo para desbloquear (em moedas do jogo) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration", meta = (ClampMin = "0"))
    int32 UnlockCost = 3150;

    /** CampeÃ£o desbloqueado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    bool bUnlocked = false;

    /** DisponÃ­vel para seleÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    bool bAvailableForSelection = true;

    /** CompatÃ­vel com SÃ­gilos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    bool bSigiloCompatible = true;
};

/**
 * Estrutura para entrada de configuração de campeão (substitui TMap para replicação)
 */
USTRUCT(BlueprintType)
struct AURACRONCHAMPIONSBRIDGE_API FAuracronChampionConfigurationEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    FString ChampionID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Configuration")
    FAuracronChampionConfiguration Configuration;

    FAuracronChampionConfigurationEntry()
    {
        ChampionID = TEXT("");
    }

    FAuracronChampionConfigurationEntry(const FString& InChampionID, const FAuracronChampionConfiguration& InConfiguration)
        : ChampionID(InChampionID), Configuration(InConfiguration)
    {
    }
};

/**
 * Classe principal do Bridge para Sistema de CampeÃµes
 * ResponsÃ¡vel pelo gerenciamento completo de campeÃµes com integraÃ§Ã£o MetaHuman
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Champions", meta = (DisplayName = "AURACRON Champions Bridge", BlueprintSpawnableComponent))
class AURACRONCHAMPIONSBRIDGE_API UAuracronChampionsBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronChampionsBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Champion Management ===

    /**
     * Selecionar CampeÃ£o para a partida
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Selection", CallInEditor)
    bool SelectChampion(const FString& ChampionID);

    /**
     * Spawnar CampeÃ£o no mundo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Spawning", CallInEditor)
    bool SpawnChampion(const FVector& SpawnLocation, const FRotator& SpawnRotation);

    /**
     * Despawnar CampeÃ£o atual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Spawning", CallInEditor)
    bool DespawnChampion();

    /**
     * Obter CampeÃ£o atualmente selecionado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Selection", CallInEditor)
    FString GetSelectedChampionID() const { return SelectedChampionID; }

    /**
     * Obter configuraÃ§Ã£o do CampeÃ£o atual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Configuration", CallInEditor)
    FAuracronChampionConfiguration GetCurrentChampionConfiguration() const;

    /**
     * Verificar se CampeÃ£o estÃ¡ vivo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|State", CallInEditor)
    bool IsChampionAlive() const;

    /**
     * Obter estado atual do CampeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|State", CallInEditor)
    EAuracronChampionState GetChampionState() const { return CurrentChampionState; }

    // === Ability Management ===

    /**
     * Ativar habilidade por slot
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Abilities", CallInEditor)
    bool ActivateAbility(const FString& AbilitySlot);

    /**
     * Cancelar habilidade ativa
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Abilities", CallInEditor)
    bool CancelActiveAbility();

    /**
     * Upar habilidade
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Abilities", CallInEditor)
    bool LevelUpAbility(const FString& AbilitySlot);

    /**
     * Obter cooldown de habilidade
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Abilities", CallInEditor)
    float GetAbilityCooldown(const FString& AbilitySlot) const;

    /**
     * Verificar se habilidade estÃ¡ disponÃ­vel
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Abilities", CallInEditor)
    bool IsAbilityAvailable(const FString& AbilitySlot) const;

    /**
     * Obter nÃ­vel de habilidade
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Abilities", CallInEditor)
    int32 GetAbilityLevel(const FString& AbilitySlot) const;

    // === Attribute Management ===

    /**
     * Aplicar dano ao CampeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Attributes", CallInEditor)
    bool ApplyDamage(float DamageAmount, bool bIsMagicDamage = false);

    /**
     * Curar CampeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Attributes", CallInEditor)
    bool HealChampion(float HealAmount);

    /**
     * Restaurar mana
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Attributes", CallInEditor)
    bool RestoreMana(float ManaAmount);

    /**
     * Obter HP atual em percentual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Attributes", CallInEditor)
    float GetHealthPercentage() const;

    /**
     * Obter Mana atual em percentual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Attributes", CallInEditor)
    float GetManaPercentage() const;

    /**
     * Modificar atributo temporariamente
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Attributes", CallInEditor)
    bool ModifyAttribute(const FString& AttributeName, float ModifierValue, float Duration);

    // === Level and Experience ===

    /**
     * Ganhar experiÃªncia
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Progression", CallInEditor)
    bool GainExperience(int32 ExperienceAmount);

    /**
     * Subir de nÃ­vel
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Progression", CallInEditor)
    bool LevelUp();

    /**
     * Obter experiÃªncia necessÃ¡ria para prÃ³ximo nÃ­vel
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Progression", CallInEditor)
    int32 GetExperienceToNextLevel() const;

    /**
     * Calcular atributos baseados no nÃ­vel
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Progression", CallInEditor)
    FAuracronChampionBaseAttributes CalculateAttributesForLevel(int32 Level) const;

    // === MetaHuman Integration ===

    /**
     * Aplicar configuraÃ§Ã£o MetaHuman
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|MetaHuman", CallInEditor)
    bool ApplyMetaHumanConfiguration();

    /**
     * Customizar aparÃªncia facial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|MetaHuman", CallInEditor)
    bool CustomizeFacialAppearance(const TMap<FString, float>& FacialParameters);

    /**
     * Aplicar skin alternativa
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|MetaHuman", CallInEditor)
    bool ApplyAlternativeSkin(int32 SkinIndex);

    /**
     * Resetar para aparÃªncia padrÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|MetaHuman", CallInEditor)
    bool ResetToDefaultAppearance();

    // === Input Management ===

    /**
     * Configurar input do CampeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Input", CallInEditor)
    bool SetupChampionInput();

    /**
     * Processar input de movimento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Input", CallInEditor)
    void ProcessMovementInput(const FVector2D& MovementVector);

    /**
     * Processar input de cÃ¢mera
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Input", CallInEditor)
    void ProcessLookInput(const FVector2D& LookVector);

    // === Configuration Management ===

    /**
     * Obter configuraÃ§Ã£o de um CampeÃ£o especÃ­fico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Configuration", CallInEditor)
    FAuracronChampionConfiguration GetChampionConfiguration(const FString& ChampionID) const;

    /**
     * Definir configuraÃ§Ã£o de um CampeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Configuration", CallInEditor)
    void SetChampionConfiguration(const FString& ChampionID, const FAuracronChampionConfiguration& Configuration);

    /**
     * Carregar configuraÃ§Ãµes padrÃ£o dos CampeÃµes
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Configuration", CallInEditor)
    bool LoadDefaultChampionConfigurations();

    /**
     * Obter lista de todos os CampeÃµes disponÃ­veis
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Champions|Configuration", CallInEditor)
    TArray<FString> GetAvailableChampions() const;

protected:
    // === Internal Management Methods ===

    /** Inicializar sistema de CampeÃµes */
    bool InitializeChampionSystem();

    /** Configurar AbilitySystemComponent */
    bool SetupAbilitySystem();

    /** Configurar sistema de movimento */
    bool SetupMovementSystem();

    /** Configurar sistema de animaÃ§Ã£o */
    bool SetupAnimationSystem();

    /** Aplicar atributos base */
    bool ApplyBaseAttributes(const FAuracronChampionBaseAttributes& Attributes);

    /** Conceder habilidades */
    bool GrantAbilities(const FAuracronChampionAbilities& Abilities);

    /** Remover habilidades */
    bool RemoveAbilities();

    /** Atualizar mesh do CampeÃ£o */
    bool UpdateChampionMesh(const FAuracronChampionVisualConfig& VisualConfig);

    /** Validar configuraÃ§Ã£o de CampeÃ£o */
    bool ValidateChampionConfiguration(const FAuracronChampionConfiguration& Configuration) const;

    /** Processar regeneraÃ§Ã£o */
    void ProcessRegeneration(float DeltaTime);

    /** Processar cooldowns */
    void ProcessCooldowns(float DeltaTime);

public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ãµes dos CampeÃµes disponÃ­veis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronChampionConfigurationEntry> ChampionConfigurations;

    /** ID do CampeÃ£o atualmente selecionado */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_SelectedChampionID)
    FString SelectedChampionID;

    /** Estado atual do CampeÃ£o */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_ChampionState)
    EAuracronChampionState CurrentChampionState = EAuracronChampionState::Idle;

    /** Atributos atuais do CampeÃ£o */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_CurrentAttributes)
    FAuracronChampionBaseAttributes CurrentAttributes;

    /** ReferÃªncia ao AbilitySystemComponent */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

    /** ReferÃªncia ao Enhanced Input Component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UEnhancedInputComponent> EnhancedInputComponent;

    /** ReferÃªncia ao Character Movement Component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UCharacterMovementComponent> MovementComponent;

    /** ReferÃªncia ao Skeletal Mesh Component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USkeletalMeshComponent> ChampionMesh;

private:
    // === Internal State ===

    /** Habilidades concedidas */
    TArray<FGameplayAbilitySpecHandle> GrantedAbilities;

    /** Efeitos ativos */
    TArray<FActiveGameplayEffectHandle> ActiveEffects;

    /** Componentes de efeitos visuais */
    TArray<TObjectPtr<UNiagaraComponent>> ActiveVisualEffects;

    /** Sistema inicializado */
    bool bSystemInitialized = false;

    /** CampeÃ£o spawnado */
    bool bChampionSpawned = false;

    /** Timer para regeneraÃ§Ã£o */
    FTimerHandle RegenerationTimer;

    /** Timer para cooldowns */
    FTimerHandle CooldownTimer;

    /** Mutex para thread safety */
    mutable FCriticalSection ChampionMutex;

    // === Replication Callbacks ===

    UFUNCTION()
    void OnRep_SelectedChampionID();

    UFUNCTION()
    void OnRep_ChampionState();

    UFUNCTION()
    void OnRep_CurrentAttributes();

public:
    // === Delegates ===

    /** Delegate chamado quando CampeÃ£o Ã© selecionado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnChampionSelected, FString, ChampionID);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Champions|Events")
    FOnChampionSelected OnChampionSelected;

    /** Delegate chamado quando CampeÃ£o Ã© spawnado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnChampionSpawned, FString, ChampionID);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Champions|Events")
    FOnChampionSpawned OnChampionSpawned;

    /** Delegate chamado quando CampeÃ£o morre */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnChampionDied, FString, ChampionID);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Champions|Events")
    FOnChampionDied OnChampionDied;

    /** Delegate chamado quando CampeÃ£o sobe de nÃ­vel */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnChampionLevelUp, FString, ChampionID, int32, NewLevel);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Champions|Events")
    FOnChampionLevelUp OnChampionLevelUp;

    /** Delegate chamado quando habilidade Ã© ativada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAbilityActivated, FString, ChampionID, FString, AbilitySlot);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Champions|Events")
    FOnAbilityActivated OnAbilityActivated;

    /** Delegate chamado quando atributos sÃ£o atualizados */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAttributesUpdated, FString, ChampionID, FAuracronChampionBaseAttributes, NewAttributes);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Champions|Events")
    FOnAttributesUpdated OnAttributesUpdated;
};

