// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageInteraction.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageInteraction_generated_h
#error "AuracronFoliageInteraction.generated.h already included, missing '#pragma once' in AuracronFoliageInteraction.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageInteraction_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class APawn;
class UAuracronFoliageCollisionManager;
class UAuracronFoliageInteractionManager;
class UHierarchicalInstancedStaticMeshComponent;
class UWorld;
enum class EAuracronPlayerInteractionType : uint8;
enum class EAuracronRecoverySimulationType : uint8;
enum class EAuracronSeasonType : uint8;
struct FAuracronFoliageBendingData;
struct FAuracronFoliageInteractionConfiguration;
struct FAuracronInteractionPerformanceData;
struct FAuracronPlayerInteractionData;
struct FAuracronRecoverySimulationData;

// ********** Begin ScriptStruct FAuracronFoliageInteractionConfiguration **************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_139_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageInteractionConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliageInteractionConfiguration;
// ********** End ScriptStruct FAuracronFoliageInteractionConfiguration ****************************

// ********** Begin ScriptStruct FAuracronPlayerInteractionData ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_273_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPlayerInteractionData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPlayerInteractionData;
// ********** End ScriptStruct FAuracronPlayerInteractionData **************************************

// ********** Begin ScriptStruct FAuracronFoliageBendingData ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_344_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageBendingData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliageBendingData;
// ********** End ScriptStruct FAuracronFoliageBendingData *****************************************

// ********** Begin ScriptStruct FAuracronRecoverySimulationData ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_424_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRecoverySimulationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRecoverySimulationData;
// ********** End ScriptStruct FAuracronRecoverySimulationData *************************************

// ********** Begin ScriptStruct FAuracronInteractionPerformanceData *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_504_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronInteractionPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronInteractionPerformanceData;
// ********** End ScriptStruct FAuracronInteractionPerformanceData *********************************

// ********** Begin Delegate FOnPlayerInteractionStarted *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_693_DELEGATE \
static void FOnPlayerInteractionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerInteractionStarted, const FString& InteractionId, APawn* Player);


// ********** End Delegate FOnPlayerInteractionStarted *********************************************

// ********** Begin Delegate FOnPlayerInteractionEnded *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_694_DELEGATE \
static void FOnPlayerInteractionEnded_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerInteractionEnded, const FString& InteractionId);


// ********** End Delegate FOnPlayerInteractionEnded ***********************************************

// ********** Begin Delegate FOnFoliageBendingStarted **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_695_DELEGATE \
static void FOnFoliageBendingStarted_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageBendingStarted, const FString& BendingId, const FString& FoliageInstanceId);


// ********** End Delegate FOnFoliageBendingStarted ************************************************

// ********** Begin Delegate FOnFoliageRecoveryCompleted *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_696_DELEGATE \
static void FOnFoliageRecoveryCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageRecoveryCompleted, const FString& RecoveryId);


// ********** End Delegate FOnFoliageRecoveryCompleted *********************************************

// ********** Begin Class UAuracronFoliageInteractionManager ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_558_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogInteractionStatistics); \
	DECLARE_FUNCTION(execDrawDebugInteractionInfo); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetBendingInstanceCount); \
	DECLARE_FUNCTION(execGetActiveInteractionCount); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execGetPerformanceData); \
	DECLARE_FUNCTION(execApplySeasonalInteractionEffects); \
	DECLARE_FUNCTION(execSynchronizeWithWindSystem); \
	DECLARE_FUNCTION(execIntegrateWithCollisionSystem); \
	DECLARE_FUNCTION(execSetBendingParameters); \
	DECLARE_FUNCTION(execUpdateRealTimeBending); \
	DECLARE_FUNCTION(execUpdateAdvancedTramplingEffects); \
	DECLARE_FUNCTION(execApplyAdvancedTrampling); \
	DECLARE_FUNCTION(execStartRecoveryForFoliage); \
	DECLARE_FUNCTION(execGetAllRecoverySimulations); \
	DECLARE_FUNCTION(execGetRecoverySimulation); \
	DECLARE_FUNCTION(execRemoveRecoverySimulation); \
	DECLARE_FUNCTION(execUpdateRecoverySimulation); \
	DECLARE_FUNCTION(execCreateRecoverySimulation); \
	DECLARE_FUNCTION(execApplyBendingToFoliageInstance); \
	DECLARE_FUNCTION(execGetAllFoliageBending); \
	DECLARE_FUNCTION(execGetFoliageBending); \
	DECLARE_FUNCTION(execRemoveFoliageBending); \
	DECLARE_FUNCTION(execUpdateFoliageBending); \
	DECLARE_FUNCTION(execCreateFoliageBending); \
	DECLARE_FUNCTION(execProcessPlayerMovement); \
	DECLARE_FUNCTION(execGetAllPlayerInteractions); \
	DECLARE_FUNCTION(execGetPlayerInteraction); \
	DECLARE_FUNCTION(execRemovePlayerInteraction); \
	DECLARE_FUNCTION(execUpdatePlayerInteraction); \
	DECLARE_FUNCTION(execCreatePlayerInteraction); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageInteractionManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_558_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageInteractionManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageInteractionManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageInteractionManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageInteractionManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageInteractionManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageInteractionManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_558_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageInteractionManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageInteractionManager(UAuracronFoliageInteractionManager&&) = delete; \
	UAuracronFoliageInteractionManager(const UAuracronFoliageInteractionManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageInteractionManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageInteractionManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageInteractionManager) \
	NO_API virtual ~UAuracronFoliageInteractionManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_555_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_558_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_558_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_558_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h_558_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageInteractionManager;

// ********** End Class UAuracronFoliageInteractionManager *****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInteraction_h

// ********** Begin Enum EAuracronPlayerInteractionType ********************************************
#define FOREACH_ENUM_EAURACRONPLAYERINTERACTIONTYPE(op) \
	op(EAuracronPlayerInteractionType::None) \
	op(EAuracronPlayerInteractionType::Touch) \
	op(EAuracronPlayerInteractionType::Push) \
	op(EAuracronPlayerInteractionType::Walk) \
	op(EAuracronPlayerInteractionType::Run) \
	op(EAuracronPlayerInteractionType::Jump) \
	op(EAuracronPlayerInteractionType::Crouch) \
	op(EAuracronPlayerInteractionType::Attack) \
	op(EAuracronPlayerInteractionType::Harvest) \
	op(EAuracronPlayerInteractionType::Custom) 

enum class EAuracronPlayerInteractionType : uint8;
template<> struct TIsUEnumClass<EAuracronPlayerInteractionType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronPlayerInteractionType>();
// ********** End Enum EAuracronPlayerInteractionType **********************************************

// ********** Begin Enum EAuracronFoliageBendingType ***********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGEBENDINGTYPE(op) \
	op(EAuracronFoliageBendingType::None) \
	op(EAuracronFoliageBendingType::Gentle) \
	op(EAuracronFoliageBendingType::Moderate) \
	op(EAuracronFoliageBendingType::Strong) \
	op(EAuracronFoliageBendingType::Extreme) \
	op(EAuracronFoliageBendingType::Directional) \
	op(EAuracronFoliageBendingType::Radial) \
	op(EAuracronFoliageBendingType::Custom) 

enum class EAuracronFoliageBendingType : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageBendingType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageBendingType>();
// ********** End Enum EAuracronFoliageBendingType *************************************************

// ********** Begin Enum EAuracronRecoverySimulationType *******************************************
#define FOREACH_ENUM_EAURACRONRECOVERYSIMULATIONTYPE(op) \
	op(EAuracronRecoverySimulationType::None) \
	op(EAuracronRecoverySimulationType::Instant) \
	op(EAuracronRecoverySimulationType::Linear) \
	op(EAuracronRecoverySimulationType::Exponential) \
	op(EAuracronRecoverySimulationType::Spring) \
	op(EAuracronRecoverySimulationType::Elastic) \
	op(EAuracronRecoverySimulationType::Damped) \
	op(EAuracronRecoverySimulationType::Custom) 

enum class EAuracronRecoverySimulationType : uint8;
template<> struct TIsUEnumClass<EAuracronRecoverySimulationType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronRecoverySimulationType>();
// ********** End Enum EAuracronRecoverySimulationType *********************************************

// ********** Begin Enum EAuracronInteractionResponseType ******************************************
#define FOREACH_ENUM_EAURACRONINTERACTIONRESPONSETYPE(op) \
	op(EAuracronInteractionResponseType::None) \
	op(EAuracronInteractionResponseType::Visual) \
	op(EAuracronInteractionResponseType::Physics) \
	op(EAuracronInteractionResponseType::Audio) \
	op(EAuracronInteractionResponseType::Particle) \
	op(EAuracronInteractionResponseType::Combined) \
	op(EAuracronInteractionResponseType::Gameplay) \
	op(EAuracronInteractionResponseType::Custom) 

enum class EAuracronInteractionResponseType : uint8;
template<> struct TIsUEnumClass<EAuracronInteractionResponseType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronInteractionResponseType>();
// ********** End Enum EAuracronInteractionResponseType ********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
