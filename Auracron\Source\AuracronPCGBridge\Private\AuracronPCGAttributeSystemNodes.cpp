// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Attribute System Nodes Implementation
// Bridge 2.5: PCG Framework - Attribute System

#include "AuracronPCGAttributeSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"

// Engine includes
#include "Curves/CurveFloat.h"
#include "Async/ParallelFor.h"

// =============================================================================
// ATTRIBUTE INTERPOLATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGAttributeInterpolatorSettings::UAuracronPCGAttributeInterpolatorSettings()
{
    NodeMetadata.NodeName = TEXT("Attribute Interpolator");
    NodeMetadata.NodeDescription = TEXT("Interpolates between attribute values using various interpolation methods");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Attribute;
    NodeMetadata.Tags.Add(TEXT("Attribute"));
    NodeMetadata.Tags.Add(TEXT("Interpolate"));
    NodeMetadata.Tags.Add(TEXT("Blend"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.6f, 0.2f);
}

void UAuracronPCGAttributeInterpolatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAttributeInterpolatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
}

FAuracronPCGElementResult FAuracronPCGAttributeInterpolatorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                FPCGDataCollection& OutputData, 
                                                                                const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        const UAuracronPCGAttributeInterpolatorSettings* Settings = GetTypedSettings<UAuracronPCGAttributeInterpolatorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Attribute Interpolator");
            return Result;
        }

        int32 TotalProcessed = 0;

        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGSpatialData* InputSpatialData = Cast<UPCGSpatialData>(TaggedData.Data);
            if (!InputSpatialData)
            {
                continue;
            }

            const UPCGPointData* InputPointData = InputSpatialData->ToPointData(nullptr);
            if (!InputPointData || !InputPointData->Metadata)
            {
                continue;
            }

            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            if (ProcessInterpolation(OutputPointData, Settings))
            {
                TotalProcessed += InputPointData->GetPoints().Num();

                FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
                OutputTaggedData.Data = OutputPointData;
                OutputTaggedData.Pin = TEXT("Output");
                OutputTaggedData.Tags = TaggedData.Tags;
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Attribute Interpolator processed %d points"), TotalProcessed);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Attribute Interpolator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGAttributeInterpolatorElement::ProcessInterpolation(UPCGPointData* PointData, 
                                                                     const UAuracronPCGAttributeInterpolatorSettings* Settings) const
{
    UPCGMetadata* Metadata = PointData->Metadata;
    if (!Metadata)
    {
        return false;
    }

    // Check if source attributes exist
    if (!Metadata->HasAttribute(FName(*Settings->SourceAttributeA)) || 
        !Metadata->HasAttribute(FName(*Settings->SourceAttributeB)))
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Source attributes not found"));
        return false;
    }

    // Create target attribute if it doesn't exist
    if (!Metadata->HasAttribute(FName(*Settings->TargetAttribute)))
    {
        // Determine type from source A
        const UPCGMetadataAttributeBase* SourceAttrA = Metadata->GetConstAttribute(FName(*Settings->SourceAttributeA));
        if (SourceAttrA->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
        {
            Metadata->CreateFloatAttribute(FName(*Settings->TargetAttribute), 0.0f, true);
        }
        else if (SourceAttrA->GetTypeId() == PCG::Private::MetadataTypes<FVector>::Id)
        {
            Metadata->CreateVectorAttribute(FName(*Settings->TargetAttribute), FVector::ZeroVector, true);
        }
        // Add more types as needed
    }

    // Get alpha attribute or use default
    const UPCGMetadataAttributeBase* AlphaAttr = nullptr;
    if (!Settings->AlphaAttribute.IsEmpty() && Metadata->HasAttribute(FName(*Settings->AlphaAttribute)))
    {
        AlphaAttr = Metadata->GetConstAttribute(FName(*Settings->AlphaAttribute));
    }

    // Load curve if needed
    const UCurveFloat* Curve = nullptr;
    if (Settings->InterpolationMethod == EAuracronPCGAttributeInterpolation::Curve && Settings->InterpolationCurve.IsValid())
    {
        Curve = Settings->InterpolationCurve.LoadSynchronous();
    }

    // Process interpolation for each point
    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    const UPCGMetadataAttributeBase* SourceAttrA = Metadata->GetConstAttribute(FName(*Settings->SourceAttributeA));
    const UPCGMetadataAttributeBase* SourceAttrB = Metadata->GetConstAttribute(FName(*Settings->SourceAttributeB));
    UPCGMetadataAttributeBase* TargetAttr = Metadata->GetMutableAttribute(FName(*Settings->TargetAttribute));

    // Handle float interpolation
    if (SourceAttrA->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
    {
        const UPCGMetadataAttribute<float>* FloatAttrA = static_cast<const UPCGMetadataAttribute<float>*>(SourceAttrA);
        const UPCGMetadataAttribute<float>* FloatAttrB = static_cast<const UPCGMetadataAttribute<float>*>(SourceAttrB);
        UPCGMetadataAttribute<float>* FloatTargetAttr = static_cast<UPCGMetadataAttribute<float>*>(TargetAttr);
        const UPCGMetadataAttribute<float>* FloatAlphaAttr = AlphaAttr ? static_cast<const UPCGMetadataAttribute<float>*>(AlphaAttr) : nullptr;

        for (int32 i = 0; i < Points.Num(); i++)
        {
            float ValueA = FloatAttrA->GetValueFromItemKey(Points[i].MetadataEntry);
            float ValueB = FloatAttrB->GetValueFromItemKey(Points[i].MetadataEntry);
            float Alpha = FloatAlphaAttr ? FloatAlphaAttr->GetValueFromItemKey(Points[i].MetadataEntry) : Settings->DefaultAlpha;

            if (Settings->bClampAlpha)
            {
                Alpha = FMath::Clamp(Alpha, 0.0f, 1.0f);
            }

            if (Settings->bNormalizeAlpha)
            {
                Alpha = FMath::GetMappedRangeValueClamped(Settings->AlphaRange, FVector2D(0.0f, 1.0f), Alpha);
            }

            float InterpolatedValue = AuracronPCGAttributeSystemUtils::InterpolateFloat(ValueA, ValueB, Alpha, Settings->InterpolationMethod, Curve);
            FloatTargetAttr->SetValueFromItemKey(Points[i].MetadataEntry, InterpolatedValue);
        }
    }
    // Handle vector interpolation
    else if (SourceAttrA->GetTypeId() == PCG::Private::MetadataTypes<FVector>::Id)
    {
        const UPCGMetadataAttribute<FVector>* VectorAttrA = static_cast<const UPCGMetadataAttribute<FVector>*>(SourceAttrA);
        const UPCGMetadataAttribute<FVector>* VectorAttrB = static_cast<const UPCGMetadataAttribute<FVector>*>(SourceAttrB);
        UPCGMetadataAttribute<FVector>* VectorTargetAttr = static_cast<UPCGMetadataAttribute<FVector>*>(TargetAttr);
        const UPCGMetadataAttribute<float>* FloatAlphaAttr = AlphaAttr ? static_cast<const UPCGMetadataAttribute<float>*>(AlphaAttr) : nullptr;

        for (int32 i = 0; i < Points.Num(); i++)
        {
            FVector ValueA = VectorAttrA->GetValueFromItemKey(Points[i].MetadataEntry);
            FVector ValueB = VectorAttrB->GetValueFromItemKey(Points[i].MetadataEntry);
            float Alpha = FloatAlphaAttr ? FloatAlphaAttr->GetValueFromItemKey(Points[i].MetadataEntry) : Settings->DefaultAlpha;

            if (Settings->bClampAlpha)
            {
                Alpha = FMath::Clamp(Alpha, 0.0f, 1.0f);
            }

            if (Settings->bNormalizeAlpha)
            {
                Alpha = FMath::GetMappedRangeValueClamped(Settings->AlphaRange, FVector2D(0.0f, 1.0f), Alpha);
            }

            FVector InterpolatedValue = AuracronPCGAttributeSystemUtils::InterpolateVector(ValueA, ValueB, Alpha, Settings->InterpolationMethod);
            VectorTargetAttr->SetValueFromItemKey(Points[i].MetadataEntry, InterpolatedValue);
        }
    }

    return true;
}

// =============================================================================
// ATTRIBUTE FILTER IMPLEMENTATION
// =============================================================================

UAuracronPCGAttributeFilterSettings::UAuracronPCGAttributeFilterSettings()
{
    NodeMetadata.NodeName = TEXT("Attribute Filter");
    NodeMetadata.NodeDescription = TEXT("Filters attributes based on various criteria and patterns");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Attribute;
    NodeMetadata.Tags.Add(TEXT("Attribute"));
    NodeMetadata.Tags.Add(TEXT("Filter"));
    NodeMetadata.Tags.Add(TEXT("Select"));
    NodeMetadata.NodeColor = FLinearColor(0.4f, 0.8f, 0.6f);
}

void UAuracronPCGAttributeFilterSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAttributeFilterSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Filtered");
    OutputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;

    if (bOutputFilteredAttributes)
    {
        FPCGPinProperties& RemovedPin = OutputPins.Emplace_GetRef();
        RemovedPin.Label = TEXT("Removed");
        RemovedPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
        RemovedPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGAttributeFilterElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                          FPCGDataCollection& OutputData, 
                                                                          const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        const UAuracronPCGAttributeFilterSettings* Settings = GetTypedSettings<UAuracronPCGAttributeFilterSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Attribute Filter");
            return Result;
        }

        int32 TotalProcessed = 0;

        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGSpatialData* InputSpatialData = Cast<UPCGSpatialData>(TaggedData.Data);
            if (!InputSpatialData)
            {
                continue;
            }

            const UPCGPointData* InputPointData = InputSpatialData->ToPointData(nullptr);
            if (!InputPointData || !InputPointData->Metadata)
            {
                continue;
            }

            UPCGPointData* FilteredData = NewObject<UPCGPointData>();
            UPCGPointData* RemovedData = Settings->bOutputFilteredAttributes ? NewObject<UPCGPointData>() : nullptr;

            if (ProcessAttributeFilter(InputPointData, FilteredData, RemovedData, Settings))
            {
                TotalProcessed += InputPointData->GetPoints().Num();

                // Add filtered data to output
                FPCGTaggedData& FilteredTaggedData = OutputData.TaggedData.Emplace_GetRef();
                FilteredTaggedData.Data = FilteredData;
                FilteredTaggedData.Pin = TEXT("Filtered");
                FilteredTaggedData.Tags = TaggedData.Tags;

                // Add removed data if requested
                if (RemovedData)
                {
                    FPCGTaggedData& RemovedTaggedData = OutputData.TaggedData.Emplace_GetRef();
                    RemovedTaggedData.Data = RemovedData;
                    RemovedTaggedData.Pin = TEXT("Removed");
                    RemovedTaggedData.Tags = TaggedData.Tags;
                }
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Attribute Filter processed %d points"), TotalProcessed);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Attribute Filter error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGAttributeFilterElement::ProcessAttributeFilter(const UPCGPointData* InputData, 
                                                                UPCGPointData* FilteredData, 
                                                                UPCGPointData* RemovedData,
                                                                const UAuracronPCGAttributeFilterSettings* Settings) const
{
    if (!InputData || !FilteredData)
    {
        return false;
    }

    // Initialize output data
    FilteredData->InitializeFromData(InputData);
    if (RemovedData)
    {
        RemovedData->InitializeFromData(InputData);
    }

    // Get all attribute names
    TArray<FString> AllAttributeNames = UAuracronPCGAttributeSystemUtils::GetAttributeNames(InputData->Metadata);
    TArray<FString> FilteredAttributeNames;

    // Apply filtering logic
    for (const FString& AttributeName : AllAttributeNames)
    {
        bool bShouldInclude = ShouldIncludeAttribute(AttributeName, Settings);
        
        if (bShouldInclude)
        {
            FilteredAttributeNames.Add(AttributeName);
        }
    }

    // Create filtered metadata
    if (Settings->bPreserveMetadata && FilteredData->Metadata)
    {
        // Copy only filtered attributes
        for (const FString& AttributeName : FilteredAttributeNames)
        {
            UAuracronPCGAttributeSystemUtils::CopyAttribute(
                InputData->Metadata, FilteredData->Metadata, AttributeName);
        }
    }

    // Create removed metadata if needed
    if (RemovedData && RemovedData->Metadata)
    {
        TArray<FString> RemovedAttributeNames;
        for (const FString& AttributeName : AllAttributeNames)
        {
            if (!FilteredAttributeNames.Contains(AttributeName))
            {
                RemovedAttributeNames.Add(AttributeName);
            }
        }

        for (const FString& AttributeName : RemovedAttributeNames)
        {
            UAuracronPCGAttributeSystemUtils::CopyAttribute(
                InputData->Metadata, RemovedData->Metadata, AttributeName);
        }
    }

    return true;
}

bool FAuracronPCGAttributeFilterElement::ShouldIncludeAttribute(const FString& AttributeName, 
                                                                const UAuracronPCGAttributeFilterSettings* Settings) const
{
    bool bInclude = false;

    switch (Settings->FilterMode)
    {
        case EAuracronPCGAttributeFilterMode::Include:
            bInclude = Settings->AttributeNames.Contains(AttributeName);
            break;
        case EAuracronPCGAttributeFilterMode::Exclude:
            bInclude = !Settings->AttributeNames.Contains(AttributeName);
            break;
        case EAuracronPCGAttributeFilterMode::IncludePattern:
            bInclude = AuracronPCGAttributeSystemUtils::MatchesPattern(AttributeName, Settings->FilterPattern);
            break;
        case EAuracronPCGAttributeFilterMode::ExcludePattern:
            bInclude = !AuracronPCGAttributeSystemUtils::MatchesPattern(AttributeName, Settings->FilterPattern);
            break;
    }

    return bInclude;
}
