// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Telemetria e Analytics Bridge Implementation

#include "AuracronAnalyticsBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "Analytics.h"
#include "AnalyticsEventAttribute.h"
#include "Interfaces/IAnalyticsProvider.h"
#include "Json.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Kismet/GameplayStatics.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/Guid.h"

UAuracronAnalyticsBridge::UAuracronAnalyticsBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f; // 1 FPS para analytics
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Gerar ID de sessão único
    CurrentSessionID = FGuid::NewGuid().ToString();
    
    // Configurações padrão
    bAnalyticsEnabled = true;
}

void UAuracronAnalyticsBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Analytics"));

    // Inicializar sistema
    bSystemInitialized = InitializeAnalyticsSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers
        GetWorld()->GetTimerManager().SetTimer(
            EventProcessingTimer,
            [this]()
            {
                ProcessEventQueue(1.0f);
            },
            1.0f,
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            AutoCollectionTimer,
            [this]()
            {
                CollectAutomaticMetrics(5.0f);
            },
            5.0f,
            true
        );
        
        // Registrar início de sessão
        FAuracronAnalyticsEvent SessionStartEvent;
        SessionStartEvent.EventID = FGuid::NewGuid().ToString();
        SessionStartEvent.EventName = TEXT("SessionStart");
        SessionStartEvent.EventType = EAuracronAnalyticsEventType::UserBehavior;
        SessionStartEvent.EventCategory = TEXT("Session");
        SessionStartEvent.EventTimestamp = FDateTime::Now();
        SessionStartEvent.SessionID = CurrentSessionID;
        SessionStartEvent.Priority = 8;

        RecordAnalyticsEvent(SessionStartEvent);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Analytics inicializado - Sessão: %s"), *CurrentSessionID);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Analytics"));
    }
}

void UAuracronAnalyticsBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Registrar fim de sessão
    if (bSystemInitialized)
    {
        FAuracronAnalyticsEvent SessionEndEvent;
        SessionEndEvent.EventID = FGuid::NewGuid().ToString();
        SessionEndEvent.EventName = TEXT("SessionEnd");
        SessionEndEvent.EventType = EAuracronAnalyticsEventType::UserBehavior;
        SessionEndEvent.EventCategory = TEXT("Session");
        SessionEndEvent.EventTimestamp = FDateTime::Now();
        SessionEndEvent.SessionID = CurrentSessionID;
        SessionEndEvent.Priority = 8;

        RecordAnalyticsEvent(SessionEndEvent);
        
        // Processar fila final
        ProcessEventQueue(0.0f);
    }

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(EventProcessingTimer);
        GetWorld()->GetTimerManager().ClearTimer(AutoCollectionTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronAnalyticsBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronAnalyticsBridge, BalanceMetrics);
    DOREPLIFETIME(UAuracronAnalyticsBridge, ActiveABTests);
}

void UAuracronAnalyticsBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized || !bAnalyticsEnabled)
        return;

    // Processar fila de eventos
    ProcessEventQueue(DeltaTime);
    
    // Coletar métricas automáticas
    CollectAutomaticMetrics(DeltaTime);
}

// === Core Analytics ===

bool UAuracronAnalyticsBridge::RecordAnalyticsEvent(const FAuracronAnalyticsEvent& Event)
{
    if (!bSystemInitialized || !bAnalyticsEnabled)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de analytics não inicializado ou desabilitado"));
        return false;
    }

    if (!ValidateAnalyticsEvent(Event))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Evento analítico inválido: %s"), *Event.EventName);
        return false;
    }

    FScopeLock Lock(&AnalyticsMutex);

    // Adicionar à fila de eventos
    EventQueue.Add(Event);

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Evento analítico registrado: %s"), *Event.EventName);

    // Broadcast evento
    OnAnalyticsEventRecorded.Broadcast(Event);

    return true;
}

// === Private Helper Methods ===

void UAuracronAnalyticsBridge::ProcessEventQueue(float DeltaTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronAnalyticsBridge::ProcessEventQueue);
    
    if (EventQueue.IsEmpty())
    {
        return;
    }
    
    // Process events in batches to avoid frame drops
    const int32 MaxEventsPerFrame = 10;
    int32 ProcessedEvents = 0;
    
    while (!EventQueue.IsEmpty() && ProcessedEvents < MaxEventsPerFrame)
    {
        FAuracronAnalyticsEvent Event;
        if (EventQueue.Dequeue(Event))
        {
            // Send event to analytics provider
            if (AnalyticsProvider.IsValid())
            {
                TArray<FAnalyticsEventAttribute> Attributes;
                
                // Add string parameters
                for (const auto& Param : Event.EventParameters)
                {
                    Attributes.Add(FAnalyticsEventAttribute(Param.Key, Param.Value));
                }
                
                // Add numeric values
                for (const auto& NumValue : Event.NumericValues)
                {
                    Attributes.Add(FAnalyticsEventAttribute(NumValue.Key, NumValue.Value));
                }
                
                // Add metadata
                Attributes.Add(FAnalyticsEventAttribute(TEXT("SessionID"), Event.SessionID));
                Attributes.Add(FAnalyticsEventAttribute(TEXT("Priority"), Event.Priority));
                Attributes.Add(FAnalyticsEventAttribute(TEXT("Timestamp"), Event.EventTimestamp.ToString()));
                
                AnalyticsProvider->RecordEvent(Event.EventName, Attributes);
            }
            
            // Store for local analytics if needed
            if (bStoreLocalAnalytics)
            {
                LocalAnalyticsData.Add(Event);
            }
            
            ProcessedEvents++;
        }
    }
    
    // Flush analytics provider periodically
    static float FlushTimer = 0.0f;
    FlushTimer += DeltaTime;
    
    if (FlushTimer >= 30.0f) // Flush every 30 seconds
    {
        if (AnalyticsProvider.IsValid())
        {
            AnalyticsProvider->FlushEvents();
        }
        FlushTimer = 0.0f;
    }
}

void UAuracronAnalyticsBridge::CollectAutomaticMetrics(float DeltaTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronAnalyticsBridge::CollectAutomaticMetrics);
    
    static float MetricsTimer = 0.0f;
    MetricsTimer += DeltaTime;
    
    // Collect metrics every 5 seconds
    if (MetricsTimer < 5.0f)
    {
        return;
    }
    
    MetricsTimer = 0.0f;
    
    // Collect performance metrics
    if (UWorld* World = GetWorld())
    {
        // FPS metrics
        float CurrentFPS = 1.0f / DeltaTime;
        RecordPerformanceMetric(TEXT("FPS"), CurrentFPS, TEXT("Automatic"));
        
        // Memory usage
        FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
        float MemoryUsageMB = MemStats.UsedPhysical / (1024.0f * 1024.0f);
        RecordPerformanceMetric(TEXT("MemoryUsage"), MemoryUsageMB, TEXT("Automatic"));
        
        // Player count
        if (AGameStateBase* GameState = World->GetGameState())
        {
            int32 PlayerCount = GameState->PlayerArray.Num();
            RecordPerformanceMetric(TEXT("PlayerCount"), static_cast<float>(PlayerCount), TEXT("Automatic"));
        }
        
        // Network metrics
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            if (UNetConnection* NetConnection = PC->GetNetConnection())
            {
                float Ping = NetConnection->AvgLag * 1000.0f; // Convert to ms
                RecordPerformanceMetric(TEXT("Ping"), Ping, TEXT("Automatic"));
                
                float PacketLoss = NetConnection->GetPacketLoss();
                RecordPerformanceMetric(TEXT("PacketLoss"), PacketLoss, TEXT("Automatic"));
            }
        }
        
        // GPU metrics (if available)
        if (GEngine && GEngine->GetGPUUsage() > 0.0f)
        {
            float GPUUsage = GEngine->GetGPUUsage();
            RecordPerformanceMetric(TEXT("GPUUsage"), GPUUsage, TEXT("Automatic"));
        }
        
        // Audio metrics
        if (FAudioDevice* AudioDevice = World->GetAudioDevice())
        {
            int32 ActiveSounds = AudioDevice->GetNumActiveSounds();
            RecordPerformanceMetric(TEXT("ActiveSounds"), static_cast<float>(ActiveSounds), TEXT("Automatic"));
        }
    }
}

bool UAuracronAnalyticsBridge::RecordGameplayEvent(const FString& EventName, const TMap<FString, FString>& Parameters)
{
    if (EventName.IsEmpty())
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = EventName;
    Event.EventType = EAuracronAnalyticsEventType::GameplayEvent;
    Event.EventCategory = TEXT("Gameplay");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.EventParameters = Parameters;
    Event.Priority = 6;

    // Obter informações do jogador
    if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
    {
        if (APlayerState* PS = PC->GetPlayerState<APlayerState>())
        {
            Event.PlayerID = PS->GetUniqueId().ToString();
        }
    }

    return RecordAnalyticsEvent(Event);
}

bool UAuracronAnalyticsBridge::RecordPerformanceMetric(const FString& MetricName, float Value, const FString& Context)
{
    if (MetricName.IsEmpty())
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = FString::Printf(TEXT("Performance_%s"), *MetricName);
    Event.EventType = EAuracronAnalyticsEventType::PerformanceMetric;
    Event.EventCategory = TEXT("Performance");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.NumericValues.Add(MetricName, Value);
    Event.EventParameters.Add(TEXT("Context"), Context);
    Event.Priority = 4;

    return RecordAnalyticsEvent(Event);
}

bool UAuracronAnalyticsBridge::RecordBalanceData(const FAuracronBalanceMetrics& BalanceData)
{
    if (BalanceData.ObjectID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&AnalyticsMutex);

    // Atualizar ou adicionar métricas de balanceamento
    bool bFound = false;
    for (FAuracronBalanceMetrics& ExistingMetrics : BalanceMetrics)
    {
        if (ExistingMetrics.Category == BalanceData.Category && ExistingMetrics.ObjectID == BalanceData.ObjectID)
        {
            ExistingMetrics = BalanceData;
            ExistingMetrics.LastUpdated = FDateTime::Now();
            bFound = true;
            break;
        }
    }

    if (!bFound)
    {
        FAuracronBalanceMetrics NewMetrics = BalanceData;
        NewMetrics.LastUpdated = FDateTime::Now();
        BalanceMetrics.Add(NewMetrics);
    }

    // Criar evento analítico
    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = TEXT("BalanceData");
    Event.EventType = EAuracronAnalyticsEventType::BalanceData;
    Event.EventCategory = TEXT("Balance");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.EventParameters.Add(TEXT("ObjectID"), BalanceData.ObjectID);
    Event.EventParameters.Add(TEXT("Category"), UEnum::GetValueAsString(BalanceData.Category));
    Event.NumericValues.Add(TEXT("WinRate"), BalanceData.WinRate);
    Event.NumericValues.Add(TEXT("PickRate"), BalanceData.PickRate);
    Event.NumericValues.Add(TEXT("BanRate"), BalanceData.BanRate);
    Event.NumericValues.Add(TEXT("AverageKDA"), BalanceData.AverageKDA);
    Event.Priority = 7;

    RecordAnalyticsEvent(Event);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dados de balanceamento registrados para %s"), *BalanceData.ObjectID);

    return true;
}
