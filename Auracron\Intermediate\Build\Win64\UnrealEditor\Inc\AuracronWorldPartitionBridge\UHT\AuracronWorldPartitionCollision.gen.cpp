// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionCollision.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionCollision() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionCollisionManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionDescriptor();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionStatistics();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronCollisionStreamingState ******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCollisionStreamingState;
static UEnum* EAuracronCollisionStreamingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCollisionStreamingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCollisionStreamingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronCollisionStreamingState"));
	}
	return Z_Registration_Info_UEnum_EAuracronCollisionStreamingState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCollisionStreamingState>()
{
	return EAuracronCollisionStreamingState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision streaming states\n" },
#endif
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronCollisionStreamingState::Failed" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EAuracronCollisionStreamingState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronCollisionStreamingState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision streaming states" },
#endif
		{ "Unloaded.DisplayName", "Unloaded" },
		{ "Unloaded.Name", "EAuracronCollisionStreamingState::Unloaded" },
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EAuracronCollisionStreamingState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCollisionStreamingState::Unloaded", (int64)EAuracronCollisionStreamingState::Unloaded },
		{ "EAuracronCollisionStreamingState::Loading", (int64)EAuracronCollisionStreamingState::Loading },
		{ "EAuracronCollisionStreamingState::Loaded", (int64)EAuracronCollisionStreamingState::Loaded },
		{ "EAuracronCollisionStreamingState::Unloading", (int64)EAuracronCollisionStreamingState::Unloading },
		{ "EAuracronCollisionStreamingState::Failed", (int64)EAuracronCollisionStreamingState::Failed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronCollisionStreamingState",
	"EAuracronCollisionStreamingState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState()
{
	if (!Z_Registration_Info_UEnum_EAuracronCollisionStreamingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCollisionStreamingState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCollisionStreamingState.InnerSingleton;
}
// ********** End Enum EAuracronCollisionStreamingState ********************************************

// ********** Begin Enum EAuracronCollisionLODLevel ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCollisionLODLevel;
static UEnum* EAuracronCollisionLODLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCollisionLODLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCollisionLODLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronCollisionLODLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronCollisionLODLevel.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCollisionLODLevel>()
{
	return EAuracronCollisionLODLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision LOD levels\n" },
#endif
		{ "LOD0.DisplayName", "LOD 0 (Highest)" },
		{ "LOD0.Name", "EAuracronCollisionLODLevel::LOD0" },
		{ "LOD1.DisplayName", "LOD 1" },
		{ "LOD1.Name", "EAuracronCollisionLODLevel::LOD1" },
		{ "LOD2.DisplayName", "LOD 2" },
		{ "LOD2.Name", "EAuracronCollisionLODLevel::LOD2" },
		{ "LOD3.DisplayName", "LOD 3" },
		{ "LOD3.Name", "EAuracronCollisionLODLevel::LOD3" },
		{ "LOD4.DisplayName", "LOD 4 (Lowest)" },
		{ "LOD4.Name", "EAuracronCollisionLODLevel::LOD4" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision LOD levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCollisionLODLevel::LOD0", (int64)EAuracronCollisionLODLevel::LOD0 },
		{ "EAuracronCollisionLODLevel::LOD1", (int64)EAuracronCollisionLODLevel::LOD1 },
		{ "EAuracronCollisionLODLevel::LOD2", (int64)EAuracronCollisionLODLevel::LOD2 },
		{ "EAuracronCollisionLODLevel::LOD3", (int64)EAuracronCollisionLODLevel::LOD3 },
		{ "EAuracronCollisionLODLevel::LOD4", (int64)EAuracronCollisionLODLevel::LOD4 },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronCollisionLODLevel",
	"EAuracronCollisionLODLevel",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronCollisionLODLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCollisionLODLevel.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCollisionLODLevel.InnerSingleton;
}
// ********** End Enum EAuracronCollisionLODLevel **************************************************

// ********** Begin Enum EAuracronCollisionType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCollisionType;
static UEnum* EAuracronCollisionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCollisionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCollisionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronCollisionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronCollisionType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCollisionType>()
{
	return EAuracronCollisionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision types\n" },
#endif
		{ "Dynamic.DisplayName", "Dynamic" },
		{ "Dynamic.Name", "EAuracronCollisionType::Dynamic" },
		{ "Kinematic.DisplayName", "Kinematic" },
		{ "Kinematic.Name", "EAuracronCollisionType::Kinematic" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
		{ "Query.DisplayName", "Query Only" },
		{ "Query.Name", "EAuracronCollisionType::Query" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EAuracronCollisionType::Static" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision types" },
#endif
		{ "Trigger.DisplayName", "Trigger" },
		{ "Trigger.Name", "EAuracronCollisionType::Trigger" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCollisionType::Static", (int64)EAuracronCollisionType::Static },
		{ "EAuracronCollisionType::Dynamic", (int64)EAuracronCollisionType::Dynamic },
		{ "EAuracronCollisionType::Kinematic", (int64)EAuracronCollisionType::Kinematic },
		{ "EAuracronCollisionType::Trigger", (int64)EAuracronCollisionType::Trigger },
		{ "EAuracronCollisionType::Query", (int64)EAuracronCollisionType::Query },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronCollisionType",
	"EAuracronCollisionType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronCollisionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCollisionType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCollisionType.InnerSingleton;
}
// ********** End Enum EAuracronCollisionType ******************************************************

// ********** Begin Enum EAuracronCollisionComplexity **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCollisionComplexity;
static UEnum* EAuracronCollisionComplexity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCollisionComplexity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCollisionComplexity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronCollisionComplexity"));
	}
	return Z_Registration_Info_UEnum_EAuracronCollisionComplexity.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCollisionComplexity>()
{
	return EAuracronCollisionComplexity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision complexity\n" },
#endif
		{ "Complex.DisplayName", "Complex" },
		{ "Complex.Name", "EAuracronCollisionComplexity::Complex" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
		{ "Simple.DisplayName", "Simple" },
		{ "Simple.Name", "EAuracronCollisionComplexity::Simple" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision complexity" },
#endif
		{ "UseDefault.DisplayName", "Use Default" },
		{ "UseDefault.Name", "EAuracronCollisionComplexity::UseDefault" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCollisionComplexity::Simple", (int64)EAuracronCollisionComplexity::Simple },
		{ "EAuracronCollisionComplexity::Complex", (int64)EAuracronCollisionComplexity::Complex },
		{ "EAuracronCollisionComplexity::UseDefault", (int64)EAuracronCollisionComplexity::UseDefault },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronCollisionComplexity",
	"EAuracronCollisionComplexity",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity()
{
	if (!Z_Registration_Info_UEnum_EAuracronCollisionComplexity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCollisionComplexity.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCollisionComplexity.InnerSingleton;
}
// ********** End Enum EAuracronCollisionComplexity ************************************************

// ********** Begin ScriptStruct FAuracronCollisionConfiguration ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCollisionConfiguration;
class UScriptStruct* FAuracronCollisionConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCollisionConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCollisionConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronCollisionConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Collision Configuration\n * Configuration settings for collision streaming in world partition\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision Configuration\nConfiguration settings for collision streaming in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCollisionStreaming_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCollisionLOD_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncCollisionLoading_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionStreamingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionUnloadingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentCollisionOperations_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseLODDistance_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistanceMultiplier_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODLevel_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCollisionMemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCollisionCaching_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCollisionCulling_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultCollisionComplexity_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateSimpleCollision_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateComplexCollision_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCollisionDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogCollisionOperations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableCollisionStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollisionStreaming;
	static void NewProp_bEnableCollisionLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollisionLOD;
	static void NewProp_bEnableAsyncCollisionLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncCollisionLoading;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionStreamingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionUnloadingDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentCollisionOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseLODDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistanceMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLODLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxCollisionMemoryUsageMB;
	static void NewProp_bEnableCollisionCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollisionCaching;
	static void NewProp_bEnableCollisionCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollisionCulling;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultCollisionComplexity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultCollisionComplexity;
	static void NewProp_bGenerateSimpleCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateSimpleCollision;
	static void NewProp_bGenerateComplexCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateComplexCollision;
	static void NewProp_bEnableCollisionDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollisionDebug;
	static void NewProp_bLogCollisionOperations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogCollisionOperations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCollisionConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionStreaming_SetBit(void* Obj)
{
	((FAuracronCollisionConfiguration*)Obj)->bEnableCollisionStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionStreaming = { "bEnableCollisionStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCollisionStreaming_MetaData), NewProp_bEnableCollisionStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionLOD_SetBit(void* Obj)
{
	((FAuracronCollisionConfiguration*)Obj)->bEnableCollisionLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionLOD = { "bEnableCollisionLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCollisionLOD_MetaData), NewProp_bEnableCollisionLOD_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableAsyncCollisionLoading_SetBit(void* Obj)
{
	((FAuracronCollisionConfiguration*)Obj)->bEnableAsyncCollisionLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableAsyncCollisionLoading = { "bEnableAsyncCollisionLoading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableAsyncCollisionLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncCollisionLoading_MetaData), NewProp_bEnableAsyncCollisionLoading_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_CollisionStreamingDistance = { "CollisionStreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfiguration, CollisionStreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionStreamingDistance_MetaData), NewProp_CollisionStreamingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_CollisionUnloadingDistance = { "CollisionUnloadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfiguration, CollisionUnloadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionUnloadingDistance_MetaData), NewProp_CollisionUnloadingDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_MaxConcurrentCollisionOperations = { "MaxConcurrentCollisionOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfiguration, MaxConcurrentCollisionOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentCollisionOperations_MetaData), NewProp_MaxConcurrentCollisionOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_BaseLODDistance = { "BaseLODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfiguration, BaseLODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseLODDistance_MetaData), NewProp_BaseLODDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_LODDistanceMultiplier = { "LODDistanceMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfiguration, LODDistanceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistanceMultiplier_MetaData), NewProp_LODDistanceMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_MaxLODLevel = { "MaxLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfiguration, MaxLODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODLevel_MetaData), NewProp_MaxLODLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_MaxCollisionMemoryUsageMB = { "MaxCollisionMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfiguration, MaxCollisionMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCollisionMemoryUsageMB_MetaData), NewProp_MaxCollisionMemoryUsageMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionCaching_SetBit(void* Obj)
{
	((FAuracronCollisionConfiguration*)Obj)->bEnableCollisionCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionCaching = { "bEnableCollisionCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCollisionCaching_MetaData), NewProp_bEnableCollisionCaching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionCulling_SetBit(void* Obj)
{
	((FAuracronCollisionConfiguration*)Obj)->bEnableCollisionCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionCulling = { "bEnableCollisionCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCollisionCulling_MetaData), NewProp_bEnableCollisionCulling_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_DefaultCollisionComplexity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_DefaultCollisionComplexity = { "DefaultCollisionComplexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfiguration, DefaultCollisionComplexity), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultCollisionComplexity_MetaData), NewProp_DefaultCollisionComplexity_MetaData) }; // 2638560628
void Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bGenerateSimpleCollision_SetBit(void* Obj)
{
	((FAuracronCollisionConfiguration*)Obj)->bGenerateSimpleCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bGenerateSimpleCollision = { "bGenerateSimpleCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bGenerateSimpleCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateSimpleCollision_MetaData), NewProp_bGenerateSimpleCollision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bGenerateComplexCollision_SetBit(void* Obj)
{
	((FAuracronCollisionConfiguration*)Obj)->bGenerateComplexCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bGenerateComplexCollision = { "bGenerateComplexCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bGenerateComplexCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateComplexCollision_MetaData), NewProp_bGenerateComplexCollision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionDebug_SetBit(void* Obj)
{
	((FAuracronCollisionConfiguration*)Obj)->bEnableCollisionDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionDebug = { "bEnableCollisionDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCollisionDebug_MetaData), NewProp_bEnableCollisionDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bLogCollisionOperations_SetBit(void* Obj)
{
	((FAuracronCollisionConfiguration*)Obj)->bLogCollisionOperations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bLogCollisionOperations = { "bLogCollisionOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bLogCollisionOperations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogCollisionOperations_MetaData), NewProp_bLogCollisionOperations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableAsyncCollisionLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_CollisionStreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_CollisionUnloadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_MaxConcurrentCollisionOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_BaseLODDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_LODDistanceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_MaxLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_MaxCollisionMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_DefaultCollisionComplexity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_DefaultCollisionComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bGenerateSimpleCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bGenerateComplexCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bEnableCollisionDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewProp_bLogCollisionOperations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronCollisionConfiguration",
	Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::PropPointers),
	sizeof(FAuracronCollisionConfiguration),
	alignof(FAuracronCollisionConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCollisionConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCollisionConfiguration *************************************

// ********** Begin ScriptStruct FAuracronCollisionDescriptor **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCollisionDescriptor;
class UScriptStruct* FAuracronCollisionDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCollisionDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCollisionDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronCollisionDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Collision Descriptor\n * Descriptor for collision objects and their properties\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision Descriptor\nDescriptor for collision objects and their properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionName_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceActorId_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionType_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionComplexity_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingState_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLODLevel_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Bounds_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriangleCount_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConvexCount_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEnabled_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "Collision Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceActorId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionComplexity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionComplexity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentLODLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentLODLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Bounds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TriangleCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ConvexCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_bIsEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEnabled;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastAccessTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCollisionDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionName = { "CollisionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, CollisionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionName_MetaData), NewProp_CollisionName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_SourceActorId = { "SourceActorId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, SourceActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceActorId_MetaData), NewProp_SourceActorId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionType = { "CollisionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, CollisionType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionType_MetaData), NewProp_CollisionType_MetaData) }; // 3676296331
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionComplexity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionComplexity = { "CollisionComplexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, CollisionComplexity), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionComplexity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionComplexity_MetaData), NewProp_CollisionComplexity_MetaData) }; // 2638560628
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_StreamingState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_StreamingState = { "StreamingState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, StreamingState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingState_MetaData), NewProp_StreamingState_MetaData) }; // 889311792
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CurrentLODLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CurrentLODLevel = { "CurrentLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, CurrentLODLevel), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLODLevel_MetaData), NewProp_CurrentLODLevel_MetaData) }; // 3026529594
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_Bounds = { "Bounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, Bounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Bounds_MetaData), NewProp_Bounds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_TriangleCount = { "TriangleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, TriangleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriangleCount_MetaData), NewProp_TriangleCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_ConvexCount = { "ConvexCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, ConvexCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConvexCount_MetaData), NewProp_ConvexCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_bIsEnabled_SetBit(void* Obj)
{
	((FAuracronCollisionDescriptor*)Obj)->bIsEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_bIsEnabled = { "bIsEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionDescriptor), &Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_bIsEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEnabled_MetaData), NewProp_bIsEnabled_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((FAuracronCollisionDescriptor*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionDescriptor), &Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionDescriptor, LastAccessTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_SourceActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionComplexity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CollisionComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_StreamingState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_StreamingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CurrentLODLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CurrentLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_Bounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_TriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_ConvexCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_bIsEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewProp_LastAccessTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronCollisionDescriptor",
	Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::PropPointers),
	sizeof(FAuracronCollisionDescriptor),
	alignof(FAuracronCollisionDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCollisionDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCollisionDescriptor ****************************************

// ********** Begin ScriptStruct FAuracronCollisionQueryParameters *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCollisionQueryParameters;
class UScriptStruct* FAuracronCollisionQueryParameters::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionQueryParameters.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCollisionQueryParameters.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronCollisionQueryParameters"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionQueryParameters.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Collision Query Parameters\n * Parameters for spatial collision queries\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision Query Parameters\nParameters for spatial collision queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryLocation_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryExtent_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryRotation_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterTypes_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeDisabled_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeInvisible_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxResults_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryExtent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryRotation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FilterTypes_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FilterTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FilterTypes;
	static void NewProp_bIncludeDisabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeDisabled;
	static void NewProp_bIncludeInvisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeInvisible;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxResults;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCollisionQueryParameters>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_QueryLocation = { "QueryLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionQueryParameters, QueryLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryLocation_MetaData), NewProp_QueryLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_QueryExtent = { "QueryExtent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionQueryParameters, QueryExtent), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryExtent_MetaData), NewProp_QueryExtent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_QueryRotation = { "QueryRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionQueryParameters, QueryRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryRotation_MetaData), NewProp_QueryRotation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_FilterTypes_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_FilterTypes_Inner = { "FilterTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType, METADATA_PARAMS(0, nullptr) }; // 3676296331
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_FilterTypes = { "FilterTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionQueryParameters, FilterTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterTypes_MetaData), NewProp_FilterTypes_MetaData) }; // 3676296331
void Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_bIncludeDisabled_SetBit(void* Obj)
{
	((FAuracronCollisionQueryParameters*)Obj)->bIncludeDisabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_bIncludeDisabled = { "bIncludeDisabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionQueryParameters), &Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_bIncludeDisabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeDisabled_MetaData), NewProp_bIncludeDisabled_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_bIncludeInvisible_SetBit(void* Obj)
{
	((FAuracronCollisionQueryParameters*)Obj)->bIncludeInvisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_bIncludeInvisible = { "bIncludeInvisible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionQueryParameters), &Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_bIncludeInvisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeInvisible_MetaData), NewProp_bIncludeInvisible_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_MaxResults = { "MaxResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionQueryParameters, MaxResults), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxResults_MetaData), NewProp_MaxResults_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_QueryLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_QueryExtent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_QueryRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_FilterTypes_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_FilterTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_FilterTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_bIncludeDisabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_bIncludeInvisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewProp_MaxResults,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronCollisionQueryParameters",
	Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::PropPointers),
	sizeof(FAuracronCollisionQueryParameters),
	alignof(FAuracronCollisionQueryParameters),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionQueryParameters.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCollisionQueryParameters.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionQueryParameters.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCollisionQueryParameters ***********************************

// ********** Begin ScriptStruct FAuracronCollisionStatistics **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCollisionStatistics;
class UScriptStruct* FAuracronCollisionStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCollisionStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCollisionStatistics, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronCollisionStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Collision Statistics\n * Performance and usage statistics for collision system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision Statistics\nPerformance and usage statistics for collision system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalCollisionObjects_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedCollisionObjects_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingCollisionObjects_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsageMB_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadingTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionQueries_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageQueryTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODTransitions_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedOperations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionEfficiency_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalCollisionObjects;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedCollisionObjects;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingCollisionObjects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadingTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CollisionQueries;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageQueryTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODTransitions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionEfficiency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCollisionStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_TotalCollisionObjects = { "TotalCollisionObjects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, TotalCollisionObjects), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalCollisionObjects_MetaData), NewProp_TotalCollisionObjects_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_LoadedCollisionObjects = { "LoadedCollisionObjects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, LoadedCollisionObjects), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedCollisionObjects_MetaData), NewProp_LoadedCollisionObjects_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_StreamingCollisionObjects = { "StreamingCollisionObjects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, StreamingCollisionObjects), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingCollisionObjects_MetaData), NewProp_StreamingCollisionObjects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_TotalMemoryUsageMB = { "TotalMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, TotalMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsageMB_MetaData), NewProp_TotalMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_AverageLoadingTime = { "AverageLoadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, AverageLoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadingTime_MetaData), NewProp_AverageLoadingTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_CollisionQueries = { "CollisionQueries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, CollisionQueries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionQueries_MetaData), NewProp_CollisionQueries_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_AverageQueryTime = { "AverageQueryTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, AverageQueryTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageQueryTime_MetaData), NewProp_AverageQueryTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_LODTransitions = { "LODTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, LODTransitions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODTransitions_MetaData), NewProp_LODTransitions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_FailedOperations = { "FailedOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, FailedOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedOperations_MetaData), NewProp_FailedOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_CollisionEfficiency = { "CollisionEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, CollisionEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionEfficiency_MetaData), NewProp_CollisionEfficiency_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionStatistics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_TotalCollisionObjects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_LoadedCollisionObjects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_StreamingCollisionObjects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_TotalMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_AverageLoadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_CollisionQueries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_AverageQueryTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_LODTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_FailedOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_CollisionEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronCollisionStatistics",
	Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::PropPointers),
	sizeof(FAuracronCollisionStatistics),
	alignof(FAuracronCollisionStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCollisionStatistics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionStatistics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCollisionStatistics ****************************************

// ********** Begin Delegate FOnCollisionLoaded ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventOnCollisionLoaded_Parms
	{
		FString CollisionId;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventOnCollisionLoaded_Parms, CollisionId), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventOnCollisionLoaded_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventOnCollisionLoaded_Parms), &Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "OnCollisionLoaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::AuracronWorldPartitionCollisionManager_eventOnCollisionLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::AuracronWorldPartitionCollisionManager_eventOnCollisionLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionCollisionManager::FOnCollisionLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnCollisionLoaded, const FString& CollisionId, bool bSuccess)
{
	struct AuracronWorldPartitionCollisionManager_eventOnCollisionLoaded_Parms
	{
		FString CollisionId;
		bool bSuccess;
	};
	AuracronWorldPartitionCollisionManager_eventOnCollisionLoaded_Parms Parms;
	Parms.CollisionId=CollisionId;
	Parms.bSuccess=bSuccess ? true : false;
	OnCollisionLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCollisionLoaded ******************************************************

// ********** Begin Delegate FOnCollisionUnloaded **************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventOnCollisionUnloaded_Parms
	{
		FString CollisionId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventOnCollisionUnloaded_Parms, CollisionId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::NewProp_CollisionId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "OnCollisionUnloaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::AuracronWorldPartitionCollisionManager_eventOnCollisionUnloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::AuracronWorldPartitionCollisionManager_eventOnCollisionUnloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionCollisionManager::FOnCollisionUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnCollisionUnloaded, const FString& CollisionId)
{
	struct AuracronWorldPartitionCollisionManager_eventOnCollisionUnloaded_Parms
	{
		FString CollisionId;
	};
	AuracronWorldPartitionCollisionManager_eventOnCollisionUnloaded_Parms Parms;
	Parms.CollisionId=CollisionId;
	OnCollisionUnloaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCollisionUnloaded ****************************************************

// ********** Begin Delegate FOnCollisionLODChanged ************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventOnCollisionLODChanged_Parms
	{
		FString CollisionId;
		EAuracronCollisionLODLevel NewLOD;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLOD_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLOD;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventOnCollisionLODChanged_Parms, CollisionId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::NewProp_NewLOD_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::NewProp_NewLOD = { "NewLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventOnCollisionLODChanged_Parms, NewLOD), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel, METADATA_PARAMS(0, nullptr) }; // 3026529594
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::NewProp_NewLOD_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::NewProp_NewLOD,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "OnCollisionLODChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::AuracronWorldPartitionCollisionManager_eventOnCollisionLODChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::AuracronWorldPartitionCollisionManager_eventOnCollisionLODChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionCollisionManager::FOnCollisionLODChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCollisionLODChanged, const FString& CollisionId, EAuracronCollisionLODLevel NewLOD)
{
	struct AuracronWorldPartitionCollisionManager_eventOnCollisionLODChanged_Parms
	{
		FString CollisionId;
		EAuracronCollisionLODLevel NewLOD;
	};
	AuracronWorldPartitionCollisionManager_eventOnCollisionLODChanged_Parms Parms;
	Parms.CollisionId=CollisionId;
	Parms.NewLOD=NewLOD;
	OnCollisionLODChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCollisionLODChanged **************************************************

// ********** Begin Delegate FOnCollisionStateChanged **********************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventOnCollisionStateChanged_Parms
	{
		FString CollisionId;
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventOnCollisionStateChanged_Parms, CollisionId), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventOnCollisionStateChanged_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventOnCollisionStateChanged_Parms), &Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "OnCollisionStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::AuracronWorldPartitionCollisionManager_eventOnCollisionStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::AuracronWorldPartitionCollisionManager_eventOnCollisionStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionCollisionManager::FOnCollisionStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCollisionStateChanged, const FString& CollisionId, bool bEnabled)
{
	struct AuracronWorldPartitionCollisionManager_eventOnCollisionStateChanged_Parms
	{
		FString CollisionId;
		bool bEnabled;
	};
	AuracronWorldPartitionCollisionManager_eventOnCollisionStateChanged_Parms Parms;
	Parms.CollisionId=CollisionId;
	Parms.bEnabled=bEnabled ? true : false;
	OnCollisionStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCollisionStateChanged ************************************************

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function CalculateLODForDistance *
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventCalculateLODForDistance_Parms
	{
		float Distance;
		EAuracronCollisionLODLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventCalculateLODForDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventCalculateLODForDistance_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel, METADATA_PARAMS(0, nullptr) }; // 3026529594
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "CalculateLODForDistance", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::AuracronWorldPartitionCollisionManager_eventCalculateLODForDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::AuracronWorldPartitionCollisionManager_eventCalculateLODForDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execCalculateLODForDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronCollisionLODLevel*)Z_Param__Result=P_THIS->CalculateLODForDistance(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function CalculateLODForDistance ***

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function CreateCollisionObject ***
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventCreateCollisionObject_Parms
	{
		FString SourceActorId;
		EAuracronCollisionType CollisionType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision creation and management\n" },
#endif
		{ "CPP_Default_CollisionType", "Static" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision creation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceActorId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::NewProp_SourceActorId = { "SourceActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventCreateCollisionObject_Parms, SourceActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceActorId_MetaData), NewProp_SourceActorId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::NewProp_CollisionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::NewProp_CollisionType = { "CollisionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventCreateCollisionObject_Parms, CollisionType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType, METADATA_PARAMS(0, nullptr) }; // 3676296331
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventCreateCollisionObject_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::NewProp_SourceActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::NewProp_CollisionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::NewProp_CollisionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "CreateCollisionObject", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::AuracronWorldPartitionCollisionManager_eventCreateCollisionObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::AuracronWorldPartitionCollisionManager_eventCreateCollisionObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execCreateCollisionObject)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SourceActorId);
	P_GET_ENUM(EAuracronCollisionType,Z_Param_CollisionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateCollisionObject(Z_Param_SourceActorId,EAuracronCollisionType(Z_Param_CollisionType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function CreateCollisionObject *****

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function DoesCollisionObjectExist 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventDoesCollisionObjectExist_Parms
	{
		FString CollisionId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventDoesCollisionObjectExist_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventDoesCollisionObjectExist_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventDoesCollisionObjectExist_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "DoesCollisionObjectExist", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::AuracronWorldPartitionCollisionManager_eventDoesCollisionObjectExist_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::AuracronWorldPartitionCollisionManager_eventDoesCollisionObjectExist_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execDoesCollisionObjectExist)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesCollisionObjectExist(Z_Param_CollisionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function DoesCollisionObjectExist **

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function DrawDebugCollisionInfo **
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventDrawDebugCollisionInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventDrawDebugCollisionInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "DrawDebugCollisionInfo", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::AuracronWorldPartitionCollisionManager_eventDrawDebugCollisionInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::AuracronWorldPartitionCollisionManager_eventDrawDebugCollisionInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execDrawDebugCollisionInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugCollisionInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function DrawDebugCollisionInfo ****

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function EnableCollisionDebug ****
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventEnableCollisionDebug_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and utilities" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventEnableCollisionDebug_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventEnableCollisionDebug_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "EnableCollisionDebug", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::AuracronWorldPartitionCollisionManager_eventEnableCollisionDebug_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::AuracronWorldPartitionCollisionManager_eventEnableCollisionDebug_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execEnableCollisionDebug)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableCollisionDebug(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function EnableCollisionDebug ******

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function EnableCollisionObject ***
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventEnableCollisionObject_Parms
	{
		FString CollisionId;
		bool bEnabled;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision state management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision state management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventEnableCollisionObject_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventEnableCollisionObject_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventEnableCollisionObject_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventEnableCollisionObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventEnableCollisionObject_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::NewProp_bEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "EnableCollisionObject", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::AuracronWorldPartitionCollisionManager_eventEnableCollisionObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::AuracronWorldPartitionCollisionManager_eventEnableCollisionObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execEnableCollisionObject)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EnableCollisionObject(Z_Param_CollisionId,Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function EnableCollisionObject *****

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function ExecuteCollisionQuery ***
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventExecuteCollisionQuery_Parms
	{
		FAuracronCollisionQueryParameters QueryParams;
		TArray<FAuracronCollisionDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryParams_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryParams;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::NewProp_QueryParams = { "QueryParams", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventExecuteCollisionQuery_Parms, QueryParams), Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryParams_MetaData), NewProp_QueryParams_MetaData) }; // 3352131011
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCollisionDescriptor, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventExecuteCollisionQuery_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::NewProp_QueryParams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "ExecuteCollisionQuery", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::AuracronWorldPartitionCollisionManager_eventExecuteCollisionQuery_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::AuracronWorldPartitionCollisionManager_eventExecuteCollisionQuery_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execExecuteCollisionQuery)
{
	P_GET_STRUCT_REF(FAuracronCollisionQueryParameters,Z_Param_Out_QueryParams);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronCollisionDescriptor>*)Z_Param__Result=P_THIS->ExecuteCollisionQuery(Z_Param_Out_QueryParams);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function ExecuteCollisionQuery *****

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetAllCollisionObjects **
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetAllCollisionObjects_Parms
	{
		TArray<FAuracronCollisionDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCollisionDescriptor, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetAllCollisionObjects_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetAllCollisionObjects", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::AuracronWorldPartitionCollisionManager_eventGetAllCollisionObjects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::AuracronWorldPartitionCollisionManager_eventGetAllCollisionObjects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetAllCollisionObjects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronCollisionDescriptor>*)Z_Param__Result=P_THIS->GetAllCollisionObjects();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetAllCollisionObjects ****

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetCollisionDescriptor **
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetCollisionDescriptor_Parms
	{
		FString CollisionId;
		FAuracronCollisionDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionDescriptor_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronCollisionDescriptor, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetCollisionDescriptor", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetCollisionDescriptor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronCollisionDescriptor*)Z_Param__Result=P_THIS->GetCollisionDescriptor(Z_Param_CollisionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetCollisionDescriptor ****

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetCollisionIds *********
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetCollisionIds_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionIds_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetCollisionIds", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionIds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionIds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetCollisionIds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetCollisionIds();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetCollisionIds ***********

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetCollisionLOD *********
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetCollisionLOD_Parms
	{
		FString CollisionId;
		EAuracronCollisionLODLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionLOD_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionLOD_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel, METADATA_PARAMS(0, nullptr) }; // 3026529594
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetCollisionLOD", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetCollisionLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronCollisionLODLevel*)Z_Param__Result=P_THIS->GetCollisionLOD(Z_Param_CollisionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetCollisionLOD ***********

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetCollisionObjectCell **
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetCollisionObjectCell_Parms
	{
		FString CollisionId;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectCell_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectCell_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetCollisionObjectCell", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionObjectCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionObjectCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetCollisionObjectCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetCollisionObjectCell(Z_Param_CollisionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetCollisionObjectCell ****

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetCollisionObjectsByType 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsByType_Parms
	{
		EAuracronCollisionType CollisionType;
		TArray<FAuracronCollisionDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::NewProp_CollisionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::NewProp_CollisionType = { "CollisionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsByType_Parms, CollisionType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionType, METADATA_PARAMS(0, nullptr) }; // 3676296331
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCollisionDescriptor, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::NewProp_CollisionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::NewProp_CollisionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetCollisionObjectsByType", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetCollisionObjectsByType)
{
	P_GET_ENUM(EAuracronCollisionType,Z_Param_CollisionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronCollisionDescriptor>*)Z_Param__Result=P_THIS->GetCollisionObjectsByType(EAuracronCollisionType(Z_Param_CollisionType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetCollisionObjectsByType *

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetCollisionObjectsInBox 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInBox_Parms
	{
		FBox Box;
		TArray<FAuracronCollisionDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Box_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Box;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::NewProp_Box = { "Box", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInBox_Parms, Box), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Box_MetaData), NewProp_Box_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCollisionDescriptor, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInBox_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::NewProp_Box,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetCollisionObjectsInBox", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInBox_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInBox_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetCollisionObjectsInBox)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Box);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronCollisionDescriptor>*)Z_Param__Result=P_THIS->GetCollisionObjectsInBox(Z_Param_Out_Box);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetCollisionObjectsInBox **

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetCollisionObjectsInCell 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInCell_Parms
	{
		FString CellId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInCell_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetCollisionObjectsInCell", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetCollisionObjectsInCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetCollisionObjectsInCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetCollisionObjectsInCell *

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetCollisionObjectsInRadius 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInRadius_Parms
	{
		FVector Location;
		float Radius;
		TArray<FAuracronCollisionDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCollisionDescriptor, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 780928640
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetCollisionObjectsInRadius", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionObjectsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetCollisionObjectsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronCollisionDescriptor>*)Z_Param__Result=P_THIS->GetCollisionObjectsInRadius(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetCollisionObjectsInRadius 

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetCollisionStatistics **
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetCollisionStatistics_Parms
	{
		FAuracronCollisionStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronCollisionStatistics, METADATA_PARAMS(0, nullptr) }; // 2109608552
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetCollisionStatistics", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetCollisionStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronCollisionStatistics*)Z_Param__Result=P_THIS->GetCollisionStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetCollisionStatistics ****

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetCollisionStreamingState 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetCollisionStreamingState_Parms
	{
		FString CollisionId;
		EAuracronCollisionStreamingState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionStreamingState_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetCollisionStreamingState_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionStreamingState, METADATA_PARAMS(0, nullptr) }; // 889311792
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetCollisionStreamingState", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionStreamingState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::AuracronWorldPartitionCollisionManager_eventGetCollisionStreamingState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetCollisionStreamingState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronCollisionStreamingState*)Z_Param__Result=P_THIS->GetCollisionStreamingState(Z_Param_CollisionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetCollisionStreamingState 

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetConfiguration ********
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetConfiguration_Parms
	{
		FAuracronCollisionConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronCollisionConfiguration, METADATA_PARAMS(0, nullptr) }; // 2665838714
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::AuracronWorldPartitionCollisionManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::AuracronWorldPartitionCollisionManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronCollisionConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetConfiguration **********

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetInstance *************
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionCollisionManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::AuracronWorldPartitionCollisionManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::AuracronWorldPartitionCollisionManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionCollisionManager**)Z_Param__Result=UAuracronWorldPartitionCollisionManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetInstance ***************

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetLoadedCollisionObjectCount 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetLoadedCollisionObjectCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetLoadedCollisionObjectCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetLoadedCollisionObjectCount", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::AuracronWorldPartitionCollisionManager_eventGetLoadedCollisionObjectCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::AuracronWorldPartitionCollisionManager_eventGetLoadedCollisionObjectCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetLoadedCollisionObjectCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetLoadedCollisionObjectCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetLoadedCollisionObjectCount 

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetLoadedCollisionObjects 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetLoadedCollisionObjects_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetLoadedCollisionObjects_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetLoadedCollisionObjects", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::AuracronWorldPartitionCollisionManager_eventGetLoadedCollisionObjects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::AuracronWorldPartitionCollisionManager_eventGetLoadedCollisionObjects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetLoadedCollisionObjects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLoadedCollisionObjects();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetLoadedCollisionObjects *

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetStreamingCollisionObjects 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetStreamingCollisionObjects_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetStreamingCollisionObjects_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetStreamingCollisionObjects", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::AuracronWorldPartitionCollisionManager_eventGetStreamingCollisionObjects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::AuracronWorldPartitionCollisionManager_eventGetStreamingCollisionObjects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetStreamingCollisionObjects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetStreamingCollisionObjects();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetStreamingCollisionObjects 

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetTotalCollisionObjectCount 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetTotalCollisionObjectCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetTotalCollisionObjectCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetTotalCollisionObjectCount", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::AuracronWorldPartitionCollisionManager_eventGetTotalCollisionObjectCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::AuracronWorldPartitionCollisionManager_eventGetTotalCollisionObjectCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetTotalCollisionObjectCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalCollisionObjectCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetTotalCollisionObjectCount 

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function GetTotalMemoryUsage *****
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventGetTotalMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventGetTotalMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "GetTotalMemoryUsage", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionCollisionManager_eventGetTotalMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionCollisionManager_eventGetTotalMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execGetTotalMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function GetTotalMemoryUsage *******

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function Initialize **************
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventInitialize_Parms
	{
		FAuracronCollisionConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronCollisionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2665838714
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::AuracronWorldPartitionCollisionManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::AuracronWorldPartitionCollisionManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronCollisionConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function Initialize ****************

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function IsCollisionDebugEnabled *
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventIsCollisionDebugEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventIsCollisionDebugEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventIsCollisionDebugEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "IsCollisionDebugEnabled", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::AuracronWorldPartitionCollisionManager_eventIsCollisionDebugEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::AuracronWorldPartitionCollisionManager_eventIsCollisionDebugEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execIsCollisionDebugEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCollisionDebugEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function IsCollisionDebugEnabled ***

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function IsCollisionObjectEnabled 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventIsCollisionObjectEnabled_Parms
	{
		FString CollisionId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventIsCollisionObjectEnabled_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventIsCollisionObjectEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventIsCollisionObjectEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "IsCollisionObjectEnabled", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::AuracronWorldPartitionCollisionManager_eventIsCollisionObjectEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::AuracronWorldPartitionCollisionManager_eventIsCollisionObjectEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execIsCollisionObjectEnabled)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCollisionObjectEnabled(Z_Param_CollisionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function IsCollisionObjectEnabled **

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function IsCollisionObjectVisible 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventIsCollisionObjectVisible_Parms
	{
		FString CollisionId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventIsCollisionObjectVisible_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventIsCollisionObjectVisible_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventIsCollisionObjectVisible_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "IsCollisionObjectVisible", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::AuracronWorldPartitionCollisionManager_eventIsCollisionObjectVisible_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::AuracronWorldPartitionCollisionManager_eventIsCollisionObjectVisible_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execIsCollisionObjectVisible)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCollisionObjectVisible(Z_Param_CollisionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function IsCollisionObjectVisible **

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function IsInitialized ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::AuracronWorldPartitionCollisionManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::AuracronWorldPartitionCollisionManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function IsInitialized *************

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function LoadCollisionObject *****
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventLoadCollisionObject_Parms
	{
		FString CollisionId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision streaming\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventLoadCollisionObject_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventLoadCollisionObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventLoadCollisionObject_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "LoadCollisionObject", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::AuracronWorldPartitionCollisionManager_eventLoadCollisionObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::AuracronWorldPartitionCollisionManager_eventLoadCollisionObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execLoadCollisionObject)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadCollisionObject(Z_Param_CollisionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function LoadCollisionObject *******

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function LogCollisionState *******
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LogCollisionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LogCollisionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "LogCollisionState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LogCollisionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LogCollisionState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LogCollisionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LogCollisionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execLogCollisionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogCollisionState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function LogCollisionState *********

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function MoveCollisionObjectToCell 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventMoveCollisionObjectToCell_Parms
	{
		FString CollisionId;
		FString CellId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventMoveCollisionObjectToCell_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventMoveCollisionObjectToCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventMoveCollisionObjectToCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventMoveCollisionObjectToCell_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "MoveCollisionObjectToCell", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::AuracronWorldPartitionCollisionManager_eventMoveCollisionObjectToCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::AuracronWorldPartitionCollisionManager_eventMoveCollisionObjectToCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execMoveCollisionObjectToCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MoveCollisionObjectToCell(Z_Param_CollisionId,Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function MoveCollisionObjectToCell *

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function RemoveCollisionObject ***
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventRemoveCollisionObject_Parms
	{
		FString CollisionId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventRemoveCollisionObject_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventRemoveCollisionObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventRemoveCollisionObject_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "RemoveCollisionObject", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::AuracronWorldPartitionCollisionManager_eventRemoveCollisionObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::AuracronWorldPartitionCollisionManager_eventRemoveCollisionObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execRemoveCollisionObject)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveCollisionObject(Z_Param_CollisionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function RemoveCollisionObject *****

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function ResetStatistics *********
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function ResetStatistics ***********

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function SetCollisionLOD *********
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventSetCollisionLOD_Parms
	{
		FString CollisionId;
		EAuracronCollisionLODLevel LODLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision LOD\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision LOD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LODLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LODLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventSetCollisionLOD_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::NewProp_LODLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventSetCollisionLOD_Parms, LODLevel), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCollisionLODLevel, METADATA_PARAMS(0, nullptr) }; // 3026529594
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventSetCollisionLOD_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventSetCollisionLOD_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::NewProp_LODLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "SetCollisionLOD", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::AuracronWorldPartitionCollisionManager_eventSetCollisionLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::AuracronWorldPartitionCollisionManager_eventSetCollisionLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execSetCollisionLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_GET_ENUM(EAuracronCollisionLODLevel,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetCollisionLOD(Z_Param_CollisionId,EAuracronCollisionLODLevel(Z_Param_LODLevel));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function SetCollisionLOD ***********

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function SetCollisionObjectVisibility 
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventSetCollisionObjectVisibility_Parms
	{
		FString CollisionId;
		bool bVisible;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventSetCollisionObjectVisibility_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventSetCollisionObjectVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventSetCollisionObjectVisibility_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventSetCollisionObjectVisibility_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventSetCollisionObjectVisibility_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::NewProp_bVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "SetCollisionObjectVisibility", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::AuracronWorldPartitionCollisionManager_eventSetCollisionObjectVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::AuracronWorldPartitionCollisionManager_eventSetCollisionObjectVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execSetCollisionObjectVisibility)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetCollisionObjectVisibility(Z_Param_CollisionId,Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function SetCollisionObjectVisibility 

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function SetConfiguration ********
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventSetConfiguration_Parms
	{
		FAuracronCollisionConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronCollisionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2665838714
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::AuracronWorldPartitionCollisionManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::AuracronWorldPartitionCollisionManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronCollisionConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function SetConfiguration **********

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function Shutdown ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function Shutdown ******************

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function Tick ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::AuracronWorldPartitionCollisionManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::AuracronWorldPartitionCollisionManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function Tick **********************

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function UnloadCollisionObject ***
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventUnloadCollisionObject_Parms
	{
		FString CollisionId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::NewProp_CollisionId = { "CollisionId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventUnloadCollisionObject_Parms, CollisionId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionId_MetaData), NewProp_CollisionId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionCollisionManager_eventUnloadCollisionObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionCollisionManager_eventUnloadCollisionObject_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::NewProp_CollisionId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "UnloadCollisionObject", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::AuracronWorldPartitionCollisionManager_eventUnloadCollisionObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::AuracronWorldPartitionCollisionManager_eventUnloadCollisionObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execUnloadCollisionObject)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnloadCollisionObject(Z_Param_CollisionId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function UnloadCollisionObject *****

// ********** Begin Class UAuracronWorldPartitionCollisionManager Function UpdateDistanceBasedLODs *
struct Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics
{
	struct AuracronWorldPartitionCollisionManager_eventUpdateDistanceBasedLODs_Parms
	{
		FVector ViewerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionCollisionManager_eventUpdateDistanceBasedLODs_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::NewProp_ViewerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, nullptr, "UpdateDistanceBasedLODs", Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::AuracronWorldPartitionCollisionManager_eventUpdateDistanceBasedLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::AuracronWorldPartitionCollisionManager_eventUpdateDistanceBasedLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionCollisionManager::execUpdateDistanceBasedLODs)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDistanceBasedLODs(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionCollisionManager Function UpdateDistanceBasedLODs ***

// ********** Begin Class UAuracronWorldPartitionCollisionManager **********************************
void UAuracronWorldPartitionCollisionManager::StaticRegisterNativesUAuracronWorldPartitionCollisionManager()
{
	UClass* Class = UAuracronWorldPartitionCollisionManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateLODForDistance", &UAuracronWorldPartitionCollisionManager::execCalculateLODForDistance },
		{ "CreateCollisionObject", &UAuracronWorldPartitionCollisionManager::execCreateCollisionObject },
		{ "DoesCollisionObjectExist", &UAuracronWorldPartitionCollisionManager::execDoesCollisionObjectExist },
		{ "DrawDebugCollisionInfo", &UAuracronWorldPartitionCollisionManager::execDrawDebugCollisionInfo },
		{ "EnableCollisionDebug", &UAuracronWorldPartitionCollisionManager::execEnableCollisionDebug },
		{ "EnableCollisionObject", &UAuracronWorldPartitionCollisionManager::execEnableCollisionObject },
		{ "ExecuteCollisionQuery", &UAuracronWorldPartitionCollisionManager::execExecuteCollisionQuery },
		{ "GetAllCollisionObjects", &UAuracronWorldPartitionCollisionManager::execGetAllCollisionObjects },
		{ "GetCollisionDescriptor", &UAuracronWorldPartitionCollisionManager::execGetCollisionDescriptor },
		{ "GetCollisionIds", &UAuracronWorldPartitionCollisionManager::execGetCollisionIds },
		{ "GetCollisionLOD", &UAuracronWorldPartitionCollisionManager::execGetCollisionLOD },
		{ "GetCollisionObjectCell", &UAuracronWorldPartitionCollisionManager::execGetCollisionObjectCell },
		{ "GetCollisionObjectsByType", &UAuracronWorldPartitionCollisionManager::execGetCollisionObjectsByType },
		{ "GetCollisionObjectsInBox", &UAuracronWorldPartitionCollisionManager::execGetCollisionObjectsInBox },
		{ "GetCollisionObjectsInCell", &UAuracronWorldPartitionCollisionManager::execGetCollisionObjectsInCell },
		{ "GetCollisionObjectsInRadius", &UAuracronWorldPartitionCollisionManager::execGetCollisionObjectsInRadius },
		{ "GetCollisionStatistics", &UAuracronWorldPartitionCollisionManager::execGetCollisionStatistics },
		{ "GetCollisionStreamingState", &UAuracronWorldPartitionCollisionManager::execGetCollisionStreamingState },
		{ "GetConfiguration", &UAuracronWorldPartitionCollisionManager::execGetConfiguration },
		{ "GetInstance", &UAuracronWorldPartitionCollisionManager::execGetInstance },
		{ "GetLoadedCollisionObjectCount", &UAuracronWorldPartitionCollisionManager::execGetLoadedCollisionObjectCount },
		{ "GetLoadedCollisionObjects", &UAuracronWorldPartitionCollisionManager::execGetLoadedCollisionObjects },
		{ "GetStreamingCollisionObjects", &UAuracronWorldPartitionCollisionManager::execGetStreamingCollisionObjects },
		{ "GetTotalCollisionObjectCount", &UAuracronWorldPartitionCollisionManager::execGetTotalCollisionObjectCount },
		{ "GetTotalMemoryUsage", &UAuracronWorldPartitionCollisionManager::execGetTotalMemoryUsage },
		{ "Initialize", &UAuracronWorldPartitionCollisionManager::execInitialize },
		{ "IsCollisionDebugEnabled", &UAuracronWorldPartitionCollisionManager::execIsCollisionDebugEnabled },
		{ "IsCollisionObjectEnabled", &UAuracronWorldPartitionCollisionManager::execIsCollisionObjectEnabled },
		{ "IsCollisionObjectVisible", &UAuracronWorldPartitionCollisionManager::execIsCollisionObjectVisible },
		{ "IsInitialized", &UAuracronWorldPartitionCollisionManager::execIsInitialized },
		{ "LoadCollisionObject", &UAuracronWorldPartitionCollisionManager::execLoadCollisionObject },
		{ "LogCollisionState", &UAuracronWorldPartitionCollisionManager::execLogCollisionState },
		{ "MoveCollisionObjectToCell", &UAuracronWorldPartitionCollisionManager::execMoveCollisionObjectToCell },
		{ "RemoveCollisionObject", &UAuracronWorldPartitionCollisionManager::execRemoveCollisionObject },
		{ "ResetStatistics", &UAuracronWorldPartitionCollisionManager::execResetStatistics },
		{ "SetCollisionLOD", &UAuracronWorldPartitionCollisionManager::execSetCollisionLOD },
		{ "SetCollisionObjectVisibility", &UAuracronWorldPartitionCollisionManager::execSetCollisionObjectVisibility },
		{ "SetConfiguration", &UAuracronWorldPartitionCollisionManager::execSetConfiguration },
		{ "Shutdown", &UAuracronWorldPartitionCollisionManager::execShutdown },
		{ "Tick", &UAuracronWorldPartitionCollisionManager::execTick },
		{ "UnloadCollisionObject", &UAuracronWorldPartitionCollisionManager::execUnloadCollisionObject },
		{ "UpdateDistanceBasedLODs", &UAuracronWorldPartitionCollisionManager::execUpdateDistanceBasedLODs },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionCollisionManager;
UClass* UAuracronWorldPartitionCollisionManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionCollisionManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionCollisionManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionCollisionManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionCollisionManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionCollisionManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionCollisionManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_NoRegister()
{
	return UAuracronWorldPartitionCollisionManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Collision Manager\n * Central manager for collision streaming in world partition\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionCollision.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Collision Manager\nCentral manager for collision streaming in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCollisionLoaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCollisionUnloaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCollisionLODChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCollisionStateChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCollisionLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCollisionUnloaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCollisionLODChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCollisionStateChanged;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CalculateLODForDistance, "CalculateLODForDistance" }, // 271427797
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_CreateCollisionObject, "CreateCollisionObject" }, // 3405488638
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DoesCollisionObjectExist, "DoesCollisionObjectExist" }, // 1221866560
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_DrawDebugCollisionInfo, "DrawDebugCollisionInfo" }, // 2022616319
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionDebug, "EnableCollisionDebug" }, // 3144137861
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_EnableCollisionObject, "EnableCollisionObject" }, // 3753487797
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ExecuteCollisionQuery, "ExecuteCollisionQuery" }, // 1030609328
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetAllCollisionObjects, "GetAllCollisionObjects" }, // 2938792299
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionDescriptor, "GetCollisionDescriptor" }, // 1997421661
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionIds, "GetCollisionIds" }, // 2463223481
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionLOD, "GetCollisionLOD" }, // 3417018983
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectCell, "GetCollisionObjectCell" }, // 4088649477
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsByType, "GetCollisionObjectsByType" }, // 4198575290
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInBox, "GetCollisionObjectsInBox" }, // 510890249
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInCell, "GetCollisionObjectsInCell" }, // 1886670114
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionObjectsInRadius, "GetCollisionObjectsInRadius" }, // 1999729504
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStatistics, "GetCollisionStatistics" }, // 511058678
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetCollisionStreamingState, "GetCollisionStreamingState" }, // 3212495838
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetConfiguration, "GetConfiguration" }, // 1700716838
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetInstance, "GetInstance" }, // 3347390269
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjectCount, "GetLoadedCollisionObjectCount" }, // 3844306187
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetLoadedCollisionObjects, "GetLoadedCollisionObjects" }, // 3316599733
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetStreamingCollisionObjects, "GetStreamingCollisionObjects" }, // 2549565589
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalCollisionObjectCount, "GetTotalCollisionObjectCount" }, // 3027911217
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_GetTotalMemoryUsage, "GetTotalMemoryUsage" }, // 2598186194
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Initialize, "Initialize" }, // 2655630792
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionDebugEnabled, "IsCollisionDebugEnabled" }, // 1040249331
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectEnabled, "IsCollisionObjectEnabled" }, // 3373012233
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsCollisionObjectVisible, "IsCollisionObjectVisible" }, // 3135812139
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_IsInitialized, "IsInitialized" }, // 2063484625
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LoadCollisionObject, "LoadCollisionObject" }, // 1175836281
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_LogCollisionState, "LogCollisionState" }, // 408881213
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_MoveCollisionObjectToCell, "MoveCollisionObjectToCell" }, // 717845609
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature, "OnCollisionLoaded__DelegateSignature" }, // 2991784505
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature, "OnCollisionLODChanged__DelegateSignature" }, // 2291165546
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature, "OnCollisionStateChanged__DelegateSignature" }, // 1682379222
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature, "OnCollisionUnloaded__DelegateSignature" }, // 2179662558
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_RemoveCollisionObject, "RemoveCollisionObject" }, // 4100316181
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_ResetStatistics, "ResetStatistics" }, // 884053269
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionLOD, "SetCollisionLOD" }, // 2854985884
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetCollisionObjectVisibility, "SetCollisionObjectVisibility" }, // 3421375560
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_SetConfiguration, "SetConfiguration" }, // 1871075211
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Shutdown, "Shutdown" }, // 1953594131
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_Tick, "Tick" }, // 1713086480
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UnloadCollisionObject, "UnloadCollisionObject" }, // 1299604991
		{ &Z_Construct_UFunction_UAuracronWorldPartitionCollisionManager_UpdateDistanceBasedLODs, "UpdateDistanceBasedLODs" }, // 485362358
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionCollisionManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_OnCollisionLoaded = { "OnCollisionLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionCollisionManager, OnCollisionLoaded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCollisionLoaded_MetaData), NewProp_OnCollisionLoaded_MetaData) }; // 2991784505
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_OnCollisionUnloaded = { "OnCollisionUnloaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionCollisionManager, OnCollisionUnloaded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCollisionUnloaded_MetaData), NewProp_OnCollisionUnloaded_MetaData) }; // 2179662558
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_OnCollisionLODChanged = { "OnCollisionLODChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionCollisionManager, OnCollisionLODChanged), Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCollisionLODChanged_MetaData), NewProp_OnCollisionLODChanged_MetaData) }; // 2291165546
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_OnCollisionStateChanged = { "OnCollisionStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionCollisionManager, OnCollisionStateChanged), Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCollisionStateChanged_MetaData), NewProp_OnCollisionStateChanged_MetaData) }; // 1682379222
void Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionCollisionManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionCollisionManager), &Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionCollisionManager, Configuration), Z_Construct_UScriptStruct_FAuracronCollisionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2665838714
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionCollisionManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_OnCollisionLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_OnCollisionUnloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_OnCollisionLODChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_OnCollisionStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::ClassParams = {
	&UAuracronWorldPartitionCollisionManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionCollisionManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionCollisionManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionCollisionManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionCollisionManager.OuterSingleton;
}
UAuracronWorldPartitionCollisionManager::UAuracronWorldPartitionCollisionManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionCollisionManager);
UAuracronWorldPartitionCollisionManager::~UAuracronWorldPartitionCollisionManager() {}
// ********** End Class UAuracronWorldPartitionCollisionManager ************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronCollisionStreamingState_StaticEnum, TEXT("EAuracronCollisionStreamingState"), &Z_Registration_Info_UEnum_EAuracronCollisionStreamingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 889311792U) },
		{ EAuracronCollisionLODLevel_StaticEnum, TEXT("EAuracronCollisionLODLevel"), &Z_Registration_Info_UEnum_EAuracronCollisionLODLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3026529594U) },
		{ EAuracronCollisionType_StaticEnum, TEXT("EAuracronCollisionType"), &Z_Registration_Info_UEnum_EAuracronCollisionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3676296331U) },
		{ EAuracronCollisionComplexity_StaticEnum, TEXT("EAuracronCollisionComplexity"), &Z_Registration_Info_UEnum_EAuracronCollisionComplexity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2638560628U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronCollisionConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics::NewStructOps, TEXT("AuracronCollisionConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronCollisionConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCollisionConfiguration), 2665838714U) },
		{ FAuracronCollisionDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics::NewStructOps, TEXT("AuracronCollisionDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronCollisionDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCollisionDescriptor), 780928640U) },
		{ FAuracronCollisionQueryParameters::StaticStruct, Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics::NewStructOps, TEXT("AuracronCollisionQueryParameters"), &Z_Registration_Info_UScriptStruct_FAuracronCollisionQueryParameters, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCollisionQueryParameters), 3352131011U) },
		{ FAuracronCollisionStatistics::StaticStruct, Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics::NewStructOps, TEXT("AuracronCollisionStatistics"), &Z_Registration_Info_UScriptStruct_FAuracronCollisionStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCollisionStatistics), 2109608552U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionCollisionManager, UAuracronWorldPartitionCollisionManager::StaticClass, TEXT("UAuracronWorldPartitionCollisionManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionCollisionManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionCollisionManager), 1801710639U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h__Script_AuracronWorldPartitionBridge_3772996409(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
