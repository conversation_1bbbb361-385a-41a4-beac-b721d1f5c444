// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Realms Dinâmicos Bridge Implementation (Parte 2)

#include "AuracronRealmsBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "PCG/Public/PCGComponent.h"
#include "PCG/Public/PCGSubsystem.h"
#include "PCG/Public/PCGGraph.h"
#include "PCG/Public/PCGManagedResource.h"
#include "NavigationSystem.h"

// === Map Evolution (Continuação) ===

bool UAuracronRealmsBridge::StopMapEvolution()
{
    if (CurrentTransitionState != EAuracronRealmTransitionState::Evolving)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhuma evolução em andamento"));
        return false;
    }

    CurrentTransitionState = EAuracronRealmTransitionState::Stable;
    CurrentEvolutionIndex = -1;
    EvolutionStartTime = 0.0f;

    // Parar timer de evolução
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(EvolutionTimer);
    }

    // Parar timeline
    if (EvolutionTimeline)
    {
        EvolutionTimeline->Stop();
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Evolução do mapa interrompida"));
    return true;
}

float UAuracronRealmsBridge::GetEvolutionProgress() const
{
    if (CurrentTransitionState != EAuracronRealmTransitionState::Evolving || CurrentEvolutionIndex == -1)
    {
        return 0.0f;
    }

    if (!ScheduledEvolutions.IsValidIndex(CurrentEvolutionIndex))
    {
        return 0.0f;
    }

    const FAuracronMapEvolution& Evolution = ScheduledEvolutions[CurrentEvolutionIndex];
    float GameTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float ElapsedTime = GameTime - EvolutionStartTime;

    return FMath::Clamp(ElapsedTime / Evolution.Duration, 0.0f, 1.0f);
}

bool UAuracronRealmsBridge::IsEvolutionActive() const
{
    return CurrentTransitionState == EAuracronRealmTransitionState::Evolving;
}

// === World Partition Integration ===

bool UAuracronRealmsBridge::LoadRealmDataLayers(EAuracronRealmType RealmType)
{
    if (!bSystemInitialized || !DataLayerSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou DataLayerSubsystem inválido"));
        return false;
    }

    const FAuracronRealmConfiguration* Config = RealmConfigurations.Find(RealmType);
    if (!Config)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração do Realm %d não encontrada"), (int32)RealmType);
        return false;
    }

    bool bSuccess = true;
    for (const TSoftObjectPtr<UDataLayerInstance>& DataLayerPtr : Config->DataLayers)
    {
        if (DataLayerPtr.IsValid())
        {
            UDataLayerInstance* DataLayer = DataLayerPtr.LoadSynchronous();
            if (DataLayer)
            {
                DataLayerSubsystem->SetDataLayerRuntimeState(DataLayer, EDataLayerRuntimeState::Activated);
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Data Layer carregada: %s"), *DataLayer->GetDataLayerShortName());
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao carregar Data Layer"));
                bSuccess = false;
            }
        }
    }

    return bSuccess;
}

bool UAuracronRealmsBridge::UnloadRealmDataLayers(EAuracronRealmType RealmType)
{
    if (!bSystemInitialized || !DataLayerSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou DataLayerSubsystem inválido"));
        return false;
    }

    const FAuracronRealmConfiguration* Config = RealmConfigurations.Find(RealmType);
    if (!Config)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração do Realm %d não encontrada"), (int32)RealmType);
        return false;
    }

    bool bSuccess = true;
    for (const TSoftObjectPtr<UDataLayerInstance>& DataLayerPtr : Config->DataLayers)
    {
        if (DataLayerPtr.IsValid())
        {
            UDataLayerInstance* DataLayer = DataLayerPtr.LoadSynchronous();
            if (DataLayer)
            {
                DataLayerSubsystem->SetDataLayerRuntimeState(DataLayer, EDataLayerRuntimeState::Unloaded);
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Data Layer descarregada: %s"), *DataLayer->GetDataLayerShortName());
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao descarregar Data Layer"));
                bSuccess = false;
            }
        }
    }

    return bSuccess;
}

bool UAuracronRealmsBridge::OptimizeWorldPartitionStreaming()
{
    if (!bSystemInitialized || !WorldPartitionSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou WorldPartitionSubsystem inválido"));
        return false;
    }

    // Otimizar streaming baseado nos Realms ativos
    for (EAuracronRealmType RealmType : LoadedRealms)
    {
        const FAuracronRealmConfiguration* Config = RealmConfigurations.Find(RealmType);
        if (Config)
        {
            // Implementar lógica de otimização específica
            // Por exemplo, ajustar distâncias de streaming baseadas na altura do Realm
            float StreamingDistance = FMath::Abs(Config->MaxHeight - Config->MinHeight) * 2.0f;
            
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Otimizando streaming para Realm %d com distância %f"), 
                (int32)RealmType, StreamingDistance);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Streaming de World Partition otimizado"));
    return true;
}

// === Configuration Management ===

FAuracronRealmConfiguration UAuracronRealmsBridge::GetRealmConfiguration(EAuracronRealmType RealmType) const
{
    const FAuracronRealmConfiguration* Config = RealmConfigurations.Find(RealmType);
    if (Config)
    {
        return *Config;
    }

    return FAuracronRealmConfiguration();
}

void UAuracronRealmsBridge::SetRealmConfiguration(EAuracronRealmType RealmType, const FAuracronRealmConfiguration& Configuration)
{
    if (ValidateRealmConfiguration(Configuration))
    {
        RealmConfigurations.Add(RealmType, Configuration);
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Configuração do Realm %d atualizada"), (int32)RealmType);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração inválida para Realm %d"), (int32)RealmType);
    }
}

bool UAuracronRealmsBridge::LoadDefaultRealmConfigurations()
{
    // Configuração padrão para Planície Radiante (Surface)
    FAuracronRealmConfiguration PlanicieConfig;
    PlanicieConfig.RealmType = EAuracronRealmType::PlanicieRadiante;
    PlanicieConfig.RealmName = FText::FromString(TEXT("Planície Radiante"));
    PlanicieConfig.RealmDescription = FText::FromString(TEXT("Camada superficial com vegetação exuberante"));
    PlanicieConfig.MinHeight = 0.0f;
    PlanicieConfig.MaxHeight = 1000.0f;
    PlanicieConfig.AmbientColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado
    PlanicieConfig.LightingIntensity = 1.2f;
    PlanicieConfig.FogDensity = 0.05f;
    PlanicieConfig.GravityScale = 1.0f;
    PlanicieConfig.MovementSpeedModifier = 1.0f;
    PlanicieConfig.EvolutionTime = 600.0f; // 10 minutos

    // Configuração padrão para Firmamento Zephyr (Sky)
    FAuracronRealmConfiguration FirmamentoConfig;
    FirmamentoConfig.RealmType = EAuracronRealmType::FirmamentoZephyr;
    FirmamentoConfig.RealmName = FText::FromString(TEXT("Firmamento Zephyr"));
    FirmamentoConfig.RealmDescription = FText::FromString(TEXT("Camada aérea com plataformas flutuantes"));
    FirmamentoConfig.MinHeight = 1000.0f;
    FirmamentoConfig.MaxHeight = 3000.0f;
    FirmamentoConfig.AmbientColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Azul celeste
    FirmamentoConfig.LightingIntensity = 1.5f;
    FirmamentoConfig.FogDensity = 0.02f;
    FirmamentoConfig.GravityScale = 0.6f; // Gravidade reduzida
    FirmamentoConfig.MovementSpeedModifier = 1.3f; // Movimento mais rápido
    FirmamentoConfig.EvolutionTime = 900.0f; // 15 minutos

    // Configuração padrão para Abismo Umbrio (Underground)
    FAuracronRealmConfiguration AbismoConfig;
    AbismoConfig.RealmType = EAuracronRealmType::AbismoUmbrio;
    AbismoConfig.RealmName = FText::FromString(TEXT("Abismo Umbrio"));
    AbismoConfig.RealmDescription = FText::FromString(TEXT("Camada subterrânea com cavernas cristalinas"));
    AbismoConfig.MinHeight = -1000.0f;
    AbismoConfig.MaxHeight = 0.0f;
    AbismoConfig.AmbientColor = FLinearColor(0.3f, 0.2f, 0.8f, 1.0f); // Roxo sombrio
    AbismoConfig.LightingIntensity = 0.6f;
    AbismoConfig.FogDensity = 0.15f;
    AbismoConfig.GravityScale = 1.2f; // Gravidade aumentada
    AbismoConfig.MovementSpeedModifier = 0.8f; // Movimento mais lento
    AbismoConfig.EvolutionTime = 1200.0f; // 20 minutos

    // Adicionar configurações ao mapa
    RealmConfigurations.Add(EAuracronRealmType::PlanicieRadiante, PlanicieConfig);
    RealmConfigurations.Add(EAuracronRealmType::FirmamentoZephyr, FirmamentoConfig);
    RealmConfigurations.Add(EAuracronRealmType::AbismoUmbrio, AbismoConfig);

    // Configurar geração procedural padrão
    FAuracronProceduralGenerationConfig DefaultPCGConfig;
    DefaultPCGConfig.GenerationSeed = 12345;
    DefaultPCGConfig.GenerationDensity = 1.0f;
    DefaultPCGConfig.GenerationArea = 1000000.0f; // 1km²
    DefaultPCGConfig.RegenerationInterval = 120.0f;
    DefaultPCGConfig.bGenerationActive = true;
    DefaultPCGConfig.bAllowDynamicRegeneration = true;
    DefaultPCGConfig.bUseAsyncStreaming = true;

    // Aplicar configuração PCG para cada Realm
    for (auto& RealmPair : RealmConfigurations)
    {
        DefaultPCGConfig.TargetRealm = RealmPair.Key;
        ProceduralConfigs.Add(RealmPair.Key, DefaultPCGConfig);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configurações padrão dos Realms carregadas"));
    return true;
}
