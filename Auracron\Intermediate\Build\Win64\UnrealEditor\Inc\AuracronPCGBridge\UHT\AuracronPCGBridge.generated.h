// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGBridge.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGBridge_generated_h
#error "AuracronPCGBridge.generated.h already included, missing '#pragma once' in AuracronPCGBridge.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FPCGPerformanceData ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBridge_h_134_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGPerformanceData;
// ********** End ScriptStruct FPCGPerformanceData *************************************************

// ********** Begin ScriptStruct FPCGAsyncGenerationParams *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBridge_h_167_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGAsyncGenerationParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGAsyncGenerationParams;
// ********** End ScriptStruct FPCGAsyncGenerationParams *******************************************

// ********** Begin ScriptStruct FPCGMemoryOptimizationSettings ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBridge_h_200_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGMemoryOptimizationSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGMemoryOptimizationSettings;
// ********** End ScriptStruct FPCGMemoryOptimizationSettings **************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBridge_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
