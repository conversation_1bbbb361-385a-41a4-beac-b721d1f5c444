// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliageBiome.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliageBiome() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageBiomeManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageBiomeManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronBiomeConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronBiomeDefinition();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronClimateData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronEcosystemRuleData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSpeciesData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronBiomeType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronBiomeType;
static UEnum* EAuracronBiomeType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronBiomeType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronBiomeType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronBiomeType"));
	}
	return Z_Registration_Info_UEnum_EAuracronBiomeType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronBiomeType>()
{
	return EAuracronBiomeType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Coastal.DisplayName", "Coastal" },
		{ "Coastal.Name", "EAuracronBiomeType::Coastal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Biome types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronBiomeType::Custom" },
		{ "Desert.DisplayName", "Desert" },
		{ "Desert.Name", "EAuracronBiomeType::Desert" },
		{ "Forest.DisplayName", "Forest" },
		{ "Forest.Name", "EAuracronBiomeType::Forest" },
		{ "Grassland.DisplayName", "Grassland" },
		{ "Grassland.Name", "EAuracronBiomeType::Grassland" },
		{ "Magical.DisplayName", "Magical" },
		{ "Magical.Name", "EAuracronBiomeType::Magical" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
		{ "Mountain.DisplayName", "Mountain" },
		{ "Mountain.Name", "EAuracronBiomeType::Mountain" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome types" },
#endif
		{ "Tundra.DisplayName", "Tundra" },
		{ "Tundra.Name", "EAuracronBiomeType::Tundra" },
		{ "Urban.DisplayName", "Urban" },
		{ "Urban.Name", "EAuracronBiomeType::Urban" },
		{ "Volcanic.DisplayName", "Volcanic" },
		{ "Volcanic.Name", "EAuracronBiomeType::Volcanic" },
		{ "Wetland.DisplayName", "Wetland" },
		{ "Wetland.Name", "EAuracronBiomeType::Wetland" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronBiomeType::Grassland", (int64)EAuracronBiomeType::Grassland },
		{ "EAuracronBiomeType::Forest", (int64)EAuracronBiomeType::Forest },
		{ "EAuracronBiomeType::Desert", (int64)EAuracronBiomeType::Desert },
		{ "EAuracronBiomeType::Tundra", (int64)EAuracronBiomeType::Tundra },
		{ "EAuracronBiomeType::Wetland", (int64)EAuracronBiomeType::Wetland },
		{ "EAuracronBiomeType::Mountain", (int64)EAuracronBiomeType::Mountain },
		{ "EAuracronBiomeType::Coastal", (int64)EAuracronBiomeType::Coastal },
		{ "EAuracronBiomeType::Urban", (int64)EAuracronBiomeType::Urban },
		{ "EAuracronBiomeType::Volcanic", (int64)EAuracronBiomeType::Volcanic },
		{ "EAuracronBiomeType::Magical", (int64)EAuracronBiomeType::Magical },
		{ "EAuracronBiomeType::Custom", (int64)EAuracronBiomeType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronBiomeType",
	"EAuracronBiomeType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType()
{
	if (!Z_Registration_Info_UEnum_EAuracronBiomeType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronBiomeType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronBiomeType.InnerSingleton;
}
// ********** End Enum EAuracronBiomeType **********************************************************

// ********** Begin Enum EAuracronClimateType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronClimateType;
static UEnum* EAuracronClimateType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronClimateType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronClimateType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronClimateType"));
	}
	return Z_Registration_Info_UEnum_EAuracronClimateType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronClimateType>()
{
	return EAuracronClimateType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Alpine.DisplayName", "Alpine" },
		{ "Alpine.Name", "EAuracronClimateType::Alpine" },
		{ "Arid.DisplayName", "Arid" },
		{ "Arid.Name", "EAuracronClimateType::Arid" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Climate types\n" },
#endif
		{ "Continental.DisplayName", "Continental" },
		{ "Continental.Name", "EAuracronClimateType::Continental" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronClimateType::Custom" },
		{ "Mediterranean.DisplayName", "Mediterranean" },
		{ "Mediterranean.Name", "EAuracronClimateType::Mediterranean" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
		{ "Oceanic.DisplayName", "Oceanic" },
		{ "Oceanic.Name", "EAuracronClimateType::Oceanic" },
		{ "Polar.DisplayName", "Polar" },
		{ "Polar.Name", "EAuracronClimateType::Polar" },
		{ "Subarctic.DisplayName", "Subarctic" },
		{ "Subarctic.Name", "EAuracronClimateType::Subarctic" },
		{ "Temperate.DisplayName", "Temperate" },
		{ "Temperate.Name", "EAuracronClimateType::Temperate" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Climate types" },
#endif
		{ "Tropical.DisplayName", "Tropical" },
		{ "Tropical.Name", "EAuracronClimateType::Tropical" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronClimateType::Tropical", (int64)EAuracronClimateType::Tropical },
		{ "EAuracronClimateType::Arid", (int64)EAuracronClimateType::Arid },
		{ "EAuracronClimateType::Temperate", (int64)EAuracronClimateType::Temperate },
		{ "EAuracronClimateType::Continental", (int64)EAuracronClimateType::Continental },
		{ "EAuracronClimateType::Polar", (int64)EAuracronClimateType::Polar },
		{ "EAuracronClimateType::Mediterranean", (int64)EAuracronClimateType::Mediterranean },
		{ "EAuracronClimateType::Oceanic", (int64)EAuracronClimateType::Oceanic },
		{ "EAuracronClimateType::Subarctic", (int64)EAuracronClimateType::Subarctic },
		{ "EAuracronClimateType::Alpine", (int64)EAuracronClimateType::Alpine },
		{ "EAuracronClimateType::Custom", (int64)EAuracronClimateType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronClimateType",
	"EAuracronClimateType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType()
{
	if (!Z_Registration_Info_UEnum_EAuracronClimateType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronClimateType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronClimateType.InnerSingleton;
}
// ********** End Enum EAuracronClimateType ********************************************************

// ********** Begin Enum EAuracronTransitionZoneType ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTransitionZoneType;
static UEnum* EAuracronTransitionZoneType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTransitionZoneType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTransitionZoneType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronTransitionZoneType"));
	}
	return Z_Registration_Info_UEnum_EAuracronTransitionZoneType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronTransitionZoneType>()
{
	return EAuracronTransitionZoneType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transition zone types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronTransitionZoneType::Custom" },
		{ "Ecotone.DisplayName", "Ecotone" },
		{ "Ecotone.Name", "EAuracronTransitionZoneType::Ecotone" },
		{ "Elevation.DisplayName", "Elevation Based" },
		{ "Elevation.Name", "EAuracronTransitionZoneType::Elevation" },
		{ "Gradual.DisplayName", "Gradual" },
		{ "Gradual.Name", "EAuracronTransitionZoneType::Gradual" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
		{ "Moisture.DisplayName", "Moisture Based" },
		{ "Moisture.Name", "EAuracronTransitionZoneType::Moisture" },
		{ "Mosaic.DisplayName", "Mosaic" },
		{ "Mosaic.Name", "EAuracronTransitionZoneType::Mosaic" },
		{ "Sharp.DisplayName", "Sharp" },
		{ "Sharp.Name", "EAuracronTransitionZoneType::Sharp" },
		{ "Temperature.DisplayName", "Temperature Based" },
		{ "Temperature.Name", "EAuracronTransitionZoneType::Temperature" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition zone types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTransitionZoneType::Sharp", (int64)EAuracronTransitionZoneType::Sharp },
		{ "EAuracronTransitionZoneType::Gradual", (int64)EAuracronTransitionZoneType::Gradual },
		{ "EAuracronTransitionZoneType::Ecotone", (int64)EAuracronTransitionZoneType::Ecotone },
		{ "EAuracronTransitionZoneType::Mosaic", (int64)EAuracronTransitionZoneType::Mosaic },
		{ "EAuracronTransitionZoneType::Elevation", (int64)EAuracronTransitionZoneType::Elevation },
		{ "EAuracronTransitionZoneType::Moisture", (int64)EAuracronTransitionZoneType::Moisture },
		{ "EAuracronTransitionZoneType::Temperature", (int64)EAuracronTransitionZoneType::Temperature },
		{ "EAuracronTransitionZoneType::Custom", (int64)EAuracronTransitionZoneType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronTransitionZoneType",
	"EAuracronTransitionZoneType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType()
{
	if (!Z_Registration_Info_UEnum_EAuracronTransitionZoneType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTransitionZoneType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTransitionZoneType.InnerSingleton;
}
// ********** End Enum EAuracronTransitionZoneType *************************************************

// ********** Begin Enum EAuracronSpeciesDistributionPattern ***************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSpeciesDistributionPattern;
static UEnum* EAuracronSpeciesDistributionPattern_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSpeciesDistributionPattern.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSpeciesDistributionPattern.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronSpeciesDistributionPattern"));
	}
	return Z_Registration_Info_UEnum_EAuracronSpeciesDistributionPattern.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronSpeciesDistributionPattern>()
{
	return EAuracronSpeciesDistributionPattern_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Clustered.DisplayName", "Clustered" },
		{ "Clustered.Name", "EAuracronSpeciesDistributionPattern::Clustered" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Species distribution patterns\n" },
#endif
		{ "Competition.DisplayName", "Competition Based" },
		{ "Competition.Name", "EAuracronSpeciesDistributionPattern::Competition" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronSpeciesDistributionPattern::Custom" },
		{ "Fractal.DisplayName", "Fractal" },
		{ "Fractal.Name", "EAuracronSpeciesDistributionPattern::Fractal" },
		{ "Linear.DisplayName", "Linear" },
		{ "Linear.Name", "EAuracronSpeciesDistributionPattern::Linear" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
		{ "Radial.DisplayName", "Radial" },
		{ "Radial.Name", "EAuracronSpeciesDistributionPattern::Radial" },
		{ "Random.DisplayName", "Random" },
		{ "Random.Name", "EAuracronSpeciesDistributionPattern::Random" },
		{ "Succession.DisplayName", "Succession Based" },
		{ "Succession.Name", "EAuracronSpeciesDistributionPattern::Succession" },
		{ "Symbiotic.DisplayName", "Symbiotic" },
		{ "Symbiotic.Name", "EAuracronSpeciesDistributionPattern::Symbiotic" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Species distribution patterns" },
#endif
		{ "Uniform.DisplayName", "Uniform" },
		{ "Uniform.Name", "EAuracronSpeciesDistributionPattern::Uniform" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSpeciesDistributionPattern::Random", (int64)EAuracronSpeciesDistributionPattern::Random },
		{ "EAuracronSpeciesDistributionPattern::Clustered", (int64)EAuracronSpeciesDistributionPattern::Clustered },
		{ "EAuracronSpeciesDistributionPattern::Uniform", (int64)EAuracronSpeciesDistributionPattern::Uniform },
		{ "EAuracronSpeciesDistributionPattern::Linear", (int64)EAuracronSpeciesDistributionPattern::Linear },
		{ "EAuracronSpeciesDistributionPattern::Radial", (int64)EAuracronSpeciesDistributionPattern::Radial },
		{ "EAuracronSpeciesDistributionPattern::Fractal", (int64)EAuracronSpeciesDistributionPattern::Fractal },
		{ "EAuracronSpeciesDistributionPattern::Succession", (int64)EAuracronSpeciesDistributionPattern::Succession },
		{ "EAuracronSpeciesDistributionPattern::Competition", (int64)EAuracronSpeciesDistributionPattern::Competition },
		{ "EAuracronSpeciesDistributionPattern::Symbiotic", (int64)EAuracronSpeciesDistributionPattern::Symbiotic },
		{ "EAuracronSpeciesDistributionPattern::Custom", (int64)EAuracronSpeciesDistributionPattern::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronSpeciesDistributionPattern",
	"EAuracronSpeciesDistributionPattern",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern()
{
	if (!Z_Registration_Info_UEnum_EAuracronSpeciesDistributionPattern.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSpeciesDistributionPattern.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSpeciesDistributionPattern.InnerSingleton;
}
// ********** End Enum EAuracronSpeciesDistributionPattern *****************************************

// ********** Begin Enum EAuracronEcosystemRuleType ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronEcosystemRuleType;
static UEnum* EAuracronEcosystemRuleType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronEcosystemRuleType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronEcosystemRuleType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronEcosystemRuleType"));
	}
	return Z_Registration_Info_UEnum_EAuracronEcosystemRuleType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronEcosystemRuleType>()
{
	return EAuracronEcosystemRuleType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Commensalism.DisplayName", "Commensalism" },
		{ "Commensalism.Name", "EAuracronEcosystemRuleType::Commensalism" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Ecosystem rules\n" },
#endif
		{ "Competition.DisplayName", "Competition" },
		{ "Competition.Name", "EAuracronEcosystemRuleType::Competition" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronEcosystemRuleType::Custom" },
		{ "Disturbance.DisplayName", "Disturbance" },
		{ "Disturbance.Name", "EAuracronEcosystemRuleType::Disturbance" },
		{ "Migration.DisplayName", "Migration" },
		{ "Migration.Name", "EAuracronEcosystemRuleType::Migration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
		{ "Mutualism.DisplayName", "Mutualism" },
		{ "Mutualism.Name", "EAuracronEcosystemRuleType::Mutualism" },
		{ "Parasitism.DisplayName", "Parasitism" },
		{ "Parasitism.Name", "EAuracronEcosystemRuleType::Parasitism" },
		{ "Predation.DisplayName", "Predation" },
		{ "Predation.Name", "EAuracronEcosystemRuleType::Predation" },
		{ "Seasonal.DisplayName", "Seasonal" },
		{ "Seasonal.Name", "EAuracronEcosystemRuleType::Seasonal" },
		{ "Succession.DisplayName", "Succession" },
		{ "Succession.Name", "EAuracronEcosystemRuleType::Succession" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ecosystem rules" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronEcosystemRuleType::Predation", (int64)EAuracronEcosystemRuleType::Predation },
		{ "EAuracronEcosystemRuleType::Competition", (int64)EAuracronEcosystemRuleType::Competition },
		{ "EAuracronEcosystemRuleType::Mutualism", (int64)EAuracronEcosystemRuleType::Mutualism },
		{ "EAuracronEcosystemRuleType::Parasitism", (int64)EAuracronEcosystemRuleType::Parasitism },
		{ "EAuracronEcosystemRuleType::Commensalism", (int64)EAuracronEcosystemRuleType::Commensalism },
		{ "EAuracronEcosystemRuleType::Succession", (int64)EAuracronEcosystemRuleType::Succession },
		{ "EAuracronEcosystemRuleType::Disturbance", (int64)EAuracronEcosystemRuleType::Disturbance },
		{ "EAuracronEcosystemRuleType::Migration", (int64)EAuracronEcosystemRuleType::Migration },
		{ "EAuracronEcosystemRuleType::Seasonal", (int64)EAuracronEcosystemRuleType::Seasonal },
		{ "EAuracronEcosystemRuleType::Custom", (int64)EAuracronEcosystemRuleType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronEcosystemRuleType",
	"EAuracronEcosystemRuleType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType()
{
	if (!Z_Registration_Info_UEnum_EAuracronEcosystemRuleType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronEcosystemRuleType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronEcosystemRuleType.InnerSingleton;
}
// ********** End Enum EAuracronEcosystemRuleType **************************************************

// ********** Begin ScriptStruct FAuracronClimateData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronClimateData;
class UScriptStruct* FAuracronClimateData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronClimateData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronClimateData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronClimateData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronClimateData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronClimateData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronClimateData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Climate Data\n * Environmental data for climate simulation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Climate Data\nEnvironmental data for climate simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageTemperature_MetaData[] = {
		{ "Category", "Temperature" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemperatureRange_MetaData[] = {
		{ "Category", "Temperature" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalVariation_MetaData[] = {
		{ "Category", "Temperature" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnnualPrecipitation_MetaData[] = {
		{ "Category", "Precipitation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrecipitationVariability_MetaData[] = {
		{ "Category", "Precipitation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrySeason_MetaData[] = {
		{ "Category", "Precipitation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelativeHumidity_MetaData[] = {
		{ "Category", "Humidity" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HumidityRange_MetaData[] = {
		{ "Category", "Humidity" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindSpeed_MetaData[] = {
		{ "Category", "Wind" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindDirection_MetaData[] = {
		{ "Category", "Wind" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindVariability_MetaData[] = {
		{ "Category", "Wind" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolarRadiation_MetaData[] = {
		{ "Category", "Solar" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DaylightHours_MetaData[] = {
		{ "Category", "Solar" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CloudCover_MetaData[] = {
		{ "Category", "Solar" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoilMoisture_MetaData[] = {
		{ "Category", "Soil" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoilFertility_MetaData[] = {
		{ "Category", "Soil" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoilPH_MetaData[] = {
		{ "Category", "Soil" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Elevation_MetaData[] = {
		{ "Category", "Elevation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Slope_MetaData[] = {
		{ "Category", "Elevation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Aspect_MetaData[] = {
		{ "Category", "Elevation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageTemperature;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TemperatureRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonalVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnnualPrecipitation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PrecipitationVariability;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DrySeason;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RelativeHumidity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HumidityRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindSpeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WindDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindVariability;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SolarRadiation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DaylightHours;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CloudCover;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SoilMoisture;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SoilFertility;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SoilPH;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Elevation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Slope;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Aspect;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronClimateData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_AverageTemperature = { "AverageTemperature", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, AverageTemperature), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageTemperature_MetaData), NewProp_AverageTemperature_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_TemperatureRange = { "TemperatureRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, TemperatureRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemperatureRange_MetaData), NewProp_TemperatureRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_SeasonalVariation = { "SeasonalVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, SeasonalVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalVariation_MetaData), NewProp_SeasonalVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_AnnualPrecipitation = { "AnnualPrecipitation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, AnnualPrecipitation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnnualPrecipitation_MetaData), NewProp_AnnualPrecipitation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_PrecipitationVariability = { "PrecipitationVariability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, PrecipitationVariability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrecipitationVariability_MetaData), NewProp_PrecipitationVariability_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_DrySeason = { "DrySeason", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, DrySeason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrySeason_MetaData), NewProp_DrySeason_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_RelativeHumidity = { "RelativeHumidity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, RelativeHumidity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelativeHumidity_MetaData), NewProp_RelativeHumidity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_HumidityRange = { "HumidityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, HumidityRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HumidityRange_MetaData), NewProp_HumidityRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_WindSpeed = { "WindSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, WindSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindSpeed_MetaData), NewProp_WindSpeed_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_WindDirection = { "WindDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, WindDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindDirection_MetaData), NewProp_WindDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_WindVariability = { "WindVariability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, WindVariability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindVariability_MetaData), NewProp_WindVariability_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_SolarRadiation = { "SolarRadiation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, SolarRadiation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolarRadiation_MetaData), NewProp_SolarRadiation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_DaylightHours = { "DaylightHours", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, DaylightHours), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DaylightHours_MetaData), NewProp_DaylightHours_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_CloudCover = { "CloudCover", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, CloudCover), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CloudCover_MetaData), NewProp_CloudCover_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_SoilMoisture = { "SoilMoisture", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, SoilMoisture), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoilMoisture_MetaData), NewProp_SoilMoisture_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_SoilFertility = { "SoilFertility", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, SoilFertility), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoilFertility_MetaData), NewProp_SoilFertility_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_SoilPH = { "SoilPH", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, SoilPH), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoilPH_MetaData), NewProp_SoilPH_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_Elevation = { "Elevation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, Elevation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Elevation_MetaData), NewProp_Elevation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_Slope = { "Slope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, Slope), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Slope_MetaData), NewProp_Slope_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_Aspect = { "Aspect", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClimateData, Aspect), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Aspect_MetaData), NewProp_Aspect_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronClimateData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_AverageTemperature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_TemperatureRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_SeasonalVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_AnnualPrecipitation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_PrecipitationVariability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_DrySeason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_RelativeHumidity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_HumidityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_WindSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_WindDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_WindVariability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_SolarRadiation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_DaylightHours,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_CloudCover,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_SoilMoisture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_SoilFertility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_SoilPH,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_Elevation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_Slope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewProp_Aspect,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClimateData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronClimateData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronClimateData",
	Z_Construct_UScriptStruct_FAuracronClimateData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClimateData_Statics::PropPointers),
	sizeof(FAuracronClimateData),
	alignof(FAuracronClimateData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClimateData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronClimateData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronClimateData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronClimateData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronClimateData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronClimateData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronClimateData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronClimateData ************************************************

// ********** Begin ScriptStruct FAuracronSpeciesData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSpeciesData;
class UScriptStruct* FAuracronSpeciesData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSpeciesData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSpeciesData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSpeciesData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronSpeciesData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSpeciesData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Species Data\n * Data for individual species in biomes\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Species Data\nData for individual species in biomes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeciesId_MetaData[] = {
		{ "Category", "Species" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeciesName_MetaData[] = {
		{ "Category", "Species" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeciesMesh_MetaData[] = {
		{ "Category", "Species" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistributionPattern_MetaData[] = {
		{ "Category", "Species" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseAbundance_MetaData[] = {
		{ "Category", "Abundance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAbundance_MetaData[] = {
		{ "Category", "Abundance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthRate_MetaData[] = {
		{ "Category", "Abundance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemperatureTolerance_MetaData[] = {
		{ "Category", "Environmental Tolerance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MoistureTolerance_MetaData[] = {
		{ "Category", "Environmental Tolerance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoilTolerance_MetaData[] = {
		{ "Category", "Environmental Tolerance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElevationTolerance_MetaData[] = {
		{ "Category", "Environmental Tolerance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinSpacing_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSpacing_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterRadius_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterSize_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompetitorSpecies_MetaData[] = {
		{ "Category", "Interactions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SymbioticSpecies_MetaData[] = {
		{ "Category", "Interactions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreySpecies_MetaData[] = {
		{ "Category", "Interactions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredatorSpecies_MetaData[] = {
		{ "Category", "Interactions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifeSpan_MetaData[] = {
		{ "Category", "Lifecycle" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaturityAge_MetaData[] = {
		{ "Category", "Lifecycle" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReproductionRate_MetaData[] = {
		{ "Category", "Lifecycle" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MortalityRate_MetaData[] = {
		{ "Category", "Lifecycle" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SpeciesId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SpeciesName;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SpeciesMesh;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DistributionPattern_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DistributionPattern;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseAbundance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAbundance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GrowthRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TemperatureTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MoistureTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SoilTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ElevationTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinSpacing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSpacing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClusterRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ClusterSize;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompetitorSpecies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompetitorSpecies;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SymbioticSpecies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SymbioticSpecies;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PreySpecies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PreySpecies;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PredatorSpecies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PredatorSpecies;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LifeSpan;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaturityAge;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReproductionRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MortalityRate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSpeciesData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SpeciesId = { "SpeciesId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, SpeciesId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeciesId_MetaData), NewProp_SpeciesId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SpeciesName = { "SpeciesName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, SpeciesName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeciesName_MetaData), NewProp_SpeciesName_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SpeciesMesh = { "SpeciesMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, SpeciesMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeciesMesh_MetaData), NewProp_SpeciesMesh_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_DistributionPattern_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_DistributionPattern = { "DistributionPattern", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, DistributionPattern), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSpeciesDistributionPattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistributionPattern_MetaData), NewProp_DistributionPattern_MetaData) }; // 3082322166
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_BaseAbundance = { "BaseAbundance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, BaseAbundance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseAbundance_MetaData), NewProp_BaseAbundance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MaxAbundance = { "MaxAbundance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, MaxAbundance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAbundance_MetaData), NewProp_MaxAbundance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_GrowthRate = { "GrowthRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, GrowthRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthRate_MetaData), NewProp_GrowthRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_TemperatureTolerance = { "TemperatureTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, TemperatureTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemperatureTolerance_MetaData), NewProp_TemperatureTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MoistureTolerance = { "MoistureTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, MoistureTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MoistureTolerance_MetaData), NewProp_MoistureTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SoilTolerance = { "SoilTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, SoilTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoilTolerance_MetaData), NewProp_SoilTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_ElevationTolerance = { "ElevationTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, ElevationTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElevationTolerance_MetaData), NewProp_ElevationTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MinSpacing = { "MinSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, MinSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinSpacing_MetaData), NewProp_MinSpacing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MaxSpacing = { "MaxSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, MaxSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSpacing_MetaData), NewProp_MaxSpacing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_ClusterRadius = { "ClusterRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, ClusterRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterRadius_MetaData), NewProp_ClusterRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_ClusterSize = { "ClusterSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, ClusterSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterSize_MetaData), NewProp_ClusterSize_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_CompetitorSpecies_Inner = { "CompetitorSpecies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_CompetitorSpecies = { "CompetitorSpecies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, CompetitorSpecies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompetitorSpecies_MetaData), NewProp_CompetitorSpecies_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SymbioticSpecies_Inner = { "SymbioticSpecies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SymbioticSpecies = { "SymbioticSpecies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, SymbioticSpecies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SymbioticSpecies_MetaData), NewProp_SymbioticSpecies_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_PreySpecies_Inner = { "PreySpecies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_PreySpecies = { "PreySpecies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, PreySpecies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreySpecies_MetaData), NewProp_PreySpecies_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_PredatorSpecies_Inner = { "PredatorSpecies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_PredatorSpecies = { "PredatorSpecies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, PredatorSpecies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredatorSpecies_MetaData), NewProp_PredatorSpecies_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_LifeSpan = { "LifeSpan", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, LifeSpan), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifeSpan_MetaData), NewProp_LifeSpan_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MaturityAge = { "MaturityAge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, MaturityAge), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaturityAge_MetaData), NewProp_MaturityAge_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_ReproductionRate = { "ReproductionRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, ReproductionRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReproductionRate_MetaData), NewProp_ReproductionRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MortalityRate = { "MortalityRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpeciesData, MortalityRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MortalityRate_MetaData), NewProp_MortalityRate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SpeciesId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SpeciesName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SpeciesMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_DistributionPattern_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_DistributionPattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_BaseAbundance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MaxAbundance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_GrowthRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_TemperatureTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MoistureTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SoilTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_ElevationTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MinSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MaxSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_ClusterRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_ClusterSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_CompetitorSpecies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_CompetitorSpecies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SymbioticSpecies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_SymbioticSpecies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_PreySpecies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_PreySpecies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_PredatorSpecies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_PredatorSpecies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_LifeSpan,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MaturityAge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_ReproductionRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewProp_MortalityRate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronSpeciesData",
	Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::PropPointers),
	sizeof(FAuracronSpeciesData),
	alignof(FAuracronSpeciesData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSpeciesData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSpeciesData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSpeciesData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSpeciesData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSpeciesData ************************************************

// ********** Begin ScriptStruct FAuracronEcosystemRuleData ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronEcosystemRuleData;
class UScriptStruct* FAuracronEcosystemRuleData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronEcosystemRuleData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronEcosystemRuleData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronEcosystemRuleData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronEcosystemRuleData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronEcosystemRuleData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ecosystem Rule Data\n * Rules governing ecosystem interactions\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ecosystem Rule Data\nRules governing ecosystem interactions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleId_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleName_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleType_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnabled_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleStrength_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimarySpecies_MetaData[] = {
		{ "Category", "Participants" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecondarySpecies_MetaData[] = {
		{ "Category", "Participants" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectRadius_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresProximity_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationDelay_MetaData[] = {
		{ "Category", "Temporal" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Temporal" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSeasonallyActive_MetaData[] = {
		{ "Category", "Temporal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// -1 = permanent\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "-1 = permanent" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PopulationEffect_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthRateEffect_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistributionEffect_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinTemperature_MetaData[] = {
		{ "Category", "Conditions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTemperature_MetaData[] = {
		{ "Category", "Conditions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinMoisture_MetaData[] = {
		{ "Category", "Conditions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMoisture_MetaData[] = {
		{ "Category", "Conditions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomParameters_MetaData[] = {
		{ "Category", "Custom" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RuleType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RuleType;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RuleStrength;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PrimarySpecies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PrimarySpecies;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SecondarySpecies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SecondarySpecies;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectRadius;
	static void NewProp_bRequiresProximity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresProximity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivationDelay;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_bSeasonallyActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSeasonallyActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PopulationEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GrowthRateEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistributionEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinTemperature;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxTemperature;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinMoisture;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxMoisture;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CustomParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomParameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronEcosystemRuleData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_RuleId = { "RuleId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, RuleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleId_MetaData), NewProp_RuleId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_RuleName = { "RuleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, RuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleName_MetaData), NewProp_RuleName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_RuleType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_RuleType = { "RuleType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, RuleType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronEcosystemRuleType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleType_MetaData), NewProp_RuleType_MetaData) }; // 39030625
void Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((FAuracronEcosystemRuleData*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronEcosystemRuleData), &Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnabled_MetaData), NewProp_bEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_RuleStrength = { "RuleStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, RuleStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleStrength_MetaData), NewProp_RuleStrength_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_PrimarySpecies_Inner = { "PrimarySpecies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_PrimarySpecies = { "PrimarySpecies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, PrimarySpecies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimarySpecies_MetaData), NewProp_PrimarySpecies_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_SecondarySpecies_Inner = { "SecondarySpecies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_SecondarySpecies = { "SecondarySpecies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, SecondarySpecies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecondarySpecies_MetaData), NewProp_SecondarySpecies_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_EffectRadius = { "EffectRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, EffectRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectRadius_MetaData), NewProp_EffectRadius_MetaData) };
void Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bRequiresProximity_SetBit(void* Obj)
{
	((FAuracronEcosystemRuleData*)Obj)->bRequiresProximity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bRequiresProximity = { "bRequiresProximity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronEcosystemRuleData), &Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bRequiresProximity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresProximity_MetaData), NewProp_bRequiresProximity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_ActivationDelay = { "ActivationDelay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, ActivationDelay), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationDelay_MetaData), NewProp_ActivationDelay_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bSeasonallyActive_SetBit(void* Obj)
{
	((FAuracronEcosystemRuleData*)Obj)->bSeasonallyActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bSeasonallyActive = { "bSeasonallyActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronEcosystemRuleData), &Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bSeasonallyActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSeasonallyActive_MetaData), NewProp_bSeasonallyActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_PopulationEffect = { "PopulationEffect", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, PopulationEffect), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PopulationEffect_MetaData), NewProp_PopulationEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_GrowthRateEffect = { "GrowthRateEffect", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, GrowthRateEffect), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthRateEffect_MetaData), NewProp_GrowthRateEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_DistributionEffect = { "DistributionEffect", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, DistributionEffect), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistributionEffect_MetaData), NewProp_DistributionEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_MinTemperature = { "MinTemperature", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, MinTemperature), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinTemperature_MetaData), NewProp_MinTemperature_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_MaxTemperature = { "MaxTemperature", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, MaxTemperature), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTemperature_MetaData), NewProp_MaxTemperature_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_MinMoisture = { "MinMoisture", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, MinMoisture), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinMoisture_MetaData), NewProp_MinMoisture_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_MaxMoisture = { "MaxMoisture", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, MaxMoisture), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMoisture_MetaData), NewProp_MaxMoisture_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_CustomParameters_ValueProp = { "CustomParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_CustomParameters_Key_KeyProp = { "CustomParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_CustomParameters = { "CustomParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEcosystemRuleData, CustomParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomParameters_MetaData), NewProp_CustomParameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_RuleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_RuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_RuleType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_RuleType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_RuleStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_PrimarySpecies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_PrimarySpecies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_SecondarySpecies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_SecondarySpecies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_EffectRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bRequiresProximity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_ActivationDelay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_bSeasonallyActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_PopulationEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_GrowthRateEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_DistributionEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_MinTemperature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_MaxTemperature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_MinMoisture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_MaxMoisture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_CustomParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_CustomParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewProp_CustomParameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronEcosystemRuleData",
	Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::PropPointers),
	sizeof(FAuracronEcosystemRuleData),
	alignof(FAuracronEcosystemRuleData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronEcosystemRuleData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronEcosystemRuleData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronEcosystemRuleData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronEcosystemRuleData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronEcosystemRuleData ******************************************

// ********** Begin ScriptStruct FAuracronBiomeDefinition ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronBiomeDefinition;
class UScriptStruct* FAuracronBiomeDefinition::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBiomeDefinition.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronBiomeDefinition.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronBiomeDefinition, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronBiomeDefinition"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBiomeDefinition.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Biome Definition\n * Complete definition of a biome including climate, species, and rules\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome Definition\nComplete definition of a biome including climate, species, and rules" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "Category", "Biome" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeName_MetaData[] = {
		{ "Category", "Biome" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeType_MetaData[] = {
		{ "Category", "Biome" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClimateType_MetaData[] = {
		{ "Category", "Biome" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClimateData_MetaData[] = {
		{ "Category", "Biome" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DominantSpecies_MetaData[] = {
		{ "Category", "Species" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubdominantSpecies_MetaData[] = {
		{ "Category", "Species" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RareSpecies_MetaData[] = {
		{ "Category", "Species" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EcosystemRules_MetaData[] = {
		{ "Category", "Ecosystem" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionType_MetaData[] = {
		{ "Category", "Transitions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionWidth_MetaData[] = {
		{ "Category", "Transitions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdjacentBiomes_MetaData[] = {
		{ "Category", "Transitions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinBiomeSize_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBiomeSize_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeDensity_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSeasonalChanges_MetaData[] = {
		{ "Category", "Temporal" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalIntensity_MetaData[] = {
		{ "Category", "Temporal" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccessionEnabled_MetaData[] = {
		{ "Category", "Temporal" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuccessionRate_MetaData[] = {
		{ "Category", "Temporal" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDisturbanceEvents_MetaData[] = {
		{ "Category", "Disturbance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisturbanceFrequency_MetaData[] = {
		{ "Category", "Disturbance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisturbanceIntensity_MetaData[] = {
		{ "Category", "Disturbance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomePCGGraph_MetaData[] = {
		{ "Category", "PCG Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGParameters_MetaData[] = {
		{ "Category", "PCG Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BiomeType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BiomeType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ClimateType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ClimateType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ClimateData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DominantSpecies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DominantSpecies;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SubdominantSpecies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SubdominantSpecies;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RareSpecies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RareSpecies;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EcosystemRules_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EcosystemRules;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionWidth;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdjacentBiomes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AdjacentBiomes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinBiomeSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxBiomeSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BiomeDensity;
	static void NewProp_bSeasonalChanges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSeasonalChanges;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonalIntensity;
	static void NewProp_bSuccessionEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccessionEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SuccessionRate;
	static void NewProp_bDisturbanceEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDisturbanceEvents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DisturbanceFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DisturbanceIntensity;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BiomePCGGraph;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PCGParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PCGParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PCGParameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronBiomeDefinition>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomeName = { "BiomeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, BiomeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeName_MetaData), NewProp_BiomeName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomeType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomeType = { "BiomeType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, BiomeType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBiomeType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeType_MetaData), NewProp_BiomeType_MetaData) }; // 1433977737
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_ClimateType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_ClimateType = { "ClimateType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, ClimateType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronClimateType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClimateType_MetaData), NewProp_ClimateType_MetaData) }; // 2501228809
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_ClimateData = { "ClimateData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, ClimateData), Z_Construct_UScriptStruct_FAuracronClimateData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClimateData_MetaData), NewProp_ClimateData_MetaData) }; // 460639699
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_DominantSpecies_Inner = { "DominantSpecies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronSpeciesData, METADATA_PARAMS(0, nullptr) }; // 4115711002
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_DominantSpecies = { "DominantSpecies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, DominantSpecies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DominantSpecies_MetaData), NewProp_DominantSpecies_MetaData) }; // 4115711002
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_SubdominantSpecies_Inner = { "SubdominantSpecies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronSpeciesData, METADATA_PARAMS(0, nullptr) }; // 4115711002
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_SubdominantSpecies = { "SubdominantSpecies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, SubdominantSpecies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubdominantSpecies_MetaData), NewProp_SubdominantSpecies_MetaData) }; // 4115711002
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_RareSpecies_Inner = { "RareSpecies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronSpeciesData, METADATA_PARAMS(0, nullptr) }; // 4115711002
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_RareSpecies = { "RareSpecies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, RareSpecies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RareSpecies_MetaData), NewProp_RareSpecies_MetaData) }; // 4115711002
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_EcosystemRules_Inner = { "EcosystemRules", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronEcosystemRuleData, METADATA_PARAMS(0, nullptr) }; // 351304892
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_EcosystemRules = { "EcosystemRules", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, EcosystemRules), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EcosystemRules_MetaData), NewProp_EcosystemRules_MetaData) }; // 351304892
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_TransitionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_TransitionType = { "TransitionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, TransitionType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTransitionZoneType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionType_MetaData), NewProp_TransitionType_MetaData) }; // 2573166319
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_TransitionWidth = { "TransitionWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, TransitionWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionWidth_MetaData), NewProp_TransitionWidth_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_AdjacentBiomes_Inner = { "AdjacentBiomes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_AdjacentBiomes = { "AdjacentBiomes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, AdjacentBiomes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdjacentBiomes_MetaData), NewProp_AdjacentBiomes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_MinBiomeSize = { "MinBiomeSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, MinBiomeSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinBiomeSize_MetaData), NewProp_MinBiomeSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_MaxBiomeSize = { "MaxBiomeSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, MaxBiomeSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBiomeSize_MetaData), NewProp_MaxBiomeSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomeDensity = { "BiomeDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, BiomeDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeDensity_MetaData), NewProp_BiomeDensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bSeasonalChanges_SetBit(void* Obj)
{
	((FAuracronBiomeDefinition*)Obj)->bSeasonalChanges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bSeasonalChanges = { "bSeasonalChanges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeDefinition), &Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bSeasonalChanges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSeasonalChanges_MetaData), NewProp_bSeasonalChanges_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_SeasonalIntensity = { "SeasonalIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, SeasonalIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalIntensity_MetaData), NewProp_SeasonalIntensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bSuccessionEnabled_SetBit(void* Obj)
{
	((FAuracronBiomeDefinition*)Obj)->bSuccessionEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bSuccessionEnabled = { "bSuccessionEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeDefinition), &Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bSuccessionEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccessionEnabled_MetaData), NewProp_bSuccessionEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_SuccessionRate = { "SuccessionRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, SuccessionRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuccessionRate_MetaData), NewProp_SuccessionRate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bDisturbanceEvents_SetBit(void* Obj)
{
	((FAuracronBiomeDefinition*)Obj)->bDisturbanceEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bDisturbanceEvents = { "bDisturbanceEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeDefinition), &Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bDisturbanceEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDisturbanceEvents_MetaData), NewProp_bDisturbanceEvents_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_DisturbanceFrequency = { "DisturbanceFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, DisturbanceFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisturbanceFrequency_MetaData), NewProp_DisturbanceFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_DisturbanceIntensity = { "DisturbanceIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, DisturbanceIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisturbanceIntensity_MetaData), NewProp_DisturbanceIntensity_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomePCGGraph = { "BiomePCGGraph", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, BiomePCGGraph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomePCGGraph_MetaData), NewProp_BiomePCGGraph_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_PCGParameters_ValueProp = { "PCGParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_PCGParameters_Key_KeyProp = { "PCGParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_PCGParameters = { "PCGParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeDefinition, PCGParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGParameters_MetaData), NewProp_PCGParameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomeType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_ClimateType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_ClimateType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_ClimateData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_DominantSpecies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_DominantSpecies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_SubdominantSpecies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_SubdominantSpecies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_RareSpecies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_RareSpecies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_EcosystemRules_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_EcosystemRules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_TransitionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_TransitionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_TransitionWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_AdjacentBiomes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_AdjacentBiomes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_MinBiomeSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_MaxBiomeSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomeDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bSeasonalChanges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_SeasonalIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bSuccessionEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_SuccessionRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_bDisturbanceEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_DisturbanceFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_DisturbanceIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_BiomePCGGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_PCGParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_PCGParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewProp_PCGParameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronBiomeDefinition",
	Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::PropPointers),
	sizeof(FAuracronBiomeDefinition),
	alignof(FAuracronBiomeDefinition),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronBiomeDefinition()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBiomeDefinition.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronBiomeDefinition.InnerSingleton, Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBiomeDefinition.InnerSingleton;
}
// ********** End ScriptStruct FAuracronBiomeDefinition ********************************************

// ********** Begin ScriptStruct FAuracronBiomeConfiguration ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronBiomeConfiguration;
class UScriptStruct* FAuracronBiomeConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBiomeConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronBiomeConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronBiomeConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronBiomeConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBiomeConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Biome Configuration\n * Configuration for the biome system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome Configuration\nConfiguration for the biome system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBiomeSystem_MetaData[] = {
		{ "Category", "Biome System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableClimateSimulation_MetaData[] = {
		{ "Category", "Biome System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEcosystemRules_MetaData[] = {
		{ "Category", "Biome System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSpeciesInteractions_MetaData[] = {
		{ "Category", "Biome System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSuccession_MetaData[] = {
		{ "Category", "Biome System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDisturbanceEvents_MetaData[] = {
		{ "Category", "Biome System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimulationTimeStep_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClimateUpdateInterval_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EcosystemUpdateInterval_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeciesUpdateInterval_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSimulatedBiomes_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSpeciesPerBiome_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimulationRadius_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncSimulation_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentSimulations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBiomeVisualization_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowClimateData_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowSpeciesDistribution_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowEcosystemRules_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowTransitionZones_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableBiomeSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBiomeSystem;
	static void NewProp_bEnableClimateSimulation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableClimateSimulation;
	static void NewProp_bEnableEcosystemRules_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEcosystemRules;
	static void NewProp_bEnableSpeciesInteractions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSpeciesInteractions;
	static void NewProp_bEnableSuccession_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSuccession;
	static void NewProp_bEnableDisturbanceEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDisturbanceEvents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimulationTimeStep;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClimateUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EcosystemUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeciesUpdateInterval;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSimulatedBiomes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSpeciesPerBiome;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimulationRadius;
	static void NewProp_bEnableAsyncSimulation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncSimulation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentSimulations;
	static void NewProp_bEnableBiomeVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBiomeVisualization;
	static void NewProp_bShowClimateData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowClimateData;
	static void NewProp_bShowSpeciesDistribution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowSpeciesDistribution;
	static void NewProp_bShowEcosystemRules_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowEcosystemRules;
	static void NewProp_bShowTransitionZones_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowTransitionZones;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronBiomeConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableBiomeSystem_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bEnableBiomeSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableBiomeSystem = { "bEnableBiomeSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableBiomeSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBiomeSystem_MetaData), NewProp_bEnableBiomeSystem_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableClimateSimulation_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bEnableClimateSimulation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableClimateSimulation = { "bEnableClimateSimulation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableClimateSimulation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableClimateSimulation_MetaData), NewProp_bEnableClimateSimulation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableEcosystemRules_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bEnableEcosystemRules = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableEcosystemRules = { "bEnableEcosystemRules", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableEcosystemRules_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEcosystemRules_MetaData), NewProp_bEnableEcosystemRules_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableSpeciesInteractions_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bEnableSpeciesInteractions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableSpeciesInteractions = { "bEnableSpeciesInteractions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableSpeciesInteractions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSpeciesInteractions_MetaData), NewProp_bEnableSpeciesInteractions_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableSuccession_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bEnableSuccession = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableSuccession = { "bEnableSuccession", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableSuccession_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSuccession_MetaData), NewProp_bEnableSuccession_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableDisturbanceEvents_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bEnableDisturbanceEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableDisturbanceEvents = { "bEnableDisturbanceEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableDisturbanceEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDisturbanceEvents_MetaData), NewProp_bEnableDisturbanceEvents_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_SimulationTimeStep = { "SimulationTimeStep", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfiguration, SimulationTimeStep), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimulationTimeStep_MetaData), NewProp_SimulationTimeStep_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_ClimateUpdateInterval = { "ClimateUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfiguration, ClimateUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClimateUpdateInterval_MetaData), NewProp_ClimateUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_EcosystemUpdateInterval = { "EcosystemUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfiguration, EcosystemUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EcosystemUpdateInterval_MetaData), NewProp_EcosystemUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_SpeciesUpdateInterval = { "SpeciesUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfiguration, SpeciesUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeciesUpdateInterval_MetaData), NewProp_SpeciesUpdateInterval_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_MaxSimulatedBiomes = { "MaxSimulatedBiomes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfiguration, MaxSimulatedBiomes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSimulatedBiomes_MetaData), NewProp_MaxSimulatedBiomes_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_MaxSpeciesPerBiome = { "MaxSpeciesPerBiome", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfiguration, MaxSpeciesPerBiome), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSpeciesPerBiome_MetaData), NewProp_MaxSpeciesPerBiome_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_SimulationRadius = { "SimulationRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfiguration, SimulationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimulationRadius_MetaData), NewProp_SimulationRadius_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableAsyncSimulation_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bEnableAsyncSimulation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableAsyncSimulation = { "bEnableAsyncSimulation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableAsyncSimulation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncSimulation_MetaData), NewProp_bEnableAsyncSimulation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_MaxConcurrentSimulations = { "MaxConcurrentSimulations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfiguration, MaxConcurrentSimulations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentSimulations_MetaData), NewProp_MaxConcurrentSimulations_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableBiomeVisualization_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bEnableBiomeVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableBiomeVisualization = { "bEnableBiomeVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableBiomeVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBiomeVisualization_MetaData), NewProp_bEnableBiomeVisualization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowClimateData_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bShowClimateData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowClimateData = { "bShowClimateData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowClimateData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowClimateData_MetaData), NewProp_bShowClimateData_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowSpeciesDistribution_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bShowSpeciesDistribution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowSpeciesDistribution = { "bShowSpeciesDistribution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowSpeciesDistribution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowSpeciesDistribution_MetaData), NewProp_bShowSpeciesDistribution_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowEcosystemRules_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bShowEcosystemRules = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowEcosystemRules = { "bShowEcosystemRules", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowEcosystemRules_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowEcosystemRules_MetaData), NewProp_bShowEcosystemRules_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowTransitionZones_SetBit(void* Obj)
{
	((FAuracronBiomeConfiguration*)Obj)->bShowTransitionZones = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowTransitionZones = { "bShowTransitionZones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBiomeConfiguration), &Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowTransitionZones_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowTransitionZones_MetaData), NewProp_bShowTransitionZones_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableBiomeSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableClimateSimulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableEcosystemRules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableSpeciesInteractions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableSuccession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableDisturbanceEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_SimulationTimeStep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_ClimateUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_EcosystemUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_SpeciesUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_MaxSimulatedBiomes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_MaxSpeciesPerBiome,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_SimulationRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableAsyncSimulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_MaxConcurrentSimulations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bEnableBiomeVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowClimateData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowSpeciesDistribution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowEcosystemRules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewProp_bShowTransitionZones,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronBiomeConfiguration",
	Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::PropPointers),
	sizeof(FAuracronBiomeConfiguration),
	alignof(FAuracronBiomeConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronBiomeConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBiomeConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronBiomeConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBiomeConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronBiomeConfiguration *****************************************

// ********** Begin Delegate FOnBiomeRegistered ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics
{
	struct AuracronFoliageBiomeManager_eventOnBiomeRegistered_Parms
	{
		FString BiomeId;
		FAuracronBiomeDefinition BiomeDefinition;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BiomeDefinition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnBiomeRegistered_Parms, BiomeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::NewProp_BiomeDefinition = { "BiomeDefinition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnBiomeRegistered_Parms, BiomeDefinition), Z_Construct_UScriptStruct_FAuracronBiomeDefinition, METADATA_PARAMS(0, nullptr) }; // 2437733546
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::NewProp_BiomeDefinition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "OnBiomeRegistered__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::AuracronFoliageBiomeManager_eventOnBiomeRegistered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::AuracronFoliageBiomeManager_eventOnBiomeRegistered_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageBiomeManager::FOnBiomeRegistered_DelegateWrapper(const FMulticastScriptDelegate& OnBiomeRegistered, const FString& BiomeId, FAuracronBiomeDefinition BiomeDefinition)
{
	struct AuracronFoliageBiomeManager_eventOnBiomeRegistered_Parms
	{
		FString BiomeId;
		FAuracronBiomeDefinition BiomeDefinition;
	};
	AuracronFoliageBiomeManager_eventOnBiomeRegistered_Parms Parms;
	Parms.BiomeId=BiomeId;
	Parms.BiomeDefinition=BiomeDefinition;
	OnBiomeRegistered.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBiomeRegistered ******************************************************

// ********** Begin Delegate FOnBiomeUnregistered **************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics
{
	struct AuracronFoliageBiomeManager_eventOnBiomeUnregistered_Parms
	{
		FString BiomeId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnBiomeUnregistered_Parms, BiomeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::NewProp_BiomeId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "OnBiomeUnregistered__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::AuracronFoliageBiomeManager_eventOnBiomeUnregistered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::AuracronFoliageBiomeManager_eventOnBiomeUnregistered_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageBiomeManager::FOnBiomeUnregistered_DelegateWrapper(const FMulticastScriptDelegate& OnBiomeUnregistered, const FString& BiomeId)
{
	struct AuracronFoliageBiomeManager_eventOnBiomeUnregistered_Parms
	{
		FString BiomeId;
	};
	AuracronFoliageBiomeManager_eventOnBiomeUnregistered_Parms Parms;
	Parms.BiomeId=BiomeId;
	OnBiomeUnregistered.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBiomeUnregistered ****************************************************

// ********** Begin Delegate FOnSpeciesAdded *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics
{
	struct AuracronFoliageBiomeManager_eventOnSpeciesAdded_Parms
	{
		FString BiomeId;
		FString SpeciesId;
		FAuracronSpeciesData SpeciesData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SpeciesId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpeciesData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnSpeciesAdded_Parms, BiomeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::NewProp_SpeciesId = { "SpeciesId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnSpeciesAdded_Parms, SpeciesId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::NewProp_SpeciesData = { "SpeciesData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnSpeciesAdded_Parms, SpeciesData), Z_Construct_UScriptStruct_FAuracronSpeciesData, METADATA_PARAMS(0, nullptr) }; // 4115711002
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::NewProp_SpeciesId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::NewProp_SpeciesData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "OnSpeciesAdded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::AuracronFoliageBiomeManager_eventOnSpeciesAdded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::AuracronFoliageBiomeManager_eventOnSpeciesAdded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageBiomeManager::FOnSpeciesAdded_DelegateWrapper(const FMulticastScriptDelegate& OnSpeciesAdded, const FString& BiomeId, const FString& SpeciesId, FAuracronSpeciesData SpeciesData)
{
	struct AuracronFoliageBiomeManager_eventOnSpeciesAdded_Parms
	{
		FString BiomeId;
		FString SpeciesId;
		FAuracronSpeciesData SpeciesData;
	};
	AuracronFoliageBiomeManager_eventOnSpeciesAdded_Parms Parms;
	Parms.BiomeId=BiomeId;
	Parms.SpeciesId=SpeciesId;
	Parms.SpeciesData=SpeciesData;
	OnSpeciesAdded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSpeciesAdded *********************************************************

// ********** Begin Delegate FOnSpeciesRemoved *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics
{
	struct AuracronFoliageBiomeManager_eventOnSpeciesRemoved_Parms
	{
		FString BiomeId;
		FString SpeciesId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SpeciesId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnSpeciesRemoved_Parms, BiomeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::NewProp_SpeciesId = { "SpeciesId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnSpeciesRemoved_Parms, SpeciesId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::NewProp_SpeciesId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "OnSpeciesRemoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::AuracronFoliageBiomeManager_eventOnSpeciesRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::AuracronFoliageBiomeManager_eventOnSpeciesRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageBiomeManager::FOnSpeciesRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnSpeciesRemoved, const FString& BiomeId, const FString& SpeciesId)
{
	struct AuracronFoliageBiomeManager_eventOnSpeciesRemoved_Parms
	{
		FString BiomeId;
		FString SpeciesId;
	};
	AuracronFoliageBiomeManager_eventOnSpeciesRemoved_Parms Parms;
	Parms.BiomeId=BiomeId;
	Parms.SpeciesId=SpeciesId;
	OnSpeciesRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSpeciesRemoved *******************************************************

// ********** Begin Delegate FOnDisturbanceEvent ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics
{
	struct AuracronFoliageBiomeManager_eventOnDisturbanceEvent_Parms
	{
		FString BiomeId;
		FVector Location;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnDisturbanceEvent_Parms, BiomeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnDisturbanceEvent_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventOnDisturbanceEvent_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "OnDisturbanceEvent__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::AuracronFoliageBiomeManager_eventOnDisturbanceEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::AuracronFoliageBiomeManager_eventOnDisturbanceEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageBiomeManager::FOnDisturbanceEvent_DelegateWrapper(const FMulticastScriptDelegate& OnDisturbanceEvent, const FString& BiomeId, FVector Location, float Intensity)
{
	struct AuracronFoliageBiomeManager_eventOnDisturbanceEvent_Parms
	{
		FString BiomeId;
		FVector Location;
		float Intensity;
	};
	AuracronFoliageBiomeManager_eventOnDisturbanceEvent_Parms Parms;
	Parms.BiomeId=BiomeId;
	Parms.Location=Location;
	Parms.Intensity=Intensity;
	OnDisturbanceEvent.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDisturbanceEvent *****************************************************

// ********** Begin Class UAuracronFoliageBiomeManager Function AddEcosystemRule *******************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics
{
	struct AuracronFoliageBiomeManager_eventAddEcosystemRule_Parms
	{
		FString BiomeId;
		FAuracronEcosystemRuleData RuleData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Ecosystem rules\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ecosystem rules" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RuleData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventAddEcosystemRule_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::NewProp_RuleData = { "RuleData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventAddEcosystemRule_Parms, RuleData), Z_Construct_UScriptStruct_FAuracronEcosystemRuleData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleData_MetaData), NewProp_RuleData_MetaData) }; // 351304892
void Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageBiomeManager_eventAddEcosystemRule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageBiomeManager_eventAddEcosystemRule_Parms), &Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::NewProp_RuleData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "AddEcosystemRule", Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::AuracronFoliageBiomeManager_eventAddEcosystemRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::AuracronFoliageBiomeManager_eventAddEcosystemRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execAddEcosystemRule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_STRUCT_REF(FAuracronEcosystemRuleData,Z_Param_Out_RuleData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddEcosystemRule(Z_Param_BiomeId,Z_Param_Out_RuleData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function AddEcosystemRule *********************

// ********** Begin Class UAuracronFoliageBiomeManager Function AddSpeciesToBiome ******************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics
{
	struct AuracronFoliageBiomeManager_eventAddSpeciesToBiome_Parms
	{
		FString BiomeId;
		FAuracronSpeciesData SpeciesData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Species management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Species management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeciesData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpeciesData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventAddSpeciesToBiome_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::NewProp_SpeciesData = { "SpeciesData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventAddSpeciesToBiome_Parms, SpeciesData), Z_Construct_UScriptStruct_FAuracronSpeciesData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeciesData_MetaData), NewProp_SpeciesData_MetaData) }; // 4115711002
void Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageBiomeManager_eventAddSpeciesToBiome_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageBiomeManager_eventAddSpeciesToBiome_Parms), &Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::NewProp_SpeciesData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "AddSpeciesToBiome", Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::AuracronFoliageBiomeManager_eventAddSpeciesToBiome_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::AuracronFoliageBiomeManager_eventAddSpeciesToBiome_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execAddSpeciesToBiome)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_STRUCT_REF(FAuracronSpeciesData,Z_Param_Out_SpeciesData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddSpeciesToBiome(Z_Param_BiomeId,Z_Param_Out_SpeciesData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function AddSpeciesToBiome ********************

// ********** Begin Class UAuracronFoliageBiomeManager Function DrawDebugVisualization *************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics
{
	struct AuracronFoliageBiomeManager_eventDrawDebugVisualization_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventDrawDebugVisualization_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "DrawDebugVisualization", Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::AuracronFoliageBiomeManager_eventDrawDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::AuracronFoliageBiomeManager_eventDrawDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execDrawDebugVisualization)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugVisualization(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function DrawDebugVisualization ***************

// ********** Begin Class UAuracronFoliageBiomeManager Function EnableDebugVisualization ***********
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageBiomeManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageBiomeManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageBiomeManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::AuracronFoliageBiomeManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::AuracronFoliageBiomeManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function EnableDebugVisualization *************

// ********** Begin Class UAuracronFoliageBiomeManager Function GenerateBiomePCG *******************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics
{
	struct AuracronFoliageBiomeManager_eventGenerateBiomePCG_Parms
	{
		FString BiomeId;
		FBox Area;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGenerateBiomePCG_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGenerateBiomePCG_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::NewProp_Area,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GenerateBiomePCG", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::AuracronFoliageBiomeManager_eventGenerateBiomePCG_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::AuracronFoliageBiomeManager_eventGenerateBiomePCG_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGenerateBiomePCG)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateBiomePCG(Z_Param_BiomeId,Z_Param_Out_Area);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GenerateBiomePCG *********************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetAdjacentBiomes ******************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics
{
	struct AuracronFoliageBiomeManager_eventGetAdjacentBiomes_Parms
	{
		FString BiomeId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetAdjacentBiomes_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetAdjacentBiomes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetAdjacentBiomes", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::AuracronFoliageBiomeManager_eventGetAdjacentBiomes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::AuracronFoliageBiomeManager_eventGetAdjacentBiomes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetAdjacentBiomes)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAdjacentBiomes(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetAdjacentBiomes ********************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetAllBiomes ***********************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics
{
	struct AuracronFoliageBiomeManager_eventGetAllBiomes_Parms
	{
		TArray<FAuracronBiomeDefinition> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronBiomeDefinition, METADATA_PARAMS(0, nullptr) }; // 2437733546
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetAllBiomes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2437733546
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetAllBiomes", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::AuracronFoliageBiomeManager_eventGetAllBiomes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::AuracronFoliageBiomeManager_eventGetAllBiomes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetAllBiomes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronBiomeDefinition>*)Z_Param__Result=P_THIS->GetAllBiomes();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetAllBiomes *************************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetBiome ***************************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics
{
	struct AuracronFoliageBiomeManager_eventGetBiome_Parms
	{
		FString BiomeId;
		FAuracronBiomeDefinition ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiome_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiome_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronBiomeDefinition, METADATA_PARAMS(0, nullptr) }; // 2437733546
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetBiome", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::AuracronFoliageBiomeManager_eventGetBiome_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::AuracronFoliageBiomeManager_eventGetBiome_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetBiome)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronBiomeDefinition*)Z_Param__Result=P_THIS->GetBiome(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetBiome *****************************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetBiomeAtLocation *****************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics
{
	struct AuracronFoliageBiomeManager_eventGetBiomeAtLocation_Parms
	{
		FVector Location;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Biome queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiomeAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiomeAtLocation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetBiomeAtLocation", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::AuracronFoliageBiomeManager_eventGetBiomeAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::AuracronFoliageBiomeManager_eventGetBiomeAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetBiomeAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetBiomeAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetBiomeAtLocation *******************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetBiomeInfluence ******************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics
{
	struct AuracronFoliageBiomeManager_eventGetBiomeInfluence_Parms
	{
		FString BiomeId;
		FVector Location;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiomeInfluence_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiomeInfluence_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiomeInfluence_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetBiomeInfluence", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::AuracronFoliageBiomeManager_eventGetBiomeInfluence_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::AuracronFoliageBiomeManager_eventGetBiomeInfluence_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetBiomeInfluence)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetBiomeInfluence(Z_Param_BiomeId,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetBiomeInfluence ********************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetBiomePCGComponent ***************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics
{
	struct AuracronFoliageBiomeManager_eventGetBiomePCGComponent_Parms
	{
		FString BiomeId;
		UPCGComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiomePCGComponent_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiomePCGComponent_Parms, ReturnValue), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetBiomePCGComponent", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::AuracronFoliageBiomeManager_eventGetBiomePCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::AuracronFoliageBiomeManager_eventGetBiomePCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetBiomePCGComponent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGComponent**)Z_Param__Result=P_THIS->GetBiomePCGComponent(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetBiomePCGComponent *****************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetBiomesInArea ********************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics
{
	struct AuracronFoliageBiomeManager_eventGetBiomesInArea_Parms
	{
		FBox Area;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiomesInArea_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiomesInArea_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::NewProp_Area,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetBiomesInArea", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::AuracronFoliageBiomeManager_eventGetBiomesInArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::AuracronFoliageBiomeManager_eventGetBiomesInArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetBiomesInArea)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetBiomesInArea(Z_Param_Out_Area);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetBiomesInArea **********************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetBiomeStatistics *****************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics
{
	struct AuracronFoliageBiomeManager_eventGetBiomeStatistics_Parms
	{
		TMap<FString,int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetBiomeStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetBiomeStatistics", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::AuracronFoliageBiomeManager_eventGetBiomeStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::AuracronFoliageBiomeManager_eventGetBiomeStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetBiomeStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,int32>*)Z_Param__Result=P_THIS->GetBiomeStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetBiomeStatistics *******************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetClimateAtLocation ***************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics
{
	struct AuracronFoliageBiomeManager_eventGetClimateAtLocation_Parms
	{
		FVector Location;
		FAuracronClimateData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Climate simulation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Climate simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetClimateAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetClimateAtLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronClimateData, METADATA_PARAMS(0, nullptr) }; // 460639699
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetClimateAtLocation", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::AuracronFoliageBiomeManager_eventGetClimateAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::AuracronFoliageBiomeManager_eventGetClimateAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetClimateAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronClimateData*)Z_Param__Result=P_THIS->GetClimateAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetClimateAtLocation *****************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetConfiguration *******************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics
{
	struct AuracronFoliageBiomeManager_eventGetConfiguration_Parms
	{
		FAuracronBiomeConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronBiomeConfiguration, METADATA_PARAMS(0, nullptr) }; // 357011794
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::AuracronFoliageBiomeManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::AuracronFoliageBiomeManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronBiomeConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetConfiguration *********************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetEcosystemRules ******************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics
{
	struct AuracronFoliageBiomeManager_eventGetEcosystemRules_Parms
	{
		FString BiomeId;
		TArray<FAuracronEcosystemRuleData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetEcosystemRules_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronEcosystemRuleData, METADATA_PARAMS(0, nullptr) }; // 351304892
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetEcosystemRules_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 351304892
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetEcosystemRules", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::AuracronFoliageBiomeManager_eventGetEcosystemRules_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::AuracronFoliageBiomeManager_eventGetEcosystemRules_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetEcosystemRules)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronEcosystemRuleData>*)Z_Param__Result=P_THIS->GetEcosystemRules(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetEcosystemRules ********************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetInstance ************************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics
{
	struct AuracronFoliageBiomeManager_eventGetInstance_Parms
	{
		UAuracronFoliageBiomeManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageBiomeManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::AuracronFoliageBiomeManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::AuracronFoliageBiomeManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageBiomeManager**)Z_Param__Result=UAuracronFoliageBiomeManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetInstance **************************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetSpeciesAbundance ****************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics
{
	struct AuracronFoliageBiomeManager_eventGetSpeciesAbundance_Parms
	{
		FString BiomeId;
		FString SpeciesId;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeciesId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SpeciesId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetSpeciesAbundance_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::NewProp_SpeciesId = { "SpeciesId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetSpeciesAbundance_Parms, SpeciesId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeciesId_MetaData), NewProp_SpeciesId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetSpeciesAbundance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::NewProp_SpeciesId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetSpeciesAbundance", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::AuracronFoliageBiomeManager_eventGetSpeciesAbundance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::AuracronFoliageBiomeManager_eventGetSpeciesAbundance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetSpeciesAbundance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_SpeciesId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetSpeciesAbundance(Z_Param_BiomeId,Z_Param_SpeciesId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetSpeciesAbundance ******************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetSpeciesAtLocation ***************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics
{
	struct AuracronFoliageBiomeManager_eventGetSpeciesAtLocation_Parms
	{
		FVector Location;
		TArray<FAuracronSpeciesData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetSpeciesAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronSpeciesData, METADATA_PARAMS(0, nullptr) }; // 4115711002
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetSpeciesAtLocation_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4115711002
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetSpeciesAtLocation", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::AuracronFoliageBiomeManager_eventGetSpeciesAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::AuracronFoliageBiomeManager_eventGetSpeciesAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetSpeciesAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronSpeciesData>*)Z_Param__Result=P_THIS->GetSpeciesAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetSpeciesAtLocation *****************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetSpeciesInBiome ******************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics
{
	struct AuracronFoliageBiomeManager_eventGetSpeciesInBiome_Parms
	{
		FString BiomeId;
		TArray<FAuracronSpeciesData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetSpeciesInBiome_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronSpeciesData, METADATA_PARAMS(0, nullptr) }; // 4115711002
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetSpeciesInBiome_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4115711002
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetSpeciesInBiome", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::AuracronFoliageBiomeManager_eventGetSpeciesInBiome_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::AuracronFoliageBiomeManager_eventGetSpeciesInBiome_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetSpeciesInBiome)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronSpeciesData>*)Z_Param__Result=P_THIS->GetSpeciesInBiome(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetSpeciesInBiome ********************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetTotalBiomeCount *****************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics
{
	struct AuracronFoliageBiomeManager_eventGetTotalBiomeCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetTotalBiomeCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetTotalBiomeCount", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::AuracronFoliageBiomeManager_eventGetTotalBiomeCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::AuracronFoliageBiomeManager_eventGetTotalBiomeCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetTotalBiomeCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalBiomeCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetTotalBiomeCount *******************

// ********** Begin Class UAuracronFoliageBiomeManager Function GetTotalSpeciesCount ***************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics
{
	struct AuracronFoliageBiomeManager_eventGetTotalSpeciesCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventGetTotalSpeciesCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "GetTotalSpeciesCount", Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::AuracronFoliageBiomeManager_eventGetTotalSpeciesCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::AuracronFoliageBiomeManager_eventGetTotalSpeciesCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execGetTotalSpeciesCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalSpeciesCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function GetTotalSpeciesCount *****************

// ********** Begin Class UAuracronFoliageBiomeManager Function Initialize *************************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics
{
	struct AuracronFoliageBiomeManager_eventInitialize_Parms
	{
		FAuracronBiomeConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronBiomeConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 357011794
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::AuracronFoliageBiomeManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::AuracronFoliageBiomeManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronBiomeConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function Initialize ***************************

// ********** Begin Class UAuracronFoliageBiomeManager Function IsDebugVisualizationEnabled ********
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageBiomeManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageBiomeManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageBiomeManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageBiomeManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageBiomeManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function IsDebugVisualizationEnabled **********

// ********** Begin Class UAuracronFoliageBiomeManager Function IsInitialized **********************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics
{
	struct AuracronFoliageBiomeManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageBiomeManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageBiomeManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::AuracronFoliageBiomeManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::AuracronFoliageBiomeManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function IsInitialized ************************

// ********** Begin Class UAuracronFoliageBiomeManager Function RegisterBiome **********************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics
{
	struct AuracronFoliageBiomeManager_eventRegisterBiome_Parms
	{
		FAuracronBiomeDefinition BiomeDefinition;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Biome management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeDefinition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BiomeDefinition;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::NewProp_BiomeDefinition = { "BiomeDefinition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventRegisterBiome_Parms, BiomeDefinition), Z_Construct_UScriptStruct_FAuracronBiomeDefinition, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeDefinition_MetaData), NewProp_BiomeDefinition_MetaData) }; // 2437733546
void Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageBiomeManager_eventRegisterBiome_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageBiomeManager_eventRegisterBiome_Parms), &Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::NewProp_BiomeDefinition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "RegisterBiome", Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::AuracronFoliageBiomeManager_eventRegisterBiome_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::AuracronFoliageBiomeManager_eventRegisterBiome_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execRegisterBiome)
{
	P_GET_STRUCT_REF(FAuracronBiomeDefinition,Z_Param_Out_BiomeDefinition);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterBiome(Z_Param_Out_BiomeDefinition);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function RegisterBiome ************************

// ********** Begin Class UAuracronFoliageBiomeManager Function RemoveEcosystemRule ****************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics
{
	struct AuracronFoliageBiomeManager_eventRemoveEcosystemRule_Parms
	{
		FString BiomeId;
		FString RuleId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventRemoveEcosystemRule_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::NewProp_RuleId = { "RuleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventRemoveEcosystemRule_Parms, RuleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleId_MetaData), NewProp_RuleId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageBiomeManager_eventRemoveEcosystemRule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageBiomeManager_eventRemoveEcosystemRule_Parms), &Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::NewProp_RuleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "RemoveEcosystemRule", Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::AuracronFoliageBiomeManager_eventRemoveEcosystemRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::AuracronFoliageBiomeManager_eventRemoveEcosystemRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execRemoveEcosystemRule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_RuleId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveEcosystemRule(Z_Param_BiomeId,Z_Param_RuleId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function RemoveEcosystemRule ******************

// ********** Begin Class UAuracronFoliageBiomeManager Function RemoveSpeciesFromBiome *************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics
{
	struct AuracronFoliageBiomeManager_eventRemoveSpeciesFromBiome_Parms
	{
		FString BiomeId;
		FString SpeciesId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeciesId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SpeciesId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventRemoveSpeciesFromBiome_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::NewProp_SpeciesId = { "SpeciesId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventRemoveSpeciesFromBiome_Parms, SpeciesId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeciesId_MetaData), NewProp_SpeciesId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageBiomeManager_eventRemoveSpeciesFromBiome_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageBiomeManager_eventRemoveSpeciesFromBiome_Parms), &Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::NewProp_SpeciesId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "RemoveSpeciesFromBiome", Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::AuracronFoliageBiomeManager_eventRemoveSpeciesFromBiome_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::AuracronFoliageBiomeManager_eventRemoveSpeciesFromBiome_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execRemoveSpeciesFromBiome)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_PROPERTY(FStrProperty,Z_Param_SpeciesId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveSpeciesFromBiome(Z_Param_BiomeId,Z_Param_SpeciesId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function RemoveSpeciesFromBiome ***************

// ********** Begin Class UAuracronFoliageBiomeManager Function SetConfiguration *******************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics
{
	struct AuracronFoliageBiomeManager_eventSetConfiguration_Parms
	{
		FAuracronBiomeConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronBiomeConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 357011794
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::AuracronFoliageBiomeManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::AuracronFoliageBiomeManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronBiomeConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function SetConfiguration *********************

// ********** Begin Class UAuracronFoliageBiomeManager Function SetGlobalClimateParameters *********
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics
{
	struct AuracronFoliageBiomeManager_eventSetGlobalClimateParameters_Parms
	{
		float GlobalTemperature;
		float GlobalPrecipitation;
		float GlobalHumidity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalTemperature;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalPrecipitation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalHumidity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::NewProp_GlobalTemperature = { "GlobalTemperature", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventSetGlobalClimateParameters_Parms, GlobalTemperature), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::NewProp_GlobalPrecipitation = { "GlobalPrecipitation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventSetGlobalClimateParameters_Parms, GlobalPrecipitation), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::NewProp_GlobalHumidity = { "GlobalHumidity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventSetGlobalClimateParameters_Parms, GlobalHumidity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::NewProp_GlobalTemperature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::NewProp_GlobalPrecipitation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::NewProp_GlobalHumidity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "SetGlobalClimateParameters", Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::AuracronFoliageBiomeManager_eventSetGlobalClimateParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::AuracronFoliageBiomeManager_eventSetGlobalClimateParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execSetGlobalClimateParameters)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_GlobalTemperature);
	P_GET_PROPERTY(FFloatProperty,Z_Param_GlobalPrecipitation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_GlobalHumidity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGlobalClimateParameters(Z_Param_GlobalTemperature,Z_Param_GlobalPrecipitation,Z_Param_GlobalHumidity);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function SetGlobalClimateParameters ***********

// ********** Begin Class UAuracronFoliageBiomeManager Function Shutdown ***************************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function Shutdown *****************************

// ********** Begin Class UAuracronFoliageBiomeManager Function SimulateSeasonalChanges ************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics
{
	struct AuracronFoliageBiomeManager_eventSimulateSeasonalChanges_Parms
	{
		float SeasonProgress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonProgress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::NewProp_SeasonProgress = { "SeasonProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventSimulateSeasonalChanges_Parms, SeasonProgress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::NewProp_SeasonProgress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "SimulateSeasonalChanges", Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::AuracronFoliageBiomeManager_eventSimulateSeasonalChanges_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::AuracronFoliageBiomeManager_eventSimulateSeasonalChanges_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execSimulateSeasonalChanges)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_SeasonProgress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SimulateSeasonalChanges(Z_Param_SeasonProgress);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function SimulateSeasonalChanges **************

// ********** Begin Class UAuracronFoliageBiomeManager Function Tick *******************************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics
{
	struct AuracronFoliageBiomeManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::AuracronFoliageBiomeManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::AuracronFoliageBiomeManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function Tick *********************************

// ********** Begin Class UAuracronFoliageBiomeManager Function TriggerDisturbanceEvent ************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics
{
	struct AuracronFoliageBiomeManager_eventTriggerDisturbanceEvent_Parms
	{
		FString BiomeId;
		float Intensity;
		FVector Location;
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventTriggerDisturbanceEvent_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventTriggerDisturbanceEvent_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventTriggerDisturbanceEvent_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventTriggerDisturbanceEvent_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "TriggerDisturbanceEvent", Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::AuracronFoliageBiomeManager_eventTriggerDisturbanceEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::AuracronFoliageBiomeManager_eventTriggerDisturbanceEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execTriggerDisturbanceEvent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerDisturbanceEvent(Z_Param_BiomeId,Z_Param_Intensity,Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function TriggerDisturbanceEvent **************

// ********** Begin Class UAuracronFoliageBiomeManager Function UnregisterBiome ********************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics
{
	struct AuracronFoliageBiomeManager_eventUnregisterBiome_Parms
	{
		FString BiomeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventUnregisterBiome_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageBiomeManager_eventUnregisterBiome_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageBiomeManager_eventUnregisterBiome_Parms), &Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "UnregisterBiome", Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::AuracronFoliageBiomeManager_eventUnregisterBiome_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::AuracronFoliageBiomeManager_eventUnregisterBiome_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execUnregisterBiome)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterBiome(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function UnregisterBiome **********************

// ********** Begin Class UAuracronFoliageBiomeManager Function UpdateBiome ************************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics
{
	struct AuracronFoliageBiomeManager_eventUpdateBiome_Parms
	{
		FAuracronBiomeDefinition BiomeDefinition;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeDefinition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BiomeDefinition;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::NewProp_BiomeDefinition = { "BiomeDefinition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventUpdateBiome_Parms, BiomeDefinition), Z_Construct_UScriptStruct_FAuracronBiomeDefinition, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeDefinition_MetaData), NewProp_BiomeDefinition_MetaData) }; // 2437733546
void Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageBiomeManager_eventUpdateBiome_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageBiomeManager_eventUpdateBiome_Parms), &Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::NewProp_BiomeDefinition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "UpdateBiome", Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::AuracronFoliageBiomeManager_eventUpdateBiome_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::AuracronFoliageBiomeManager_eventUpdateBiome_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execUpdateBiome)
{
	P_GET_STRUCT_REF(FAuracronBiomeDefinition,Z_Param_Out_BiomeDefinition);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateBiome(Z_Param_Out_BiomeDefinition);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function UpdateBiome **************************

// ********** Begin Class UAuracronFoliageBiomeManager Function UpdateBiomePCGParameters ***********
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics
{
	struct AuracronFoliageBiomeManager_eventUpdateBiomePCGParameters_Parms
	{
		FString BiomeId;
		TMap<FString,FString> Parameters;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventUpdateBiomePCGParameters_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventUpdateBiomePCGParameters_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::NewProp_Parameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "UpdateBiomePCGParameters", Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::AuracronFoliageBiomeManager_eventUpdateBiomePCGParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::AuracronFoliageBiomeManager_eventUpdateBiomePCGParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execUpdateBiomePCGParameters)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateBiomePCGParameters(Z_Param_BiomeId,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function UpdateBiomePCGParameters *************

// ********** Begin Class UAuracronFoliageBiomeManager Function UpdateClimateSimulation ************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics
{
	struct AuracronFoliageBiomeManager_eventUpdateClimateSimulation_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventUpdateClimateSimulation_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "UpdateClimateSimulation", Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::AuracronFoliageBiomeManager_eventUpdateClimateSimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::AuracronFoliageBiomeManager_eventUpdateClimateSimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execUpdateClimateSimulation)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateClimateSimulation(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function UpdateClimateSimulation **************

// ********** Begin Class UAuracronFoliageBiomeManager Function UpdateDisturbanceEvents ************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics
{
	struct AuracronFoliageBiomeManager_eventUpdateDisturbanceEvents_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventUpdateDisturbanceEvents_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "UpdateDisturbanceEvents", Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::AuracronFoliageBiomeManager_eventUpdateDisturbanceEvents_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::AuracronFoliageBiomeManager_eventUpdateDisturbanceEvents_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execUpdateDisturbanceEvents)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDisturbanceEvents(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function UpdateDisturbanceEvents **************

// ********** Begin Class UAuracronFoliageBiomeManager Function UpdateEcosystemRules ***************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics
{
	struct AuracronFoliageBiomeManager_eventUpdateEcosystemRules_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventUpdateEcosystemRules_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "UpdateEcosystemRules", Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::AuracronFoliageBiomeManager_eventUpdateEcosystemRules_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::AuracronFoliageBiomeManager_eventUpdateEcosystemRules_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execUpdateEcosystemRules)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateEcosystemRules(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function UpdateEcosystemRules *****************

// ********** Begin Class UAuracronFoliageBiomeManager Function UpdateSuccession *******************
struct Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics
{
	struct AuracronFoliageBiomeManager_eventUpdateSuccession_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Biome Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Succession and disturbance\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Succession and disturbance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageBiomeManager_eventUpdateSuccession_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageBiomeManager, nullptr, "UpdateSuccession", Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::AuracronFoliageBiomeManager_eventUpdateSuccession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::AuracronFoliageBiomeManager_eventUpdateSuccession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageBiomeManager::execUpdateSuccession)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSuccession(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageBiomeManager Function UpdateSuccession *********************

// ********** Begin Class UAuracronFoliageBiomeManager *********************************************
void UAuracronFoliageBiomeManager::StaticRegisterNativesUAuracronFoliageBiomeManager()
{
	UClass* Class = UAuracronFoliageBiomeManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddEcosystemRule", &UAuracronFoliageBiomeManager::execAddEcosystemRule },
		{ "AddSpeciesToBiome", &UAuracronFoliageBiomeManager::execAddSpeciesToBiome },
		{ "DrawDebugVisualization", &UAuracronFoliageBiomeManager::execDrawDebugVisualization },
		{ "EnableDebugVisualization", &UAuracronFoliageBiomeManager::execEnableDebugVisualization },
		{ "GenerateBiomePCG", &UAuracronFoliageBiomeManager::execGenerateBiomePCG },
		{ "GetAdjacentBiomes", &UAuracronFoliageBiomeManager::execGetAdjacentBiomes },
		{ "GetAllBiomes", &UAuracronFoliageBiomeManager::execGetAllBiomes },
		{ "GetBiome", &UAuracronFoliageBiomeManager::execGetBiome },
		{ "GetBiomeAtLocation", &UAuracronFoliageBiomeManager::execGetBiomeAtLocation },
		{ "GetBiomeInfluence", &UAuracronFoliageBiomeManager::execGetBiomeInfluence },
		{ "GetBiomePCGComponent", &UAuracronFoliageBiomeManager::execGetBiomePCGComponent },
		{ "GetBiomesInArea", &UAuracronFoliageBiomeManager::execGetBiomesInArea },
		{ "GetBiomeStatistics", &UAuracronFoliageBiomeManager::execGetBiomeStatistics },
		{ "GetClimateAtLocation", &UAuracronFoliageBiomeManager::execGetClimateAtLocation },
		{ "GetConfiguration", &UAuracronFoliageBiomeManager::execGetConfiguration },
		{ "GetEcosystemRules", &UAuracronFoliageBiomeManager::execGetEcosystemRules },
		{ "GetInstance", &UAuracronFoliageBiomeManager::execGetInstance },
		{ "GetSpeciesAbundance", &UAuracronFoliageBiomeManager::execGetSpeciesAbundance },
		{ "GetSpeciesAtLocation", &UAuracronFoliageBiomeManager::execGetSpeciesAtLocation },
		{ "GetSpeciesInBiome", &UAuracronFoliageBiomeManager::execGetSpeciesInBiome },
		{ "GetTotalBiomeCount", &UAuracronFoliageBiomeManager::execGetTotalBiomeCount },
		{ "GetTotalSpeciesCount", &UAuracronFoliageBiomeManager::execGetTotalSpeciesCount },
		{ "Initialize", &UAuracronFoliageBiomeManager::execInitialize },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageBiomeManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronFoliageBiomeManager::execIsInitialized },
		{ "RegisterBiome", &UAuracronFoliageBiomeManager::execRegisterBiome },
		{ "RemoveEcosystemRule", &UAuracronFoliageBiomeManager::execRemoveEcosystemRule },
		{ "RemoveSpeciesFromBiome", &UAuracronFoliageBiomeManager::execRemoveSpeciesFromBiome },
		{ "SetConfiguration", &UAuracronFoliageBiomeManager::execSetConfiguration },
		{ "SetGlobalClimateParameters", &UAuracronFoliageBiomeManager::execSetGlobalClimateParameters },
		{ "Shutdown", &UAuracronFoliageBiomeManager::execShutdown },
		{ "SimulateSeasonalChanges", &UAuracronFoliageBiomeManager::execSimulateSeasonalChanges },
		{ "Tick", &UAuracronFoliageBiomeManager::execTick },
		{ "TriggerDisturbanceEvent", &UAuracronFoliageBiomeManager::execTriggerDisturbanceEvent },
		{ "UnregisterBiome", &UAuracronFoliageBiomeManager::execUnregisterBiome },
		{ "UpdateBiome", &UAuracronFoliageBiomeManager::execUpdateBiome },
		{ "UpdateBiomePCGParameters", &UAuracronFoliageBiomeManager::execUpdateBiomePCGParameters },
		{ "UpdateClimateSimulation", &UAuracronFoliageBiomeManager::execUpdateClimateSimulation },
		{ "UpdateDisturbanceEvents", &UAuracronFoliageBiomeManager::execUpdateDisturbanceEvents },
		{ "UpdateEcosystemRules", &UAuracronFoliageBiomeManager::execUpdateEcosystemRules },
		{ "UpdateSuccession", &UAuracronFoliageBiomeManager::execUpdateSuccession },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageBiomeManager;
UClass* UAuracronFoliageBiomeManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageBiomeManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageBiomeManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageBiomeManager"),
			Z_Registration_Info_UClass_UAuracronFoliageBiomeManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageBiomeManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageBiomeManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageBiomeManager_NoRegister()
{
	return UAuracronFoliageBiomeManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Biome Manager\n * Manager for the biome system including climate simulation and ecosystem rules\n */" },
#endif
		{ "IncludePath", "AuracronFoliageBiome.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Biome Manager\nManager for the biome system including climate simulation and ecosystem rules" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBiomeRegistered_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBiomeUnregistered_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSpeciesAdded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSpeciesRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDisturbanceEvent_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageBiome.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBiomeRegistered;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBiomeUnregistered;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSpeciesAdded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSpeciesRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDisturbanceEvent;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddEcosystemRule, "AddEcosystemRule" }, // 599036369
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_AddSpeciesToBiome, "AddSpeciesToBiome" }, // 231888178
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_DrawDebugVisualization, "DrawDebugVisualization" }, // 2547814902
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 1507711448
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GenerateBiomePCG, "GenerateBiomePCG" }, // 1147930707
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAdjacentBiomes, "GetAdjacentBiomes" }, // 1655155260
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetAllBiomes, "GetAllBiomes" }, // 1614922289
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiome, "GetBiome" }, // 1394564451
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeAtLocation, "GetBiomeAtLocation" }, // 847225941
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeInfluence, "GetBiomeInfluence" }, // 4076816719
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomePCGComponent, "GetBiomePCGComponent" }, // 1666050517
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomesInArea, "GetBiomesInArea" }, // 231569357
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetBiomeStatistics, "GetBiomeStatistics" }, // 677293772
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetClimateAtLocation, "GetClimateAtLocation" }, // 3614574876
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetConfiguration, "GetConfiguration" }, // 48963762
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetEcosystemRules, "GetEcosystemRules" }, // 1285698362
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetInstance, "GetInstance" }, // 3936985620
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAbundance, "GetSpeciesAbundance" }, // 2140528565
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesAtLocation, "GetSpeciesAtLocation" }, // 1967898789
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetSpeciesInBiome, "GetSpeciesInBiome" }, // 2598443985
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalBiomeCount, "GetTotalBiomeCount" }, // 128405512
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_GetTotalSpeciesCount, "GetTotalSpeciesCount" }, // 4013361419
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_Initialize, "Initialize" }, // 1786841150
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 1961698313
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_IsInitialized, "IsInitialized" }, // 3295902739
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature, "OnBiomeRegistered__DelegateSignature" }, // 221445059
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature, "OnBiomeUnregistered__DelegateSignature" }, // 1541191298
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature, "OnDisturbanceEvent__DelegateSignature" }, // 3311412133
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature, "OnSpeciesAdded__DelegateSignature" }, // 2979817394
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature, "OnSpeciesRemoved__DelegateSignature" }, // 2405145986
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_RegisterBiome, "RegisterBiome" }, // 1911988504
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveEcosystemRule, "RemoveEcosystemRule" }, // 563458589
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_RemoveSpeciesFromBiome, "RemoveSpeciesFromBiome" }, // 3902497876
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetConfiguration, "SetConfiguration" }, // 2169964981
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_SetGlobalClimateParameters, "SetGlobalClimateParameters" }, // 2568481326
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_Shutdown, "Shutdown" }, // 662033736
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_SimulateSeasonalChanges, "SimulateSeasonalChanges" }, // 3762612086
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_Tick, "Tick" }, // 2535591035
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_TriggerDisturbanceEvent, "TriggerDisturbanceEvent" }, // 2381663773
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_UnregisterBiome, "UnregisterBiome" }, // 3810378089
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiome, "UpdateBiome" }, // 3846394640
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateBiomePCGParameters, "UpdateBiomePCGParameters" }, // 2896031638
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateClimateSimulation, "UpdateClimateSimulation" }, // 3969732517
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateDisturbanceEvents, "UpdateDisturbanceEvents" }, // 1085689545
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateEcosystemRules, "UpdateEcosystemRules" }, // 1796907795
		{ &Z_Construct_UFunction_UAuracronFoliageBiomeManager_UpdateSuccession, "UpdateSuccession" }, // 1904100399
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageBiomeManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_OnBiomeRegistered = { "OnBiomeRegistered", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageBiomeManager, OnBiomeRegistered), Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBiomeRegistered_MetaData), NewProp_OnBiomeRegistered_MetaData) }; // 221445059
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_OnBiomeUnregistered = { "OnBiomeUnregistered", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageBiomeManager, OnBiomeUnregistered), Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBiomeUnregistered_MetaData), NewProp_OnBiomeUnregistered_MetaData) }; // 1541191298
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_OnSpeciesAdded = { "OnSpeciesAdded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageBiomeManager, OnSpeciesAdded), Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSpeciesAdded_MetaData), NewProp_OnSpeciesAdded_MetaData) }; // 2979817394
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_OnSpeciesRemoved = { "OnSpeciesRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageBiomeManager, OnSpeciesRemoved), Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSpeciesRemoved_MetaData), NewProp_OnSpeciesRemoved_MetaData) }; // 2405145986
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_OnDisturbanceEvent = { "OnDisturbanceEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageBiomeManager, OnDisturbanceEvent), Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDisturbanceEvent_MetaData), NewProp_OnDisturbanceEvent_MetaData) }; // 3311412133
void Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageBiomeManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageBiomeManager), &Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageBiomeManager, Configuration), Z_Construct_UScriptStruct_FAuracronBiomeConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 357011794
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageBiomeManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_OnBiomeRegistered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_OnBiomeUnregistered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_OnSpeciesAdded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_OnSpeciesRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_OnDisturbanceEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::ClassParams = {
	&UAuracronFoliageBiomeManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageBiomeManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageBiomeManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageBiomeManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageBiomeManager.OuterSingleton;
}
UAuracronFoliageBiomeManager::UAuracronFoliageBiomeManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageBiomeManager);
UAuracronFoliageBiomeManager::~UAuracronFoliageBiomeManager() {}
// ********** End Class UAuracronFoliageBiomeManager ***********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronBiomeType_StaticEnum, TEXT("EAuracronBiomeType"), &Z_Registration_Info_UEnum_EAuracronBiomeType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1433977737U) },
		{ EAuracronClimateType_StaticEnum, TEXT("EAuracronClimateType"), &Z_Registration_Info_UEnum_EAuracronClimateType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2501228809U) },
		{ EAuracronTransitionZoneType_StaticEnum, TEXT("EAuracronTransitionZoneType"), &Z_Registration_Info_UEnum_EAuracronTransitionZoneType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2573166319U) },
		{ EAuracronSpeciesDistributionPattern_StaticEnum, TEXT("EAuracronSpeciesDistributionPattern"), &Z_Registration_Info_UEnum_EAuracronSpeciesDistributionPattern, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3082322166U) },
		{ EAuracronEcosystemRuleType_StaticEnum, TEXT("EAuracronEcosystemRuleType"), &Z_Registration_Info_UEnum_EAuracronEcosystemRuleType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 39030625U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronClimateData::StaticStruct, Z_Construct_UScriptStruct_FAuracronClimateData_Statics::NewStructOps, TEXT("AuracronClimateData"), &Z_Registration_Info_UScriptStruct_FAuracronClimateData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronClimateData), 460639699U) },
		{ FAuracronSpeciesData::StaticStruct, Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics::NewStructOps, TEXT("AuracronSpeciesData"), &Z_Registration_Info_UScriptStruct_FAuracronSpeciesData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSpeciesData), 4115711002U) },
		{ FAuracronEcosystemRuleData::StaticStruct, Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics::NewStructOps, TEXT("AuracronEcosystemRuleData"), &Z_Registration_Info_UScriptStruct_FAuracronEcosystemRuleData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronEcosystemRuleData), 351304892U) },
		{ FAuracronBiomeDefinition::StaticStruct, Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics::NewStructOps, TEXT("AuracronBiomeDefinition"), &Z_Registration_Info_UScriptStruct_FAuracronBiomeDefinition, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronBiomeDefinition), 2437733546U) },
		{ FAuracronBiomeConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics::NewStructOps, TEXT("AuracronBiomeConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronBiomeConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronBiomeConfiguration), 357011794U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageBiomeManager, UAuracronFoliageBiomeManager::StaticClass, TEXT("UAuracronFoliageBiomeManager"), &Z_Registration_Info_UClass_UAuracronFoliageBiomeManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageBiomeManager), 2619915092U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h__Script_AuracronFoliageBridge_4215988004(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
