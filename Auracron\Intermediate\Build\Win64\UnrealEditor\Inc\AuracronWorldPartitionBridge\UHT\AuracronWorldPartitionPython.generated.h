// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionPython.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionPython_generated_h
#error "AuracronWorldPartitionPython.generated.h already included, missing '#pragma once' in AuracronWorldPartitionPython.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionPython_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronWorldPartitionPythonBridge;
enum class EAuracronPythonCallbackType : uint8;
enum class EAuracronPythonExecutionMode : uint8;
struct FAuracronDebugCellInfo;
struct FAuracronGridCell;
struct FAuracronPerformanceMetric;
struct FAuracronPythonCallbackData;
struct FAuracronPythonConfiguration;
struct FAuracronPythonExecutionResult;
struct FAuracronStreamingData;

// ********** Begin ScriptStruct FAuracronPythonConfiguration **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_65_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPythonConfiguration;
// ********** End ScriptStruct FAuracronPythonConfiguration ****************************************

// ********** Begin ScriptStruct FAuracronPythonCallbackData ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_137_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPythonCallbackData;
// ********** End ScriptStruct FAuracronPythonCallbackData *****************************************

// ********** Begin ScriptStruct FAuracronPythonExecutionResult ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_180_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPythonExecutionResult;
// ********** End ScriptStruct FAuracronPythonExecutionResult **************************************

// ********** Begin ScriptStruct FAuracronStreamingData ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_215_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronStreamingData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronStreamingData;
// ********** End ScriptStruct FAuracronStreamingData **********************************************

// ********** Begin Delegate FOnPythonExecutionComplete ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_383_DELEGATE \
static void FOnPythonExecutionComplete_DelegateWrapper(const FMulticastScriptDelegate& OnPythonExecutionComplete, bool bSuccess, FAuracronPythonExecutionResult Result);


// ********** End Delegate FOnPythonExecutionComplete **********************************************

// ********** Begin Delegate FOnPythonError ********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_384_DELEGATE \
static void FOnPythonError_DelegateWrapper(const FMulticastScriptDelegate& OnPythonError, const FString& ErrorMessage, const FString& Context);


// ********** End Delegate FOnPythonError **********************************************************

// ********** Begin Delegate FOnPythonCallback *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_385_DELEGATE \
static void FOnPythonCallback_DelegateWrapper(const FMulticastScriptDelegate& OnPythonCallback, EAuracronPythonCallbackType CallbackType, FAuracronPythonCallbackData CallbackData);


// ********** End Delegate FOnPythonCallback *******************************************************

// ********** Begin Class UAuracronWorldPartitionPythonBridge **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_252_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execClearPythonLogs); \
	DECLARE_FUNCTION(execGetPythonLogs); \
	DECLARE_FUNCTION(execIsPythonLoggingEnabled); \
	DECLARE_FUNCTION(execEnablePythonLogging); \
	DECLARE_FUNCTION(execClearPythonErrors); \
	DECLARE_FUNCTION(execGetPythonErrors); \
	DECLARE_FUNCTION(execIsErrorHandlingEnabled); \
	DECLARE_FUNCTION(execEnableErrorHandling); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execConvertDebugDataToPython); \
	DECLARE_FUNCTION(execConvertPerformanceDataToPython); \
	DECLARE_FUNCTION(execConvertStreamingDataToPython); \
	DECLARE_FUNCTION(execConvertGridCellToPython); \
	DECLARE_FUNCTION(execClearAllCallbacks); \
	DECLARE_FUNCTION(execGetRegisteredCallbacks); \
	DECLARE_FUNCTION(execTriggerPythonCallback); \
	DECLARE_FUNCTION(execUnregisterPythonCallback); \
	DECLARE_FUNCTION(execRegisterPythonCallback); \
	DECLARE_FUNCTION(execExposeDebugAPIs); \
	DECLARE_FUNCTION(execExposePerformanceAPIs); \
	DECLARE_FUNCTION(execExposeStreamingAPIs); \
	DECLARE_FUNCTION(execExposeGridSystemAPIs); \
	DECLARE_FUNCTION(execExposeWorldPartitionAPIs); \
	DECLARE_FUNCTION(execCancelPythonExecution); \
	DECLARE_FUNCTION(execIsPythonExecutionInProgress); \
	DECLARE_FUNCTION(execExecutePythonFunctionAsync); \
	DECLARE_FUNCTION(execExecutePythonFunction); \
	DECLARE_FUNCTION(execExecutePythonScript); \
	DECLARE_FUNCTION(execGetLoadedModules); \
	DECLARE_FUNCTION(execReloadPythonModule); \
	DECLARE_FUNCTION(execLoadPythonModule); \
	DECLARE_FUNCTION(execIsPythonEnvironmentReady); \
	DECLARE_FUNCTION(execShutdownPythonEnvironment); \
	DECLARE_FUNCTION(execInitializePythonEnvironment); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_252_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionPythonBridge(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionPythonBridge, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionPythonBridge)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_252_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionPythonBridge(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionPythonBridge(UAuracronWorldPartitionPythonBridge&&) = delete; \
	UAuracronWorldPartitionPythonBridge(const UAuracronWorldPartitionPythonBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionPythonBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionPythonBridge); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionPythonBridge) \
	NO_API virtual ~UAuracronWorldPartitionPythonBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_249_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_252_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_252_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_252_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h_252_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionPythonBridge;

// ********** End Class UAuracronWorldPartitionPythonBridge ****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h

// ********** Begin Enum EAuracronPythonCallbackType ***********************************************
#define FOREACH_ENUM_EAURACRONPYTHONCALLBACKTYPE(op) \
	op(EAuracronPythonCallbackType::CellLoaded) \
	op(EAuracronPythonCallbackType::CellUnloaded) \
	op(EAuracronPythonCallbackType::StreamingStarted) \
	op(EAuracronPythonCallbackType::StreamingCompleted) \
	op(EAuracronPythonCallbackType::PerformanceAlert) \
	op(EAuracronPythonCallbackType::DebugEvent) \
	op(EAuracronPythonCallbackType::GridUpdated) \
	op(EAuracronPythonCallbackType::LayerChanged) 

enum class EAuracronPythonCallbackType : uint8;
template<> struct TIsUEnumClass<EAuracronPythonCallbackType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronPythonCallbackType>();
// ********** End Enum EAuracronPythonCallbackType *************************************************

// ********** Begin Enum EAuracronPythonExecutionMode **********************************************
#define FOREACH_ENUM_EAURACRONPYTHONEXECUTIONMODE(op) \
	op(EAuracronPythonExecutionMode::Synchronous) \
	op(EAuracronPythonExecutionMode::Asynchronous) \
	op(EAuracronPythonExecutionMode::Threaded) \
	op(EAuracronPythonExecutionMode::Deferred) 

enum class EAuracronPythonExecutionMode : uint8;
template<> struct TIsUEnumClass<EAuracronPythonExecutionMode> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronPythonExecutionMode>();
// ********** End Enum EAuracronPythonExecutionMode ************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
