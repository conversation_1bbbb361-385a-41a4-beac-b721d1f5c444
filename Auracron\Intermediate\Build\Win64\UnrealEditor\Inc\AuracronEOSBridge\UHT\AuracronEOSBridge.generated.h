// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronEOSBridge.h"

#ifdef AURACRONEOSBRIDGE_AuracronEOSBridge_generated_h
#error "AuracronEOSBridge.generated.h already included, missing '#pragma once' in AuracronEOSBridge.h"
#endif
#define AURACRONEOSBRIDGE_AuracronEOSBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
enum class EAuracronEOSConnectionStatus : uint8;
enum class EAuracronSessionType : uint8;
struct FAuracronEOSAchievement;
struct FAuracronEOSFriend;
struct FAuracronSessionConfiguration;

// ********** Begin ScriptStruct FAuracronSessionConfiguration *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_68_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSessionConfiguration;
// ********** End ScriptStruct FAuracronSessionConfiguration ***************************************

// ********** Begin ScriptStruct FAuracronEOSFriend ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_125_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronEOSFriend;
// ********** End ScriptStruct FAuracronEOSFriend **************************************************

// ********** Begin ScriptStruct FAuracronEOSAchievement *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_182_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronEOSAchievement;
// ********** End ScriptStruct FAuracronEOSAchievement *********************************************

// ********** Begin Delegate FOnEOSLoginCompleted **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_502_DELEGATE \
static void FOnEOSLoginCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnEOSLoginCompleted, bool bWasSuccessful, const FString& ErrorMessage);


// ********** End Delegate FOnEOSLoginCompleted ****************************************************

// ********** Begin Delegate FOnSessionCreated *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_507_DELEGATE \
static void FOnSessionCreated_DelegateWrapper(const FMulticastScriptDelegate& OnSessionCreated, bool bWasSuccessful, const FString& SessionID);


// ********** End Delegate FOnSessionCreated *******************************************************

// ********** Begin Delegate FOnAchievementUnlocked ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_512_DELEGATE \
static void FOnAchievementUnlocked_DelegateWrapper(const FMulticastScriptDelegate& OnAchievementUnlocked, FAuracronEOSAchievement Achievement);


// ********** End Delegate FOnAchievementUnlocked **************************************************

// ********** Begin Delegate FOnFriendOnline *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_517_DELEGATE \
static void FOnFriendOnline_DelegateWrapper(const FMulticastScriptDelegate& OnFriendOnline, FAuracronEOSFriend Friend);


// ********** End Delegate FOnFriendOnline *********************************************************

// ********** Begin Class UAuracronEOSBridge *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_232_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_ConnectionStatus); \
	DECLARE_FUNCTION(execTrackGameEvent); \
	DECLARE_FUNCTION(execSendCustomMetric); \
	DECLARE_FUNCTION(execGetFriendPresence); \
	DECLARE_FUNCTION(execSetPresence); \
	DECLARE_FUNCTION(execDeletePlayerData); \
	DECLARE_FUNCTION(execLoadPlayerData); \
	DECLARE_FUNCTION(execSavePlayerData); \
	DECLARE_FUNCTION(execGetTopRankings); \
	DECLARE_FUNCTION(execGetPlayerRank); \
	DECLARE_FUNCTION(execSubmitScore); \
	DECLARE_FUNCTION(execIsAchievementUnlocked); \
	DECLARE_FUNCTION(execGetAchievements); \
	DECLARE_FUNCTION(execUpdateAchievementProgress); \
	DECLARE_FUNCTION(execUnlockAchievement); \
	DECLARE_FUNCTION(execInviteFriendToSession); \
	DECLARE_FUNCTION(execRemoveFriend); \
	DECLARE_FUNCTION(execAddFriend); \
	DECLARE_FUNCTION(execGetFriendsList); \
	DECLARE_FUNCTION(execLoadFriendsList); \
	DECLARE_FUNCTION(execDestroySession); \
	DECLARE_FUNCTION(execLeaveSession); \
	DECLARE_FUNCTION(execJoinSession); \
	DECLARE_FUNCTION(execFindSessions); \
	DECLARE_FUNCTION(execCreateSession); \
	DECLARE_FUNCTION(execGetDisplayName); \
	DECLARE_FUNCTION(execGetUserID); \
	DECLARE_FUNCTION(execGetAuthenticationStatus); \
	DECLARE_FUNCTION(execLogoutFromEOS); \
	DECLARE_FUNCTION(execLoginWithEOS);


AURACRONEOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronEOSBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_232_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronEOSBridge(); \
	friend struct Z_Construct_UClass_UAuracronEOSBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONEOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronEOSBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronEOSBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronEOSBridge"), Z_Construct_UClass_UAuracronEOSBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronEOSBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		CurrentConnectionStatus=NETFIELD_REP_START, \
		CurrentUserID, \
		CurrentDisplayName, \
		FriendsList, \
		PlayerAchievements, \
		CurrentSession, \
		bIsInSession, \
		NETFIELD_REP_END=bIsInSession	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_232_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronEOSBridge(UAuracronEOSBridge&&) = delete; \
	UAuracronEOSBridge(const UAuracronEOSBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronEOSBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronEOSBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronEOSBridge) \
	NO_API virtual ~UAuracronEOSBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_229_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_232_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_232_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_232_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h_232_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronEOSBridge;

// ********** End Class UAuracronEOSBridge *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h

// ********** Begin Enum EAuracronEOSConnectionStatus **********************************************
#define FOREACH_ENUM_EAURACRONEOSCONNECTIONSTATUS(op) \
	op(EAuracronEOSConnectionStatus::Disconnected) \
	op(EAuracronEOSConnectionStatus::Connecting) \
	op(EAuracronEOSConnectionStatus::Connected) \
	op(EAuracronEOSConnectionStatus::Authenticating) \
	op(EAuracronEOSConnectionStatus::Authenticated) \
	op(EAuracronEOSConnectionStatus::Error) \
	op(EAuracronEOSConnectionStatus::Banned) \
	op(EAuracronEOSConnectionStatus::Suspended) 

enum class EAuracronEOSConnectionStatus : uint8;
template<> struct TIsUEnumClass<EAuracronEOSConnectionStatus> { enum { Value = true }; };
template<> AURACRONEOSBRIDGE_API UEnum* StaticEnum<EAuracronEOSConnectionStatus>();
// ********** End Enum EAuracronEOSConnectionStatus ************************************************

// ********** Begin Enum EAuracronSessionType ******************************************************
#define FOREACH_ENUM_EAURACRONSESSIONTYPE(op) \
	op(EAuracronSessionType::None) \
	op(EAuracronSessionType::Ranked) \
	op(EAuracronSessionType::Casual) \
	op(EAuracronSessionType::Custom) \
	op(EAuracronSessionType::Training) \
	op(EAuracronSessionType::Tournament) \
	op(EAuracronSessionType::Event) 

enum class EAuracronSessionType : uint8;
template<> struct TIsUEnumClass<EAuracronSessionType> { enum { Value = true }; };
template<> AURACRONEOSBRIDGE_API UEnum* StaticEnum<EAuracronSessionType>();
// ********** End Enum EAuracronSessionType ********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
