// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - World Partition Bridge Build Configuration
// Production-ready build file for UE5.6 World Partition API bridge
using UnrealBuildTool;
using System.IO;
public class AuracronWorldPartitionBridge : ModuleRules
{
    public AuracronWorldPartitionBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        // Module type and optimization settings
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        bUseUnity = true;
        IWYUSupport = IWYUSupport.Full;
        // Core dependencies for World Partition functionality
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "Core",
            "CoreUObject",
            "Engine",
            "UnrealEd",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "Slate",
            "SlateCore",
            "InputCore",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "EngineSettings",
            "RenderCore",
            "RHI","DataLayerEditor","LevelInstanceEditor",
            "ActorLayerUtilities",
            "WorldBrowser",
            "WorldBrowser",
            "Landscape",
            "LandscapeEditor",
            "AudioMixer","NiagaraCore","MeshUtilities",
            "MeshUtilitiesCommon",
            "GeometryProcessingInterfaces",
            "PythonScriptPlugin"
        });
        // World Partition specific dependencies
        PublicDependencyModuleNames.AddRange(new string[]
        {"WorldPartitionHLODUtilities",
            "HierarchicalLODUtilities",
            "MeshUtilities",
            "MeshDescription",
            "StaticMeshDescription",
            "MeshBuilder",
            "MaterialUtilities",
            "Landscape",
            "Foliage",
            "NavigationSystem","UMG",
            "ToolWidgets",
            "EditorSubsystem",});
        // Private dependencies for internal functionality
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "ApplicationCore",
            "AppFramework",
            "DesktopPlatform",
            "EditorStyle",
            "GraphEditor",
            "KismetCompiler",
            "ToolMenus",
            "AssetTools",
            "ContentBrowser",
            "ContentBrowserData",
            "EditorWidgets",
            "GameplayTags",
            "Json",
            "Json","ImageWrapper",
            "RawMesh",
            "GeometryCore",
            "DynamicMesh",
            "GeometryFramework",
            "InteractiveToolsFramework",
            "EditorInteractiveToolsFramework",
            "MeshConversion",
            "ModelingComponents",
            "ModelingComponentsEditorOnly",
            "GeometryProcessingInterfaces",
            "DeveloperSettings",
            "Projects",
            "SourceControl",
            "UncontrolledChangelists",
            "CollectionManager",
            "AddContentDialog",
            "GameProjectGeneration",
            "MainFrame",
            "Documentation",
            "StylusInput",
            "XmlParser","LauncherServices","LauncherPlatform",
            "AudioEditor",
            "UnrealEdMessages",
            "GameplayTagsEditor",
            "AddContentDialog",
            "MeshPaint",
            "MeshPaint",
            "VirtualTexturingEditor",
            "TextureCompressor",
            "DerivedDataCache",
            "TargetDeviceServices",
            "LauncherServices",
            "SharedSettingsWidgets","Localization",});
        // Python integration dependencies
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "PythonScriptPlugin",
                "PythonScriptPluginPreload"
            });
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_WIN64=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Android)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_ANDROID=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_IOS=1");
        }
        // Preprocessor definitions for World Partition features
        PublicDefinitions.AddRange(new string[]
        {
            "WITH_WORLD_PARTITION_BRIDGE=1",
            "WITH_WORLD_PARTITION_STREAMING=1",
            "WITH_WORLD_PARTITION_HLOD=1",
            "WITH_WORLD_PARTITION_DATA_LAYERS=1",
            "WITH_WORLD_PARTITION_RUNTIME_HASH=1",
            "WITH_WORLD_PARTITION_EDITOR_HASH=1",
            "WITH_WORLD_PARTITION_MINIMAP=1",
            "WITH_WORLD_PARTITION_LOCATION_VOLUMES=1",
            "WITH_WORLD_PARTITION_STREAMING_SOURCE=1",
            "WITH_WORLD_PARTITION_GRID_PREVIEW=1",
            "WITH_WORLD_PARTITION_DEBUG_VISUALIZATION=1",
            "WITH_WORLD_PARTITION_COMMANDLETS=1",
            "WITH_WORLD_PARTITION_BUILDER=1",
            "WITH_WORLD_PARTITION_COOK_SUPPORT=1",
            "WITH_WORLD_PARTITION_VALIDATION=1",
            "WITH_PYTHON_BINDINGS=1",
            "WITH_AURACRON_WORLD_PARTITION_EXTENSIONS=1"
        });
        // Include paths for World Partition headers
        PublicIncludePaths.AddRange(new string[]
        {
            Path.Combine(ModuleDirectory, "Public"),
            Path.Combine(EngineDirectory, "Source/Runtime/Engine/Public/WorldPartition"),
            Path.Combine(EngineDirectory, "Source/Editor/WorldPartitionEditor/Public"),
            Path.Combine(EngineDirectory, "Source/Editor/WorldPartitionEditor/Private"),
            Path.Combine(EngineDirectory, "Source/Runtime/Engine/Public/WorldPartition"),
            Path.Combine(EngineDirectory, "Source/Runtime/Engine/Private/WorldPartition")
        });
        PrivateIncludePaths.AddRange(new string[]
        {
            Path.Combine(ModuleDirectory, "Private"),
            Path.Combine(EngineDirectory, "Source/Editor/UnrealEd/Private"),
            Path.Combine(EngineDirectory, "Source/Editor/LevelEditor/Private"),
            Path.Combine(EngineDirectory, "Source/Runtime/Renderer/Private")
        });
        // Compiler and linker settings
        bEnableExceptions = true;
        bUseRTTI = true;
        CppCompileWarningSettings.ShadowVariableWarningLevel = WarningLevel.Error;
        CppCompileWarningSettings.UnsafeTypeCastWarningLevel = WarningLevel.Error;
        // Development and shipping build optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Development ||
            Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_OPTIMIZED=1");
            bUseUnity = true;
            MinFilesUsingPrecompiledHeaderOverride = 1;
        }
        // Debug build settings
        if (Target.Configuration == UnrealTargetConfiguration.Debug ||
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_DEBUG=1");
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_VERBOSE_LOGGING=1");
        }
        // Editor-only features
        if (Target.bBuildEditor)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_EDITOR=1");
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "LevelEditor",
                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers",
                "GraphEditor",
                "KismetCompiler",
                "ToolMenus",
                "AssetTools",
                "ContentBrowser",
                "EditorWidgets",
                "Slate",
                "SlateCore",
                "EditorStyle",
                "ToolWidgets",
                "WorkspaceMenuStructure",
                "EditorSubsystem"
            });
        }
        // Ensure compatibility with UE5.6 World Partition system
        PublicDefinitions.Add("UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2=0");
        PublicDefinitions.Add("UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3=0");
        PublicDefinitions.Add("UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4=0");
        PublicDefinitions.Add("UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5=0");
    }
}
