// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageBiome.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageBiome_generated_h
#error "AuracronFoliageBiome.generated.h already included, missing '#pragma once' in AuracronFoliageBiome.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageBiome_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronFoliageBiomeManager;
class UPCGComponent;
class UWorld;
struct FAuracronBiomeConfiguration;
struct FAuracronBiomeDefinition;
struct FAuracronClimateData;
struct FAuracronEcosystemRuleData;
struct FAuracronSpeciesData;

// ********** Begin ScriptStruct FAuracronClimateData **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_138_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronClimateData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronClimateData;
// ********** End ScriptStruct FAuracronClimateData ************************************************

// ********** Begin ScriptStruct FAuracronSpeciesData **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_236_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSpeciesData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSpeciesData;
// ********** End ScriptStruct FAuracronSpeciesData ************************************************

// ********** Begin ScriptStruct FAuracronEcosystemRuleData ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_339_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronEcosystemRuleData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronEcosystemRuleData;
// ********** End ScriptStruct FAuracronEcosystemRuleData ******************************************

// ********** Begin ScriptStruct FAuracronBiomeDefinition ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_432_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronBiomeDefinition_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronBiomeDefinition;
// ********** End ScriptStruct FAuracronBiomeDefinition ********************************************

// ********** Begin ScriptStruct FAuracronBiomeConfiguration ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_536_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronBiomeConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronBiomeConfiguration;
// ********** End ScriptStruct FAuracronBiomeConfiguration *****************************************

// ********** Begin Delegate FOnBiomeRegistered ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_773_DELEGATE \
static void FOnBiomeRegistered_DelegateWrapper(const FMulticastScriptDelegate& OnBiomeRegistered, const FString& BiomeId, FAuracronBiomeDefinition BiomeDefinition);


// ********** End Delegate FOnBiomeRegistered ******************************************************

// ********** Begin Delegate FOnBiomeUnregistered **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_774_DELEGATE \
static void FOnBiomeUnregistered_DelegateWrapper(const FMulticastScriptDelegate& OnBiomeUnregistered, const FString& BiomeId);


// ********** End Delegate FOnBiomeUnregistered ****************************************************

// ********** Begin Delegate FOnSpeciesAdded *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_775_DELEGATE \
static void FOnSpeciesAdded_DelegateWrapper(const FMulticastScriptDelegate& OnSpeciesAdded, const FString& BiomeId, const FString& SpeciesId, FAuracronSpeciesData SpeciesData);


// ********** End Delegate FOnSpeciesAdded *********************************************************

// ********** Begin Delegate FOnSpeciesRemoved *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_776_DELEGATE \
static void FOnSpeciesRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnSpeciesRemoved, const FString& BiomeId, const FString& SpeciesId);


// ********** End Delegate FOnSpeciesRemoved *******************************************************

// ********** Begin Delegate FOnDisturbanceEvent ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_777_DELEGATE \
static void FOnDisturbanceEvent_DelegateWrapper(const FMulticastScriptDelegate& OnDisturbanceEvent, const FString& BiomeId, FVector Location, float Intensity);


// ********** End Delegate FOnDisturbanceEvent *****************************************************

// ********** Begin Class UAuracronFoliageBiomeManager *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_634_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDrawDebugVisualization); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetBiomeStatistics); \
	DECLARE_FUNCTION(execGetTotalSpeciesCount); \
	DECLARE_FUNCTION(execGetTotalBiomeCount); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetBiomePCGComponent); \
	DECLARE_FUNCTION(execUpdateBiomePCGParameters); \
	DECLARE_FUNCTION(execGenerateBiomePCG); \
	DECLARE_FUNCTION(execUpdateDisturbanceEvents); \
	DECLARE_FUNCTION(execTriggerDisturbanceEvent); \
	DECLARE_FUNCTION(execUpdateSuccession); \
	DECLARE_FUNCTION(execUpdateEcosystemRules); \
	DECLARE_FUNCTION(execGetEcosystemRules); \
	DECLARE_FUNCTION(execRemoveEcosystemRule); \
	DECLARE_FUNCTION(execAddEcosystemRule); \
	DECLARE_FUNCTION(execGetSpeciesAbundance); \
	DECLARE_FUNCTION(execGetSpeciesAtLocation); \
	DECLARE_FUNCTION(execGetSpeciesInBiome); \
	DECLARE_FUNCTION(execRemoveSpeciesFromBiome); \
	DECLARE_FUNCTION(execAddSpeciesToBiome); \
	DECLARE_FUNCTION(execSimulateSeasonalChanges); \
	DECLARE_FUNCTION(execSetGlobalClimateParameters); \
	DECLARE_FUNCTION(execUpdateClimateSimulation); \
	DECLARE_FUNCTION(execGetClimateAtLocation); \
	DECLARE_FUNCTION(execGetAdjacentBiomes); \
	DECLARE_FUNCTION(execGetBiomeInfluence); \
	DECLARE_FUNCTION(execGetBiomesInArea); \
	DECLARE_FUNCTION(execGetBiomeAtLocation); \
	DECLARE_FUNCTION(execUpdateBiome); \
	DECLARE_FUNCTION(execGetAllBiomes); \
	DECLARE_FUNCTION(execGetBiome); \
	DECLARE_FUNCTION(execUnregisterBiome); \
	DECLARE_FUNCTION(execRegisterBiome); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageBiomeManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_634_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageBiomeManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageBiomeManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageBiomeManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageBiomeManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageBiomeManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageBiomeManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_634_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageBiomeManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageBiomeManager(UAuracronFoliageBiomeManager&&) = delete; \
	UAuracronFoliageBiomeManager(const UAuracronFoliageBiomeManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageBiomeManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageBiomeManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageBiomeManager) \
	NO_API virtual ~UAuracronFoliageBiomeManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_631_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_634_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_634_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_634_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h_634_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageBiomeManager;

// ********** End Class UAuracronFoliageBiomeManager ***********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBiome_h

// ********** Begin Enum EAuracronBiomeType ********************************************************
#define FOREACH_ENUM_EAURACRONBIOMETYPE(op) \
	op(EAuracronBiomeType::Grassland) \
	op(EAuracronBiomeType::Forest) \
	op(EAuracronBiomeType::Desert) \
	op(EAuracronBiomeType::Tundra) \
	op(EAuracronBiomeType::Wetland) \
	op(EAuracronBiomeType::Mountain) \
	op(EAuracronBiomeType::Coastal) \
	op(EAuracronBiomeType::Urban) \
	op(EAuracronBiomeType::Volcanic) \
	op(EAuracronBiomeType::Magical) \
	op(EAuracronBiomeType::Custom) 

enum class EAuracronBiomeType : uint8;
template<> struct TIsUEnumClass<EAuracronBiomeType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronBiomeType>();
// ********** End Enum EAuracronBiomeType **********************************************************

// ********** Begin Enum EAuracronClimateType ******************************************************
#define FOREACH_ENUM_EAURACRONCLIMATETYPE(op) \
	op(EAuracronClimateType::Tropical) \
	op(EAuracronClimateType::Arid) \
	op(EAuracronClimateType::Temperate) \
	op(EAuracronClimateType::Continental) \
	op(EAuracronClimateType::Polar) \
	op(EAuracronClimateType::Mediterranean) \
	op(EAuracronClimateType::Oceanic) \
	op(EAuracronClimateType::Subarctic) \
	op(EAuracronClimateType::Alpine) \
	op(EAuracronClimateType::Custom) 

enum class EAuracronClimateType : uint8;
template<> struct TIsUEnumClass<EAuracronClimateType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronClimateType>();
// ********** End Enum EAuracronClimateType ********************************************************

// ********** Begin Enum EAuracronTransitionZoneType ***********************************************
#define FOREACH_ENUM_EAURACRONTRANSITIONZONETYPE(op) \
	op(EAuracronTransitionZoneType::Sharp) \
	op(EAuracronTransitionZoneType::Gradual) \
	op(EAuracronTransitionZoneType::Ecotone) \
	op(EAuracronTransitionZoneType::Mosaic) \
	op(EAuracronTransitionZoneType::Elevation) \
	op(EAuracronTransitionZoneType::Moisture) \
	op(EAuracronTransitionZoneType::Temperature) \
	op(EAuracronTransitionZoneType::Custom) 

enum class EAuracronTransitionZoneType : uint8;
template<> struct TIsUEnumClass<EAuracronTransitionZoneType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronTransitionZoneType>();
// ********** End Enum EAuracronTransitionZoneType *************************************************

// ********** Begin Enum EAuracronSpeciesDistributionPattern ***************************************
#define FOREACH_ENUM_EAURACRONSPECIESDISTRIBUTIONPATTERN(op) \
	op(EAuracronSpeciesDistributionPattern::Random) \
	op(EAuracronSpeciesDistributionPattern::Clustered) \
	op(EAuracronSpeciesDistributionPattern::Uniform) \
	op(EAuracronSpeciesDistributionPattern::Linear) \
	op(EAuracronSpeciesDistributionPattern::Radial) \
	op(EAuracronSpeciesDistributionPattern::Fractal) \
	op(EAuracronSpeciesDistributionPattern::Succession) \
	op(EAuracronSpeciesDistributionPattern::Competition) \
	op(EAuracronSpeciesDistributionPattern::Symbiotic) \
	op(EAuracronSpeciesDistributionPattern::Custom) 

enum class EAuracronSpeciesDistributionPattern : uint8;
template<> struct TIsUEnumClass<EAuracronSpeciesDistributionPattern> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronSpeciesDistributionPattern>();
// ********** End Enum EAuracronSpeciesDistributionPattern *****************************************

// ********** Begin Enum EAuracronEcosystemRuleType ************************************************
#define FOREACH_ENUM_EAURACRONECOSYSTEMRULETYPE(op) \
	op(EAuracronEcosystemRuleType::Predation) \
	op(EAuracronEcosystemRuleType::Competition) \
	op(EAuracronEcosystemRuleType::Mutualism) \
	op(EAuracronEcosystemRuleType::Parasitism) \
	op(EAuracronEcosystemRuleType::Commensalism) \
	op(EAuracronEcosystemRuleType::Succession) \
	op(EAuracronEcosystemRuleType::Disturbance) \
	op(EAuracronEcosystemRuleType::Migration) \
	op(EAuracronEcosystemRuleType::Seasonal) \
	op(EAuracronEcosystemRuleType::Custom) 

enum class EAuracronEcosystemRuleType : uint8;
template<> struct TIsUEnumClass<EAuracronEcosystemRuleType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronEcosystemRuleType>();
// ********** End Enum EAuracronEcosystemRuleType **************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
