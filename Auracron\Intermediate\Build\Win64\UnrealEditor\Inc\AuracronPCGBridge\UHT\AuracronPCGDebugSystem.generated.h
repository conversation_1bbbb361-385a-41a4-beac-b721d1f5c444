// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGDebugSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGDebugSystem_generated_h
#error "AuracronPCGDebugSystem.generated.h already included, missing '#pragma once' in AuracronPCGDebugSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGDebugSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronPCGDebugSystemManager;
class UPCGData;
class UPCGGraph;
class UPCGMetadata;
class UPCGPointData;
class UPCGSettings;
class UPCGSpatialData;
class UWorld;
enum class EAuracronPCGDebugSystemVisualizationMode : uint8;
struct FAuracronPCGDebugExecutionDescriptor;
struct FAuracronPCGDebugInspectionDescriptor;
struct FAuracronPCGDebugProfilingDescriptor;
struct FAuracronPCGDebugVisualizationDescriptor;
struct FLinearColor;

// ********** Begin ScriptStruct FAuracronPCGDebugVisualizationDescriptor **************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_95_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGDebugVisualizationDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGDebugVisualizationDescriptor;
// ********** End ScriptStruct FAuracronPCGDebugVisualizationDescriptor ****************************

// ********** Begin ScriptStruct FAuracronPCGDebugProfilingDescriptor ******************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_204_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGDebugProfilingDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGDebugProfilingDescriptor;
// ********** End ScriptStruct FAuracronPCGDebugProfilingDescriptor ********************************

// ********** Begin ScriptStruct FAuracronPCGDebugInspectionDescriptor *****************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_282_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGDebugInspectionDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGDebugInspectionDescriptor;
// ********** End ScriptStruct FAuracronPCGDebugInspectionDescriptor *******************************

// ********** Begin ScriptStruct FAuracronPCGDebugExecutionDescriptor ******************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_362_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGDebugExecutionDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGDebugExecutionDescriptor;
// ********** End ScriptStruct FAuracronPCGDebugExecutionDescriptor ********************************

// ********** Begin Class UAuracronPCGVisualDebugger ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_435_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIsVisualizationEnabled); \
	DECLARE_FUNCTION(execToggleVisualization); \
	DECLARE_FUNCTION(execGetVisualizationMode); \
	DECLARE_FUNCTION(execSetVisualizationMode); \
	DECLARE_FUNCTION(execClearDebugDisplay); \
	DECLARE_FUNCTION(execDrawErrors); \
	DECLARE_FUNCTION(execDrawDataFlow); \
	DECLARE_FUNCTION(execDrawPerformanceInfo); \
	DECLARE_FUNCTION(execDrawAttributes); \
	DECLARE_FUNCTION(execDrawBoundingBoxes); \
	DECLARE_FUNCTION(execDrawGraphConnections); \
	DECLARE_FUNCTION(execDrawPointData);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGVisualDebugger_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_435_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGVisualDebugger(); \
	friend struct Z_Construct_UClass_UAuracronPCGVisualDebugger_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGVisualDebugger_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGVisualDebugger, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGVisualDebugger_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGVisualDebugger)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_435_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGVisualDebugger(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGVisualDebugger(UAuracronPCGVisualDebugger&&) = delete; \
	UAuracronPCGVisualDebugger(const UAuracronPCGVisualDebugger&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGVisualDebugger); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGVisualDebugger); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGVisualDebugger) \
	NO_API virtual ~UAuracronPCGVisualDebugger();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_432_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_435_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_435_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_435_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_435_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGVisualDebugger;

// ********** End Class UAuracronPCGVisualDebugger *************************************************

// ********** Begin Class UAuracronPCGPerformanceProfiler ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_492_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execClearProfilingData); \
	DECLARE_FUNCTION(execExportProfilingData); \
	DECLARE_FUNCTION(execGeneratePerformanceReport); \
	DECLARE_FUNCTION(execGetHighMemoryNodes); \
	DECLARE_FUNCTION(execGetBottleneckNodes); \
	DECLARE_FUNCTION(execGetPeakMemoryUsage); \
	DECLARE_FUNCTION(execGetTotalExecutionTime); \
	DECLARE_FUNCTION(execGetCustomMetrics); \
	DECLARE_FUNCTION(execGetMemoryUsage); \
	DECLARE_FUNCTION(execGetExecutionTimes); \
	DECLARE_FUNCTION(execRecordCustomMetric); \
	DECLARE_FUNCTION(execRecordDataTransfer); \
	DECLARE_FUNCTION(execRecordMemoryUsage); \
	DECLARE_FUNCTION(execRecordExecutionTime); \
	DECLARE_FUNCTION(execIsProfilingActive); \
	DECLARE_FUNCTION(execResumeProfiling); \
	DECLARE_FUNCTION(execPauseProfiling); \
	DECLARE_FUNCTION(execStopProfiling); \
	DECLARE_FUNCTION(execStartProfiling);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPerformanceProfiler_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_492_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPerformanceProfiler(); \
	friend struct Z_Construct_UClass_UAuracronPCGPerformanceProfiler_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPerformanceProfiler_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPerformanceProfiler, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPerformanceProfiler_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPerformanceProfiler)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_492_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGPerformanceProfiler(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPerformanceProfiler(UAuracronPCGPerformanceProfiler&&) = delete; \
	UAuracronPCGPerformanceProfiler(const UAuracronPCGPerformanceProfiler&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPerformanceProfiler); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPerformanceProfiler); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGPerformanceProfiler) \
	NO_API virtual ~UAuracronPCGPerformanceProfiler();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_489_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_492_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_492_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_492_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_492_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPerformanceProfiler;

// ********** End Class UAuracronPCGPerformanceProfiler ********************************************

// ********** Begin Class UAuracronPCGDataInspector ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_576_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCalculateDataSize); \
	DECLARE_FUNCTION(execGetDataBounds); \
	DECLARE_FUNCTION(execGetAttributeNames); \
	DECLARE_FUNCTION(execCountPoints); \
	DECLARE_FUNCTION(execGenerateDataReport); \
	DECLARE_FUNCTION(execAnalyzeAttributeDistribution); \
	DECLARE_FUNCTION(execAnalyzeDataTypes); \
	DECLARE_FUNCTION(execCheckForMemoryLeaks); \
	DECLARE_FUNCTION(execValidateGraphConnections); \
	DECLARE_FUNCTION(execValidateDataIntegrity); \
	DECLARE_FUNCTION(execInspectNode); \
	DECLARE_FUNCTION(execInspectGraph); \
	DECLARE_FUNCTION(execInspectMetadata); \
	DECLARE_FUNCTION(execInspectSpatialData); \
	DECLARE_FUNCTION(execInspectPointData);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDataInspector_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_576_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGDataInspector(); \
	friend struct Z_Construct_UClass_UAuracronPCGDataInspector_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDataInspector_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGDataInspector, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGDataInspector_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGDataInspector)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_576_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGDataInspector(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGDataInspector(UAuracronPCGDataInspector&&) = delete; \
	UAuracronPCGDataInspector(const UAuracronPCGDataInspector&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGDataInspector); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGDataInspector); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGDataInspector) \
	NO_API virtual ~UAuracronPCGDataInspector();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_573_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_576_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_576_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_576_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_576_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGDataInspector;

// ********** End Class UAuracronPCGDataInspector **************************************************

// ********** Begin Class UAuracronPCGGraphVisualizer **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_647_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetNodeDisplayName); \
	DECLARE_FUNCTION(execGetNodeColor); \
	DECLARE_FUNCTION(execClearGraphVisualization); \
	DECLARE_FUNCTION(execCalculateNodePosition); \
	DECLARE_FUNCTION(execCalculateNodeLayout); \
	DECLARE_FUNCTION(execDrawNodeLabels); \
	DECLARE_FUNCTION(execDrawNodeConnections); \
	DECLARE_FUNCTION(execDrawNode); \
	DECLARE_FUNCTION(execVisualizeExecutionOrder); \
	DECLARE_FUNCTION(execVisualizeDataFlow); \
	DECLARE_FUNCTION(execVisualizeNodeHierarchy); \
	DECLARE_FUNCTION(execVisualizeGraph);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGGraphVisualizer_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_647_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGGraphVisualizer(); \
	friend struct Z_Construct_UClass_UAuracronPCGGraphVisualizer_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGGraphVisualizer_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGGraphVisualizer, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGGraphVisualizer_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGGraphVisualizer)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_647_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGGraphVisualizer(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGGraphVisualizer(UAuracronPCGGraphVisualizer&&) = delete; \
	UAuracronPCGGraphVisualizer(const UAuracronPCGGraphVisualizer&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGGraphVisualizer); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGGraphVisualizer); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGGraphVisualizer) \
	NO_API virtual ~UAuracronPCGGraphVisualizer();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_644_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_647_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_647_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_647_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_647_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGGraphVisualizer;

// ********** End Class UAuracronPCGGraphVisualizer ************************************************

// ********** Begin Class UAuracronPCGStepByStepExecutor *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_702_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIsReplayActive); \
	DECLARE_FUNCTION(execSetReplaySpeed); \
	DECLARE_FUNCTION(execStopReplay); \
	DECLARE_FUNCTION(execStartReplay); \
	DECLARE_FUNCTION(execGetExecutionHistory); \
	DECLARE_FUNCTION(execGetTotalSteps); \
	DECLARE_FUNCTION(execGetCurrentStep); \
	DECLARE_FUNCTION(execGetCurrentNode); \
	DECLARE_FUNCTION(execHasBreakpoint); \
	DECLARE_FUNCTION(execGetBreakpoints); \
	DECLARE_FUNCTION(execClearAllBreakpoints); \
	DECLARE_FUNCTION(execRemoveBreakpoint); \
	DECLARE_FUNCTION(execAddBreakpoint); \
	DECLARE_FUNCTION(execIsExecutionPaused); \
	DECLARE_FUNCTION(execIsExecutionActive); \
	DECLARE_FUNCTION(execStepBackward); \
	DECLARE_FUNCTION(execStepForward); \
	DECLARE_FUNCTION(execResumeExecution); \
	DECLARE_FUNCTION(execPauseExecution); \
	DECLARE_FUNCTION(execStopStepByStepExecution); \
	DECLARE_FUNCTION(execStartStepByStepExecution);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGStepByStepExecutor_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_702_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGStepByStepExecutor(); \
	friend struct Z_Construct_UClass_UAuracronPCGStepByStepExecutor_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGStepByStepExecutor_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGStepByStepExecutor, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGStepByStepExecutor_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGStepByStepExecutor)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_702_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGStepByStepExecutor(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGStepByStepExecutor(UAuracronPCGStepByStepExecutor&&) = delete; \
	UAuracronPCGStepByStepExecutor(const UAuracronPCGStepByStepExecutor&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGStepByStepExecutor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGStepByStepExecutor); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGStepByStepExecutor) \
	NO_API virtual ~UAuracronPCGStepByStepExecutor();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_699_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_702_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_702_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_702_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_702_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGStepByStepExecutor;

// ********** End Class UAuracronPCGStepByStepExecutor *********************************************

// ********** Begin Class UAuracronPCGDebugConsoleCommands *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_794_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execUnregisterConsoleCommands); \
	DECLARE_FUNCTION(execRegisterConsoleCommands);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDebugConsoleCommands_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_794_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGDebugConsoleCommands(); \
	friend struct Z_Construct_UClass_UAuracronPCGDebugConsoleCommands_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDebugConsoleCommands_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGDebugConsoleCommands, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGDebugConsoleCommands_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGDebugConsoleCommands)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_794_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGDebugConsoleCommands(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGDebugConsoleCommands(UAuracronPCGDebugConsoleCommands&&) = delete; \
	UAuracronPCGDebugConsoleCommands(const UAuracronPCGDebugConsoleCommands&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGDebugConsoleCommands); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGDebugConsoleCommands); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGDebugConsoleCommands) \
	NO_API virtual ~UAuracronPCGDebugConsoleCommands();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_791_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_794_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_794_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_794_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_794_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGDebugConsoleCommands;

// ********** End Class UAuracronPCGDebugConsoleCommands *******************************************

// ********** Begin Class UAuracronPCGDebugSystemManager *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_831_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execResetToDefaultConfiguration); \
	DECLARE_FUNCTION(execLoadDebugConfiguration); \
	DECLARE_FUNCTION(execSaveDebugConfiguration); \
	DECLARE_FUNCTION(execGenerateFullDebugReport); \
	DECLARE_FUNCTION(execStopFullDebugging); \
	DECLARE_FUNCTION(execStartFullDebugging); \
	DECLARE_FUNCTION(execSetExecutionDescriptor); \
	DECLARE_FUNCTION(execSetInspectionDescriptor); \
	DECLARE_FUNCTION(execSetProfilingDescriptor); \
	DECLARE_FUNCTION(execSetVisualizationDescriptor); \
	DECLARE_FUNCTION(execIsDebugSystemActive); \
	DECLARE_FUNCTION(execShutdownDebugSystem); \
	DECLARE_FUNCTION(execInitializeDebugSystem); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDebugSystemManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_831_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGDebugSystemManager(); \
	friend struct Z_Construct_UClass_UAuracronPCGDebugSystemManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDebugSystemManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGDebugSystemManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGDebugSystemManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGDebugSystemManager)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_831_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGDebugSystemManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGDebugSystemManager(UAuracronPCGDebugSystemManager&&) = delete; \
	UAuracronPCGDebugSystemManager(const UAuracronPCGDebugSystemManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGDebugSystemManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGDebugSystemManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGDebugSystemManager) \
	NO_API virtual ~UAuracronPCGDebugSystemManager();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_828_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_831_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_831_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_831_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h_831_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGDebugSystemManager;

// ********** End Class UAuracronPCGDebugSystemManager *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGDebugSystem_h

// ********** Begin Enum EAuracronPCGDebugSystemVisualizationMode **********************************
#define FOREACH_ENUM_EAURACRONPCGDEBUGSYSTEMVISUALIZATIONMODE(op) \
	op(EAuracronPCGDebugSystemVisualizationMode::None) \
	op(EAuracronPCGDebugSystemVisualizationMode::Points) \
	op(EAuracronPCGDebugSystemVisualizationMode::Connections) \
	op(EAuracronPCGDebugSystemVisualizationMode::BoundingBoxes) \
	op(EAuracronPCGDebugSystemVisualizationMode::Attributes) \
	op(EAuracronPCGDebugSystemVisualizationMode::Performance) \
	op(EAuracronPCGDebugSystemVisualizationMode::DataFlow) \
	op(EAuracronPCGDebugSystemVisualizationMode::Errors) \
	op(EAuracronPCGDebugSystemVisualizationMode::All) 

enum class EAuracronPCGDebugSystemVisualizationMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGDebugSystemVisualizationMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGDebugSystemVisualizationMode>();
// ********** End Enum EAuracronPCGDebugSystemVisualizationMode ************************************

// ********** Begin Enum EAuracronPCGDebugProfilingCategory ****************************************
#define FOREACH_ENUM_EAURACRONPCGDEBUGPROFILINGCATEGORY(op) \
	op(EAuracronPCGDebugProfilingCategory::Execution) \
	op(EAuracronPCGDebugProfilingCategory::Memory) \
	op(EAuracronPCGDebugProfilingCategory::DataTransfer) \
	op(EAuracronPCGDebugProfilingCategory::NodeProcessing) \
	op(EAuracronPCGDebugProfilingCategory::GraphValidation) \
	op(EAuracronPCGDebugProfilingCategory::Rendering) \
	op(EAuracronPCGDebugProfilingCategory::IO) \
	op(EAuracronPCGDebugProfilingCategory::Custom) 

enum class EAuracronPCGDebugProfilingCategory : uint8;
template<> struct TIsUEnumClass<EAuracronPCGDebugProfilingCategory> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGDebugProfilingCategory>();
// ********** End Enum EAuracronPCGDebugProfilingCategory ******************************************

// ********** Begin Enum EAuracronPCGDebugInspectionLevel ******************************************
#define FOREACH_ENUM_EAURACRONPCGDEBUGINSPECTIONLEVEL(op) \
	op(EAuracronPCGDebugInspectionLevel::Basic) \
	op(EAuracronPCGDebugInspectionLevel::Detailed) \
	op(EAuracronPCGDebugInspectionLevel::Verbose) \
	op(EAuracronPCGDebugInspectionLevel::Expert) 

enum class EAuracronPCGDebugInspectionLevel : uint8;
template<> struct TIsUEnumClass<EAuracronPCGDebugInspectionLevel> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGDebugInspectionLevel>();
// ********** End Enum EAuracronPCGDebugInspectionLevel ********************************************

// ********** Begin Enum EAuracronPCGDebugExecutionMode ********************************************
#define FOREACH_ENUM_EAURACRONPCGDEBUGEXECUTIONMODE(op) \
	op(EAuracronPCGDebugExecutionMode::Normal) \
	op(EAuracronPCGDebugExecutionMode::StepByStep) \
	op(EAuracronPCGDebugExecutionMode::Breakpoints) \
	op(EAuracronPCGDebugExecutionMode::SlowMotion) \
	op(EAuracronPCGDebugExecutionMode::Replay) 

enum class EAuracronPCGDebugExecutionMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGDebugExecutionMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGDebugExecutionMode>();
// ********** End Enum EAuracronPCGDebugExecutionMode **********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
