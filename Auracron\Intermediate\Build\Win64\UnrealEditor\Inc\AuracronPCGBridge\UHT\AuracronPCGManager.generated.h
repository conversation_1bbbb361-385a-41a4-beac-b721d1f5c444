// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGManager.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGManager_generated_h
#error "AuracronPCGManager.generated.h already included, missing '#pragma once' in AuracronPCGManager.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UAuracronPCGSettingsBase;
class UPCGGraph;
struct FAuracronPCGConfiguration;
struct FAuracronPCGErrorInfo;
struct FAuracronPCGGenerationRequest;
struct FAuracronPCGGenerationResult;
struct FAuracronPCGPerformanceMetrics;

// ********** Begin Delegate FOnPCGGenerationComplete **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_28_DELEGATE \
AURACRONPCGBRIDGE_API void FOnPCGGenerationComplete_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationComplete, bool bSuccess, FAuracronPCGPerformanceMetrics const& Metrics);


// ********** End Delegate FOnPCGGenerationComplete ************************************************

// ********** Begin Delegate FOnPCGGenerationProgress **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_29_DELEGATE \
AURACRONPCGBRIDGE_API void FOnPCGGenerationProgress_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationProgress, float Progress, const FString& CurrentOperation);


// ********** End Delegate FOnPCGGenerationProgress ************************************************

// ********** Begin Delegate FOnPCGError ***********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_30_DELEGATE \
AURACRONPCGBRIDGE_API void FOnPCGError_DelegateWrapper(const FMulticastScriptDelegate& OnPCGError, FAuracronPCGErrorInfo const& ErrorInfo);


// ********** End Delegate FOnPCGError *************************************************************

// ********** Begin ScriptStruct FAuracronPCGGenerationRequest *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_36_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGGenerationRequest_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGGenerationRequest;
// ********** End ScriptStruct FAuracronPCGGenerationRequest ***************************************

// ********** Begin ScriptStruct FAuracronPCGGenerationResult **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_72_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGGenerationResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGGenerationResult;
// ********** End ScriptStruct FAuracronPCGGenerationResult ****************************************

// ********** Begin Class UAuracronPCGManager ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_112_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execValidateSystemRequirements); \
	DECLARE_FUNCTION(execOptimizeMemoryUsage); \
	DECLARE_FUNCTION(execCleanupGeneratedContent); \
	DECLARE_FUNCTION(execGetSystemHealthScore); \
	DECLARE_FUNCTION(execGetAvailableThreadCount); \
	DECLARE_FUNCTION(execIsGPUAccelerationAvailable); \
	DECLARE_FUNCTION(execGetVersionString); \
	DECLARE_FUNCTION(execReportError); \
	DECLARE_FUNCTION(execClearErrorHistory); \
	DECLARE_FUNCTION(execGetErrorHistory); \
	DECLARE_FUNCTION(execGetPerformanceRecommendations); \
	DECLARE_FUNCTION(execResetPerformanceMetrics); \
	DECLARE_FUNCTION(execGetCurrentPerformanceMetrics); \
	DECLARE_FUNCTION(execGetRegisteredElements); \
	DECLARE_FUNCTION(execUnregisterCustomElement); \
	DECLARE_FUNCTION(execRegisterCustomElement); \
	DECLARE_FUNCTION(execExecutePCGGraph); \
	DECLARE_FUNCTION(execValidatePCGGraph); \
	DECLARE_FUNCTION(execCreatePCGGraph); \
	DECLARE_FUNCTION(execGetGenerationResult); \
	DECLARE_FUNCTION(execGetGenerationProgress); \
	DECLARE_FUNCTION(execIsGenerationActive); \
	DECLARE_FUNCTION(execStopGeneration); \
	DECLARE_FUNCTION(execStartGeneration); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execApplyConfiguration); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_112_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGManager(); \
	friend struct Z_Construct_UClass_UAuracronPCGManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGManager)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_112_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGManager(UAuracronPCGManager&&) = delete; \
	UAuracronPCGManager(const UAuracronPCGManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGManager) \
	NO_API virtual ~UAuracronPCGManager();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_109_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_112_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_112_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_112_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h_112_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGManager;

// ********** End Class UAuracronPCGManager ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
