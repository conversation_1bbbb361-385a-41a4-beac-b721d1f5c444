// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronLoreBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronLoreBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONLOREBRIDGE_API UClass* Z_Construct_UClass_UAuracronLoreBridge();
AURACRONLOREBRIDGE_API UClass* Z_Construct_UClass_UAuracronLoreBridge_NoRegister();
AURACRONLOREBRIDGE_API UEnum* Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod();
AURACRONLOREBRIDGE_API UEnum* Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity();
AURACRONLOREBRIDGE_API UEnum* Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType();
AURACRONLOREBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature();
AURACRONLOREBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature();
AURACRONLOREBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature();
AURACRONLOREBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLoreCollection();
AURACRONLOREBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLoreEntry();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
LEVELSEQUENCE_API UClass* Z_Construct_UClass_ULevelSequence_NoRegister();
LEVELSEQUENCE_API UClass* Z_Construct_UClass_ULevelSequencePlayer_NoRegister();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
UMG_API UClass* Z_Construct_UClass_UUserWidget_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronLoreBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronLoreType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLoreType;
static UEnum* EAuracronLoreType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLoreType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLoreType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType, (UObject*)Z_Construct_UPackage__Script_AuracronLoreBridge(), TEXT("EAuracronLoreType"));
	}
	return Z_Registration_Info_UEnum_EAuracronLoreType.OuterSingleton;
}
template<> AURACRONLOREBRIDGE_API UEnum* StaticEnum<EAuracronLoreType>()
{
	return EAuracronLoreType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ChampionBackstory.DisplayName", "Champion Backstory" },
		{ "ChampionBackstory.Name", "EAuracronLoreType::ChampionBackstory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de lore\n */" },
#endif
		{ "EventLore.DisplayName", "Event Lore" },
		{ "EventLore.Name", "EAuracronLoreType::EventLore" },
		{ "FactionLore.DisplayName", "Faction Lore" },
		{ "FactionLore.Name", "EAuracronLoreType::FactionLore" },
		{ "ItemLore.DisplayName", "Item Lore" },
		{ "ItemLore.Name", "EAuracronLoreType::ItemLore" },
		{ "LocationLore.DisplayName", "Location Lore" },
		{ "LocationLore.Name", "EAuracronLoreType::LocationLore" },
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
		{ "MythologyLore.DisplayName", "Mythology Lore" },
		{ "MythologyLore.Name", "EAuracronLoreType::MythologyLore" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronLoreType::None" },
		{ "RealmHistory.DisplayName", "Realm History" },
		{ "RealmHistory.Name", "EAuracronLoreType::RealmHistory" },
		{ "SecretLore.DisplayName", "Secret Lore" },
		{ "SecretLore.Name", "EAuracronLoreType::SecretLore" },
		{ "SigiloOrigins.DisplayName", "Sigilo Origins" },
		{ "SigiloOrigins.Name", "EAuracronLoreType::SigiloOrigins" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de lore" },
#endif
		{ "WorldLore.DisplayName", "World Lore" },
		{ "WorldLore.Name", "EAuracronLoreType::WorldLore" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLoreType::None", (int64)EAuracronLoreType::None },
		{ "EAuracronLoreType::ChampionBackstory", (int64)EAuracronLoreType::ChampionBackstory },
		{ "EAuracronLoreType::RealmHistory", (int64)EAuracronLoreType::RealmHistory },
		{ "EAuracronLoreType::SigiloOrigins", (int64)EAuracronLoreType::SigiloOrigins },
		{ "EAuracronLoreType::WorldLore", (int64)EAuracronLoreType::WorldLore },
		{ "EAuracronLoreType::EventLore", (int64)EAuracronLoreType::EventLore },
		{ "EAuracronLoreType::ItemLore", (int64)EAuracronLoreType::ItemLore },
		{ "EAuracronLoreType::LocationLore", (int64)EAuracronLoreType::LocationLore },
		{ "EAuracronLoreType::FactionLore", (int64)EAuracronLoreType::FactionLore },
		{ "EAuracronLoreType::MythologyLore", (int64)EAuracronLoreType::MythologyLore },
		{ "EAuracronLoreType::SecretLore", (int64)EAuracronLoreType::SecretLore },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronLoreBridge,
	nullptr,
	"EAuracronLoreType",
	"EAuracronLoreType",
	Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType()
{
	if (!Z_Registration_Info_UEnum_EAuracronLoreType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLoreType.InnerSingleton, Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLoreType.InnerSingleton;
}
// ********** End Enum EAuracronLoreType ***********************************************************

// ********** Begin Enum EAuracronLoreRarity *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLoreRarity;
static UEnum* EAuracronLoreRarity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLoreRarity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLoreRarity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity, (UObject*)Z_Construct_UPackage__Script_AuracronLoreBridge(), TEXT("EAuracronLoreRarity"));
	}
	return Z_Registration_Info_UEnum_EAuracronLoreRarity.OuterSingleton;
}
template<> AURACRONLOREBRIDGE_API UEnum* StaticEnum<EAuracronLoreRarity>()
{
	return EAuracronLoreRarity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para raridade de lore\n */" },
#endif
		{ "Common.DisplayName", "Common" },
		{ "Common.Name", "EAuracronLoreRarity::Common" },
		{ "Epic.DisplayName", "Epic" },
		{ "Epic.Name", "EAuracronLoreRarity::Epic" },
		{ "Legendary.DisplayName", "Legendary" },
		{ "Legendary.Name", "EAuracronLoreRarity::Legendary" },
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
		{ "Mythic.DisplayName", "Mythic" },
		{ "Mythic.Name", "EAuracronLoreRarity::Mythic" },
		{ "Rare.DisplayName", "Rare" },
		{ "Rare.Name", "EAuracronLoreRarity::Rare" },
		{ "Secret.DisplayName", "Secret" },
		{ "Secret.Name", "EAuracronLoreRarity::Secret" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para raridade de lore" },
#endif
		{ "Uncommon.DisplayName", "Uncommon" },
		{ "Uncommon.Name", "EAuracronLoreRarity::Uncommon" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLoreRarity::Common", (int64)EAuracronLoreRarity::Common },
		{ "EAuracronLoreRarity::Uncommon", (int64)EAuracronLoreRarity::Uncommon },
		{ "EAuracronLoreRarity::Rare", (int64)EAuracronLoreRarity::Rare },
		{ "EAuracronLoreRarity::Epic", (int64)EAuracronLoreRarity::Epic },
		{ "EAuracronLoreRarity::Legendary", (int64)EAuracronLoreRarity::Legendary },
		{ "EAuracronLoreRarity::Mythic", (int64)EAuracronLoreRarity::Mythic },
		{ "EAuracronLoreRarity::Secret", (int64)EAuracronLoreRarity::Secret },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronLoreBridge,
	nullptr,
	"EAuracronLoreRarity",
	"EAuracronLoreRarity",
	Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity()
{
	if (!Z_Registration_Info_UEnum_EAuracronLoreRarity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLoreRarity.InnerSingleton, Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLoreRarity.InnerSingleton;
}
// ********** End Enum EAuracronLoreRarity *********************************************************

// ********** Begin Enum EAuracronLoreDiscoveryMethod **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLoreDiscoveryMethod;
static UEnum* EAuracronLoreDiscoveryMethod_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLoreDiscoveryMethod.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLoreDiscoveryMethod.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod, (UObject*)Z_Construct_UPackage__Script_AuracronLoreBridge(), TEXT("EAuracronLoreDiscoveryMethod"));
	}
	return Z_Registration_Info_UEnum_EAuracronLoreDiscoveryMethod.OuterSingleton;
}
template<> AURACRONLOREBRIDGE_API UEnum* StaticEnum<EAuracronLoreDiscoveryMethod>()
{
	return EAuracronLoreDiscoveryMethod_StaticEnum();
}
struct Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Achievement.DisplayName", "Achievement" },
		{ "Achievement.Name", "EAuracronLoreDiscoveryMethod::Achievement" },
		{ "Automatic.DisplayName", "Automatic" },
		{ "Automatic.Name", "EAuracronLoreDiscoveryMethod::Automatic" },
		{ "BlueprintType", "true" },
		{ "Combat.DisplayName", "Combat" },
		{ "Combat.Name", "EAuracronLoreDiscoveryMethod::Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para m\xc3\x83\xc2\xa9todo de descoberta\n */" },
#endif
		{ "Event.DisplayName", "Event" },
		{ "Event.Name", "EAuracronLoreDiscoveryMethod::Event" },
		{ "Exploration.DisplayName", "Exploration" },
		{ "Exploration.Name", "EAuracronLoreDiscoveryMethod::Exploration" },
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
		{ "Progression.DisplayName", "Progression" },
		{ "Progression.Name", "EAuracronLoreDiscoveryMethod::Progression" },
		{ "Purchase.DisplayName", "Purchase" },
		{ "Purchase.Name", "EAuracronLoreDiscoveryMethod::Purchase" },
		{ "Quest.DisplayName", "Quest" },
		{ "Quest.Name", "EAuracronLoreDiscoveryMethod::Quest" },
		{ "Secret.DisplayName", "Secret" },
		{ "Secret.Name", "EAuracronLoreDiscoveryMethod::Secret" },
		{ "Social.DisplayName", "Social" },
		{ "Social.Name", "EAuracronLoreDiscoveryMethod::Social" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para m\xc3\x83\xc2\xa9todo de descoberta" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLoreDiscoveryMethod::Automatic", (int64)EAuracronLoreDiscoveryMethod::Automatic },
		{ "EAuracronLoreDiscoveryMethod::Exploration", (int64)EAuracronLoreDiscoveryMethod::Exploration },
		{ "EAuracronLoreDiscoveryMethod::Combat", (int64)EAuracronLoreDiscoveryMethod::Combat },
		{ "EAuracronLoreDiscoveryMethod::Quest", (int64)EAuracronLoreDiscoveryMethod::Quest },
		{ "EAuracronLoreDiscoveryMethod::Achievement", (int64)EAuracronLoreDiscoveryMethod::Achievement },
		{ "EAuracronLoreDiscoveryMethod::Purchase", (int64)EAuracronLoreDiscoveryMethod::Purchase },
		{ "EAuracronLoreDiscoveryMethod::Event", (int64)EAuracronLoreDiscoveryMethod::Event },
		{ "EAuracronLoreDiscoveryMethod::Secret", (int64)EAuracronLoreDiscoveryMethod::Secret },
		{ "EAuracronLoreDiscoveryMethod::Social", (int64)EAuracronLoreDiscoveryMethod::Social },
		{ "EAuracronLoreDiscoveryMethod::Progression", (int64)EAuracronLoreDiscoveryMethod::Progression },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronLoreBridge,
	nullptr,
	"EAuracronLoreDiscoveryMethod",
	"EAuracronLoreDiscoveryMethod",
	Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod()
{
	if (!Z_Registration_Info_UEnum_EAuracronLoreDiscoveryMethod.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLoreDiscoveryMethod.InnerSingleton, Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLoreDiscoveryMethod.InnerSingleton;
}
// ********** End Enum EAuracronLoreDiscoveryMethod ************************************************

// ********** Begin ScriptStruct FAuracronLoreEntry ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLoreEntry;
class UScriptStruct* FAuracronLoreEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLoreEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLoreEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLoreEntry, (UObject*)Z_Construct_UPackage__Script_AuracronLoreBridge(), TEXT("AuracronLoreEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLoreEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para entrada de lore\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para entrada de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico da entrada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico da entrada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreTitle_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** T\xc3\x83\xc2\xadtulo da entrada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "T\xc3\x83\xc2\xadtulo da entrada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreContent_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Conte\xc3\x83\xc2\xba""do da entrada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conte\xc3\x83\xc2\xba""do da entrada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreSummary_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resumo da entrada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resumo da entrada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreType_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de lore */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreRarity_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raridade do lore */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raridade do lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiscoveryMethod_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xa9todo de descoberta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xa9todo de descoberta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDiscovered_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Foi descoberto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foi descoberto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRead_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Foi lido */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foi lido" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiscoveryDate_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de descoberta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de descoberta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReadDate_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de leitura */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de leitura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreImage_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Imagem do lore */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Imagem do lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreAudio_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x81udio do lore */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x81udio do lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreCinematic_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sequ\xc3\x83\xc2\xaancia cinem\xc3\x83\xc2\xa1tica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sequ\xc3\x83\xc2\xaancia cinem\xc3\x83\xc2\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelatedLocation_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o relacionada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o relacionada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelatedRealm_MetaData[] = {
		{ "Category", "Lore Entry" },
		{ "ClampMax", "2" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm relacionado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm relacionado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelatedChampion_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Campe\xc3\x83\xc2\xa3o relacionado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Campe\xc3\x83\xc2\xa3o relacionado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelatedEntries_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Entradas relacionadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entradas relacionadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiscoveryPrerequisites_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pr\xc3\x83\xc2\xa9-requisitos para descoberta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pr\xc3\x83\xc2\xa9-requisitos para descoberta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiscoveryConditions_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Condi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de descoberta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de descoberta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiscoveryRewards_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas por descoberta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas por descoberta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LorePoints_MetaData[] = {
		{ "Category", "Lore Entry" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos de lore concedidos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos de lore concedidos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceReward_MetaData[] = {
		{ "Category", "Lore Entry" },
		{ "ClampMax", "10000" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Experi\xc3\x83\xc2\xaancia concedida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Experi\xc3\x83\xc2\xaancia concedida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreTags_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags do lore */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags do lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomMetadata_MetaData[] = {
		{ "Category", "Lore Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Metadados customizados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Metadados customizados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_LoreTitle;
	static const UECodeGen_Private::FTextPropertyParams NewProp_LoreContent;
	static const UECodeGen_Private::FTextPropertyParams NewProp_LoreSummary;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LoreType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LoreType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LoreRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LoreRarity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DiscoveryMethod_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DiscoveryMethod;
	static void NewProp_bIsDiscovered_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDiscovered;
	static void NewProp_bIsRead_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRead;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DiscoveryDate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReadDate;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LoreImage;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LoreAudio;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LoreCinematic;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RelatedLocation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RelatedRealm;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RelatedChampion;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RelatedEntries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RelatedEntries;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DiscoveryPrerequisites_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DiscoveryPrerequisites;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DiscoveryConditions_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DiscoveryConditions_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_DiscoveryConditions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DiscoveryRewards_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DiscoveryRewards_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_DiscoveryRewards;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LorePoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExperienceReward;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LoreTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomMetadata_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomMetadata_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomMetadata;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLoreEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreTitle = { "LoreTitle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LoreTitle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreTitle_MetaData), NewProp_LoreTitle_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreContent = { "LoreContent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LoreContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreContent_MetaData), NewProp_LoreContent_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreSummary = { "LoreSummary", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LoreSummary), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreSummary_MetaData), NewProp_LoreSummary_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreType = { "LoreType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LoreType), Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreType_MetaData), NewProp_LoreType_MetaData) }; // 3360407225
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreRarity = { "LoreRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LoreRarity), Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreRarity_MetaData), NewProp_LoreRarity_MetaData) }; // 960223969
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryMethod_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryMethod = { "DiscoveryMethod", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, DiscoveryMethod), Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiscoveryMethod_MetaData), NewProp_DiscoveryMethod_MetaData) }; // 1081072492
void Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_bIsDiscovered_SetBit(void* Obj)
{
	((FAuracronLoreEntry*)Obj)->bIsDiscovered = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_bIsDiscovered = { "bIsDiscovered", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLoreEntry), &Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_bIsDiscovered_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDiscovered_MetaData), NewProp_bIsDiscovered_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_bIsRead_SetBit(void* Obj)
{
	((FAuracronLoreEntry*)Obj)->bIsRead = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_bIsRead = { "bIsRead", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLoreEntry), &Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_bIsRead_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRead_MetaData), NewProp_bIsRead_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryDate = { "DiscoveryDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, DiscoveryDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiscoveryDate_MetaData), NewProp_DiscoveryDate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_ReadDate = { "ReadDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, ReadDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReadDate_MetaData), NewProp_ReadDate_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreImage = { "LoreImage", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LoreImage), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreImage_MetaData), NewProp_LoreImage_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreAudio = { "LoreAudio", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LoreAudio), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreAudio_MetaData), NewProp_LoreAudio_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreCinematic = { "LoreCinematic", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LoreCinematic), Z_Construct_UClass_ULevelSequence_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreCinematic_MetaData), NewProp_LoreCinematic_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_RelatedLocation = { "RelatedLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, RelatedLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelatedLocation_MetaData), NewProp_RelatedLocation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_RelatedRealm = { "RelatedRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, RelatedRealm), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelatedRealm_MetaData), NewProp_RelatedRealm_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_RelatedChampion = { "RelatedChampion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, RelatedChampion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelatedChampion_MetaData), NewProp_RelatedChampion_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_RelatedEntries_Inner = { "RelatedEntries", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_RelatedEntries = { "RelatedEntries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, RelatedEntries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelatedEntries_MetaData), NewProp_RelatedEntries_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryPrerequisites_Inner = { "DiscoveryPrerequisites", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryPrerequisites = { "DiscoveryPrerequisites", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, DiscoveryPrerequisites), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiscoveryPrerequisites_MetaData), NewProp_DiscoveryPrerequisites_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryConditions_ValueProp = { "DiscoveryConditions", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryConditions_Key_KeyProp = { "DiscoveryConditions_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryConditions = { "DiscoveryConditions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, DiscoveryConditions), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiscoveryConditions_MetaData), NewProp_DiscoveryConditions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryRewards_ValueProp = { "DiscoveryRewards", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryRewards_Key_KeyProp = { "DiscoveryRewards_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryRewards = { "DiscoveryRewards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, DiscoveryRewards), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiscoveryRewards_MetaData), NewProp_DiscoveryRewards_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LorePoints = { "LorePoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LorePoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LorePoints_MetaData), NewProp_LorePoints_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_ExperienceReward = { "ExperienceReward", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, ExperienceReward), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceReward_MetaData), NewProp_ExperienceReward_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreTags = { "LoreTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, LoreTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreTags_MetaData), NewProp_LoreTags_MetaData) }; // 2104890724
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_CustomMetadata_ValueProp = { "CustomMetadata", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_CustomMetadata_Key_KeyProp = { "CustomMetadata_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_CustomMetadata = { "CustomMetadata", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreEntry, CustomMetadata), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomMetadata_MetaData), NewProp_CustomMetadata_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreTitle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreSummary,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryMethod_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryMethod,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_bIsDiscovered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_bIsRead,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_ReadDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreCinematic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_RelatedLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_RelatedRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_RelatedChampion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_RelatedEntries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_RelatedEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryPrerequisites_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryPrerequisites,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryConditions_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryConditions_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryConditions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryRewards_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryRewards_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_DiscoveryRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LorePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_ExperienceReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_LoreTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_CustomMetadata_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_CustomMetadata_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewProp_CustomMetadata,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronLoreBridge,
	nullptr,
	&NewStructOps,
	"AuracronLoreEntry",
	Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::PropPointers),
	sizeof(FAuracronLoreEntry),
	alignof(FAuracronLoreEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLoreEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLoreEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLoreEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLoreEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLoreEntry **************************************************

// ********** Begin ScriptStruct FAuracronLoreCollection *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLoreCollection;
class UScriptStruct* FAuracronLoreCollection::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLoreCollection.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLoreCollection.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLoreCollection, (UObject*)Z_Construct_UPackage__Script_AuracronLoreBridge(), TEXT("AuracronLoreCollection"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLoreCollection.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de lore\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectionID_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectionName_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectionDescription_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreEntryIDs_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Entradas na cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entradas na cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCompleted_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o foi completada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o foi completada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletionProgress_MetaData[] = {
		{ "Category", "Lore Collection" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Progresso da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progresso da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletionRewards_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensa de conclus\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensa de conclus\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectionIcon_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x8d""cone da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x8d""cone da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectionColor_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayOrder_MetaData[] = {
		{ "Category", "Lore Collection" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ordem de exibi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ordem de exibi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSecret_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 secreta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 secreta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationDate_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de cria\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de cria\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletionDate_MetaData[] = {
		{ "Category", "Lore Collection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de conclus\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de conclus\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollectionID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_CollectionName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_CollectionDescription;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreEntryIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LoreEntryIDs;
	static void NewProp_bIsCompleted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCompleted;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompletionProgress;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompletionRewards_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompletionRewards_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CompletionRewards;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_CollectionIcon;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollectionColor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DisplayOrder;
	static void NewProp_bIsSecret_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSecret;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationDate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletionDate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLoreCollection>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CollectionID = { "CollectionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, CollectionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectionID_MetaData), NewProp_CollectionID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CollectionName = { "CollectionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, CollectionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectionName_MetaData), NewProp_CollectionName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CollectionDescription = { "CollectionDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, CollectionDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectionDescription_MetaData), NewProp_CollectionDescription_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_LoreEntryIDs_Inner = { "LoreEntryIDs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_LoreEntryIDs = { "LoreEntryIDs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, LoreEntryIDs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreEntryIDs_MetaData), NewProp_LoreEntryIDs_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_bIsCompleted_SetBit(void* Obj)
{
	((FAuracronLoreCollection*)Obj)->bIsCompleted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_bIsCompleted = { "bIsCompleted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLoreCollection), &Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_bIsCompleted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCompleted_MetaData), NewProp_bIsCompleted_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CompletionProgress = { "CompletionProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, CompletionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletionProgress_MetaData), NewProp_CompletionProgress_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CompletionRewards_ValueProp = { "CompletionRewards", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CompletionRewards_Key_KeyProp = { "CompletionRewards_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CompletionRewards = { "CompletionRewards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, CompletionRewards), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletionRewards_MetaData), NewProp_CompletionRewards_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CollectionIcon = { "CollectionIcon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, CollectionIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectionIcon_MetaData), NewProp_CollectionIcon_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CollectionColor = { "CollectionColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, CollectionColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectionColor_MetaData), NewProp_CollectionColor_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_DisplayOrder = { "DisplayOrder", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, DisplayOrder), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayOrder_MetaData), NewProp_DisplayOrder_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_bIsSecret_SetBit(void* Obj)
{
	((FAuracronLoreCollection*)Obj)->bIsSecret = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_bIsSecret = { "bIsSecret", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLoreCollection), &Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_bIsSecret_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSecret_MetaData), NewProp_bIsSecret_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CreationDate = { "CreationDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, CreationDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationDate_MetaData), NewProp_CreationDate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CompletionDate = { "CompletionDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLoreCollection, CompletionDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletionDate_MetaData), NewProp_CompletionDate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CollectionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CollectionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CollectionDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_LoreEntryIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_LoreEntryIDs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_bIsCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CompletionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CompletionRewards_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CompletionRewards_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CompletionRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CollectionIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CollectionColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_DisplayOrder,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_bIsSecret,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CreationDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewProp_CompletionDate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronLoreBridge,
	nullptr,
	&NewStructOps,
	"AuracronLoreCollection",
	Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::PropPointers),
	sizeof(FAuracronLoreCollection),
	alignof(FAuracronLoreCollection),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLoreCollection()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLoreCollection.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLoreCollection.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLoreCollection.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLoreCollection *********************************************

// ********** Begin Delegate FOnLoreDiscovered *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics
{
	struct AuracronLoreBridge_eventOnLoreDiscovered_Parms
	{
		FString LoreID;
		EAuracronLoreDiscoveryMethod DiscoveryMethod;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando lore \xc3\x83\xc2\xa9 descoberto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando lore \xc3\x83\xc2\xa9 descoberto" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DiscoveryMethod_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DiscoveryMethod;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventOnLoreDiscovered_Parms, LoreID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::NewProp_DiscoveryMethod_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::NewProp_DiscoveryMethod = { "DiscoveryMethod", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventOnLoreDiscovered_Parms, DiscoveryMethod), Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod, METADATA_PARAMS(0, nullptr) }; // 1081072492
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::NewProp_DiscoveryMethod_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::NewProp_DiscoveryMethod,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "OnLoreDiscovered__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::AuracronLoreBridge_eventOnLoreDiscovered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::AuracronLoreBridge_eventOnLoreDiscovered_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronLoreBridge::FOnLoreDiscovered_DelegateWrapper(const FMulticastScriptDelegate& OnLoreDiscovered, const FString& LoreID, EAuracronLoreDiscoveryMethod DiscoveryMethod)
{
	struct AuracronLoreBridge_eventOnLoreDiscovered_Parms
	{
		FString LoreID;
		EAuracronLoreDiscoveryMethod DiscoveryMethod;
	};
	AuracronLoreBridge_eventOnLoreDiscovered_Parms Parms;
	Parms.LoreID=LoreID;
	Parms.DiscoveryMethod=DiscoveryMethod;
	OnLoreDiscovered.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLoreDiscovered *******************************************************

// ********** Begin Delegate FOnLoreRead ***********************************************************
struct Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics
{
	struct AuracronLoreBridge_eventOnLoreRead_Parms
	{
		FString LoreID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando lore \xc3\x83\xc2\xa9 lido */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando lore \xc3\x83\xc2\xa9 lido" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventOnLoreRead_Parms, LoreID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::NewProp_LoreID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "OnLoreRead__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::AuracronLoreBridge_eventOnLoreRead_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::AuracronLoreBridge_eventOnLoreRead_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronLoreBridge::FOnLoreRead_DelegateWrapper(const FMulticastScriptDelegate& OnLoreRead, const FString& LoreID)
{
	struct AuracronLoreBridge_eventOnLoreRead_Parms
	{
		FString LoreID;
	};
	AuracronLoreBridge_eventOnLoreRead_Parms Parms;
	Parms.LoreID=LoreID;
	OnLoreRead.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLoreRead *************************************************************

// ********** Begin Delegate FOnCollectionCompleted ************************************************
struct Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics
{
	struct AuracronLoreBridge_eventOnCollectionCompleted_Parms
	{
		FString CollectionID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 completada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 completada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollectionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::NewProp_CollectionID = { "CollectionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventOnCollectionCompleted_Parms, CollectionID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::NewProp_CollectionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "OnCollectionCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::AuracronLoreBridge_eventOnCollectionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::AuracronLoreBridge_eventOnCollectionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronLoreBridge::FOnCollectionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnCollectionCompleted, const FString& CollectionID)
{
	struct AuracronLoreBridge_eventOnCollectionCompleted_Parms
	{
		FString CollectionID;
	};
	AuracronLoreBridge_eventOnCollectionCompleted_Parms Parms;
	Parms.CollectionID=CollectionID;
	OnCollectionCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCollectionCompleted **************************************************

// ********** Begin Class UAuracronLoreBridge Function CheckDiscoveryConditions ********************
struct Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics
{
	struct AuracronLoreBridge_eventCheckDiscoveryConditions_Parms
	{
		FString LoreID;
		TMap<FString,FString> CurrentConditions;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Discovery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar condi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de descoberta\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar condi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de descoberta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentConditions_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentConditions_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentConditions_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CurrentConditions;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventCheckDiscoveryConditions_Parms, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_CurrentConditions_ValueProp = { "CurrentConditions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_CurrentConditions_Key_KeyProp = { "CurrentConditions_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_CurrentConditions = { "CurrentConditions", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventCheckDiscoveryConditions_Parms, CurrentConditions), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentConditions_MetaData), NewProp_CurrentConditions_MetaData) };
void Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventCheckDiscoveryConditions_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventCheckDiscoveryConditions_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_CurrentConditions_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_CurrentConditions_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_CurrentConditions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "CheckDiscoveryConditions", Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::AuracronLoreBridge_eventCheckDiscoveryConditions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::AuracronLoreBridge_eventCheckDiscoveryConditions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execCheckDiscoveryConditions)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoreID);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_CurrentConditions);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CheckDiscoveryConditions(Z_Param_LoreID,Z_Param_Out_CurrentConditions);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function CheckDiscoveryConditions **********************

// ********** Begin Class UAuracronLoreBridge Function CreateLoreCollection ************************
struct Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics
{
	struct AuracronLoreBridge_eventCreateLoreCollection_Parms
	{
		FAuracronLoreCollection CollectionConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Collections" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectionConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollectionConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::NewProp_CollectionConfig = { "CollectionConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventCreateLoreCollection_Parms, CollectionConfig), Z_Construct_UScriptStruct_FAuracronLoreCollection, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectionConfig_MetaData), NewProp_CollectionConfig_MetaData) }; // 2033351928
void Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventCreateLoreCollection_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventCreateLoreCollection_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::NewProp_CollectionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "CreateLoreCollection", Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::AuracronLoreBridge_eventCreateLoreCollection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::AuracronLoreBridge_eventCreateLoreCollection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execCreateLoreCollection)
{
	P_GET_STRUCT_REF(FAuracronLoreCollection,Z_Param_Out_CollectionConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateLoreCollection(Z_Param_Out_CollectionConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function CreateLoreCollection **************************

// ********** Begin Class UAuracronLoreBridge Function DiscoverLoreByAction ************************
struct Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics
{
	struct AuracronLoreBridge_eventDiscoverLoreByAction_Parms
	{
		FString ActionType;
		TMap<FString,FString> ActionData;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Discovery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Descobrir lore por a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descobrir lore por a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActionData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventDiscoverLoreByAction_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ActionData_ValueProp = { "ActionData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ActionData_Key_KeyProp = { "ActionData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ActionData = { "ActionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventDiscoverLoreByAction_Parms, ActionData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionData_MetaData), NewProp_ActionData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventDiscoverLoreByAction_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ActionData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ActionData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ActionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "DiscoverLoreByAction", Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::AuracronLoreBridge_eventDiscoverLoreByAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::AuracronLoreBridge_eventDiscoverLoreByAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execDiscoverLoreByAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_ActionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->DiscoverLoreByAction(Z_Param_ActionType,Z_Param_Out_ActionData);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function DiscoverLoreByAction **************************

// ********** Begin Class UAuracronLoreBridge Function DiscoverLoreByChampion **********************
struct Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics
{
	struct AuracronLoreBridge_eventDiscoverLoreByChampion_Parms
	{
		FString ChampionID;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Discovery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Descobrir lore por campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descobrir lore por campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventDiscoverLoreByChampion_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventDiscoverLoreByChampion_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "DiscoverLoreByChampion", Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::AuracronLoreBridge_eventDiscoverLoreByChampion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::AuracronLoreBridge_eventDiscoverLoreByChampion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execDiscoverLoreByChampion)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->DiscoverLoreByChampion(Z_Param_ChampionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function DiscoverLoreByChampion ************************

// ********** Begin Class UAuracronLoreBridge Function DiscoverLoreByLocation **********************
struct Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics
{
	struct AuracronLoreBridge_eventDiscoverLoreByLocation_Parms
	{
		FVector Location;
		float Radius;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Discovery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Descobrir lore por localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "CPP_Default_Radius", "500.000000" },
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descobrir lore por localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventDiscoverLoreByLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventDiscoverLoreByLocation_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventDiscoverLoreByLocation_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "DiscoverLoreByLocation", Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::AuracronLoreBridge_eventDiscoverLoreByLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::AuracronLoreBridge_eventDiscoverLoreByLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execDiscoverLoreByLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->DiscoverLoreByLocation(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function DiscoverLoreByLocation ************************

// ********** Begin Class UAuracronLoreBridge Function DiscoverLoreEntry ***************************
struct Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics
{
	struct AuracronLoreBridge_eventDiscoverLoreEntry_Parms
	{
		FString LoreID;
		EAuracronLoreDiscoveryMethod DiscoveryMethod;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Discovery" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Descobrir entrada de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descobrir entrada de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DiscoveryMethod_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DiscoveryMethod;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventDiscoverLoreEntry_Parms, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::NewProp_DiscoveryMethod_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::NewProp_DiscoveryMethod = { "DiscoveryMethod", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventDiscoverLoreEntry_Parms, DiscoveryMethod), Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod, METADATA_PARAMS(0, nullptr) }; // 1081072492
void Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventDiscoverLoreEntry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventDiscoverLoreEntry_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::NewProp_DiscoveryMethod_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::NewProp_DiscoveryMethod,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "DiscoverLoreEntry", Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::AuracronLoreBridge_eventDiscoverLoreEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::AuracronLoreBridge_eventDiscoverLoreEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execDiscoverLoreEntry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoreID);
	P_GET_ENUM(EAuracronLoreDiscoveryMethod,Z_Param_DiscoveryMethod);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DiscoverLoreEntry(Z_Param_LoreID,EAuracronLoreDiscoveryMethod(Z_Param_DiscoveryMethod));
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function DiscoverLoreEntry *****************************

// ********** Begin Class UAuracronLoreBridge Function GetAllLoreCollections ***********************
struct Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics
{
	struct AuracronLoreBridge_eventGetAllLoreCollections_Parms
	{
		TArray<FAuracronLoreCollection> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Collections" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter todas as cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todas as cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLoreCollection, METADATA_PARAMS(0, nullptr) }; // 2033351928
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventGetAllLoreCollections_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2033351928
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "GetAllLoreCollections", Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::AuracronLoreBridge_eventGetAllLoreCollections_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::AuracronLoreBridge_eventGetAllLoreCollections_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execGetAllLoreCollections)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLoreCollection>*)Z_Param__Result=P_THIS->GetAllLoreCollections();
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function GetAllLoreCollections *************************

// ********** Begin Class UAuracronLoreBridge Function GetDiscoveredLoreEntries ********************
struct Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics
{
	struct AuracronLoreBridge_eventGetDiscoveredLoreEntries_Parms
	{
		TArray<FAuracronLoreEntry> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Access" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter todas as entradas descobertas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todas as entradas descobertas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLoreEntry, METADATA_PARAMS(0, nullptr) }; // 1821530185
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventGetDiscoveredLoreEntries_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1821530185
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "GetDiscoveredLoreEntries", Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::AuracronLoreBridge_eventGetDiscoveredLoreEntries_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::AuracronLoreBridge_eventGetDiscoveredLoreEntries_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execGetDiscoveredLoreEntries)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLoreEntry>*)Z_Param__Result=P_THIS->GetDiscoveredLoreEntries();
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function GetDiscoveredLoreEntries **********************

// ********** Begin Class UAuracronLoreBridge Function GetLoreCollection ***************************
struct Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics
{
	struct AuracronLoreBridge_eventGetLoreCollection_Parms
	{
		FString CollectionID;
		FAuracronLoreCollection ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Collections" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollectionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::NewProp_CollectionID = { "CollectionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventGetLoreCollection_Parms, CollectionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectionID_MetaData), NewProp_CollectionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventGetLoreCollection_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLoreCollection, METADATA_PARAMS(0, nullptr) }; // 2033351928
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::NewProp_CollectionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "GetLoreCollection", Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::AuracronLoreBridge_eventGetLoreCollection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::AuracronLoreBridge_eventGetLoreCollection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execGetLoreCollection)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollectionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLoreCollection*)Z_Param__Result=P_THIS->GetLoreCollection(Z_Param_CollectionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function GetLoreCollection *****************************

// ********** Begin Class UAuracronLoreBridge Function GetLoreEntry ********************************
struct Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics
{
	struct AuracronLoreBridge_eventGetLoreEntry_Parms
	{
		FString LoreID;
		FAuracronLoreEntry ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Access" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter entrada de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter entrada de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventGetLoreEntry_Parms, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventGetLoreEntry_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLoreEntry, METADATA_PARAMS(0, nullptr) }; // 1821530185
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "GetLoreEntry", Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::AuracronLoreBridge_eventGetLoreEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::AuracronLoreBridge_eventGetLoreEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execGetLoreEntry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoreID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLoreEntry*)Z_Param__Result=P_THIS->GetLoreEntry(Z_Param_LoreID);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function GetLoreEntry **********************************

// ********** Begin Class UAuracronLoreBridge Function GetLoreStatistics ***************************
struct Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics
{
	struct AuracronLoreBridge_eventGetLoreStatistics_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estat\xc3\x83\xc2\xadsticas de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\x83\xc2\xadsticas de lore" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventGetLoreStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "GetLoreStatistics", Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::AuracronLoreBridge_eventGetLoreStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::AuracronLoreBridge_eventGetLoreStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execGetLoreStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetLoreStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function GetLoreStatistics *****************************

// ********** Begin Class UAuracronLoreBridge Function HideLoreUI **********************************
struct Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics
{
	struct AuracronLoreBridge_eventHideLoreUI_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Interactive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Esconder UI de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Esconder UI de lore" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventHideLoreUI_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventHideLoreUI_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "HideLoreUI", Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::AuracronLoreBridge_eventHideLoreUI_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::AuracronLoreBridge_eventHideLoreUI_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execHideLoreUI)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HideLoreUI();
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function HideLoreUI ************************************

// ********** Begin Class UAuracronLoreBridge Function IsCollectionCompleted ***********************
struct Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics
{
	struct AuracronLoreBridge_eventIsCollectionCompleted_Parms
	{
		FString CollectionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Collections" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o foi completada\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o foi completada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollectionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::NewProp_CollectionID = { "CollectionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventIsCollectionCompleted_Parms, CollectionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectionID_MetaData), NewProp_CollectionID_MetaData) };
void Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventIsCollectionCompleted_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventIsCollectionCompleted_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::NewProp_CollectionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "IsCollectionCompleted", Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::AuracronLoreBridge_eventIsCollectionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::AuracronLoreBridge_eventIsCollectionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execIsCollectionCompleted)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollectionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCollectionCompleted(Z_Param_CollectionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function IsCollectionCompleted *************************

// ********** Begin Class UAuracronLoreBridge Function IsLoreDiscovered ****************************
struct Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics
{
	struct AuracronLoreBridge_eventIsLoreDiscovered_Parms
	{
		FString LoreID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Access" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se lore foi descoberto\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se lore foi descoberto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventIsLoreDiscovered_Parms, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
void Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventIsLoreDiscovered_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventIsLoreDiscovered_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "IsLoreDiscovered", Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::AuracronLoreBridge_eventIsLoreDiscovered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::AuracronLoreBridge_eventIsLoreDiscovered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execIsLoreDiscovered)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoreID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLoreDiscovered(Z_Param_LoreID);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function IsLoreDiscovered ******************************

// ********** Begin Class UAuracronLoreBridge Function PlayLoreAudio *******************************
struct Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics
{
	struct AuracronLoreBridge_eventPlayLoreAudio_Parms
	{
		FString LoreID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Interactive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir \xc3\x83\xc2\xa1udio de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir \xc3\x83\xc2\xa1udio de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventPlayLoreAudio_Parms, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
void Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventPlayLoreAudio_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventPlayLoreAudio_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "PlayLoreAudio", Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::AuracronLoreBridge_eventPlayLoreAudio_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::AuracronLoreBridge_eventPlayLoreAudio_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execPlayLoreAudio)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoreID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayLoreAudio(Z_Param_LoreID);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function PlayLoreAudio *********************************

// ********** Begin Class UAuracronLoreBridge Function PlayLoreCinematic ***************************
struct Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics
{
	struct AuracronLoreBridge_eventPlayLoreCinematic_Parms
	{
		FString LoreID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Interactive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir cinem\xc3\x83\xc2\xa1tica de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir cinem\xc3\x83\xc2\xa1tica de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventPlayLoreCinematic_Parms, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
void Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventPlayLoreCinematic_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventPlayLoreCinematic_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "PlayLoreCinematic", Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::AuracronLoreBridge_eventPlayLoreCinematic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::AuracronLoreBridge_eventPlayLoreCinematic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execPlayLoreCinematic)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoreID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayLoreCinematic(Z_Param_LoreID);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function PlayLoreCinematic *****************************

// ********** Begin Class UAuracronLoreBridge Function ReadLoreEntry *******************************
struct Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics
{
	struct AuracronLoreBridge_eventReadLoreEntry_Parms
	{
		FString LoreID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Reading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ler entrada de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ler entrada de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventReadLoreEntry_Parms, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
void Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventReadLoreEntry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventReadLoreEntry_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "ReadLoreEntry", Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::AuracronLoreBridge_eventReadLoreEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::AuracronLoreBridge_eventReadLoreEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execReadLoreEntry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoreID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ReadLoreEntry(Z_Param_LoreID);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function ReadLoreEntry *********************************

// ********** Begin Class UAuracronLoreBridge Function ShowLoreUI **********************************
struct Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics
{
	struct AuracronLoreBridge_eventShowLoreUI_Parms
	{
		FString LoreID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Interactive" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Mostrar UI de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mostrar UI de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventShowLoreUI_Parms, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
void Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventShowLoreUI_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventShowLoreUI_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "ShowLoreUI", Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::AuracronLoreBridge_eventShowLoreUI_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::AuracronLoreBridge_eventShowLoreUI_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execShowLoreUI)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoreID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShowLoreUI(Z_Param_LoreID);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function ShowLoreUI ************************************

// ********** Begin Class UAuracronLoreBridge Function TrackLoreDiscovery **************************
struct Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics
{
	struct AuracronLoreBridge_eventTrackLoreDiscovery_Parms
	{
		FString LoreID;
		EAuracronLoreDiscoveryMethod Method;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Rastrear descoberta de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rastrear descoberta de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Method_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Method;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventTrackLoreDiscovery_Parms, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::NewProp_Method_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::NewProp_Method = { "Method", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventTrackLoreDiscovery_Parms, Method), Z_Construct_UEnum_AuracronLoreBridge_EAuracronLoreDiscoveryMethod, METADATA_PARAMS(0, nullptr) }; // 1081072492
void Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventTrackLoreDiscovery_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventTrackLoreDiscovery_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::NewProp_Method_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::NewProp_Method,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "TrackLoreDiscovery", Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::AuracronLoreBridge_eventTrackLoreDiscovery_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::AuracronLoreBridge_eventTrackLoreDiscovery_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execTrackLoreDiscovery)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoreID);
	P_GET_ENUM(EAuracronLoreDiscoveryMethod,Z_Param_Method);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TrackLoreDiscovery(Z_Param_LoreID,EAuracronLoreDiscoveryMethod(Z_Param_Method));
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function TrackLoreDiscovery ****************************

// ********** Begin Class UAuracronLoreBridge Function TrackLoreReading ****************************
struct Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics
{
	struct AuracronLoreBridge_eventTrackLoreReading_Parms
	{
		FString LoreID;
		float ReadingTime;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Rastrear leitura de lore\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rastrear leitura de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoreID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReadingTime;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::NewProp_LoreID = { "LoreID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventTrackLoreReading_Parms, LoreID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreID_MetaData), NewProp_LoreID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::NewProp_ReadingTime = { "ReadingTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventTrackLoreReading_Parms, ReadingTime), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventTrackLoreReading_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventTrackLoreReading_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::NewProp_LoreID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::NewProp_ReadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "TrackLoreReading", Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::AuracronLoreBridge_eventTrackLoreReading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::AuracronLoreBridge_eventTrackLoreReading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execTrackLoreReading)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoreID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ReadingTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TrackLoreReading(Z_Param_LoreID,Z_Param_ReadingTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function TrackLoreReading ******************************

// ********** Begin Class UAuracronLoreBridge Function UpdateCollectionProgress ********************
struct Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics
{
	struct AuracronLoreBridge_eventUpdateCollectionProgress_Parms
	{
		FString CollectionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Lore|Collections" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar progresso da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar progresso da cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollectionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::NewProp_CollectionID = { "CollectionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLoreBridge_eventUpdateCollectionProgress_Parms, CollectionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectionID_MetaData), NewProp_CollectionID_MetaData) };
void Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLoreBridge_eventUpdateCollectionProgress_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLoreBridge_eventUpdateCollectionProgress_Parms), &Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::NewProp_CollectionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLoreBridge, nullptr, "UpdateCollectionProgress", Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::AuracronLoreBridge_eventUpdateCollectionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::AuracronLoreBridge_eventUpdateCollectionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLoreBridge::execUpdateCollectionProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollectionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateCollectionProgress(Z_Param_CollectionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronLoreBridge Function UpdateCollectionProgress **********************

// ********** Begin Class UAuracronLoreBridge ******************************************************
void UAuracronLoreBridge::StaticRegisterNativesUAuracronLoreBridge()
{
	UClass* Class = UAuracronLoreBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CheckDiscoveryConditions", &UAuracronLoreBridge::execCheckDiscoveryConditions },
		{ "CreateLoreCollection", &UAuracronLoreBridge::execCreateLoreCollection },
		{ "DiscoverLoreByAction", &UAuracronLoreBridge::execDiscoverLoreByAction },
		{ "DiscoverLoreByChampion", &UAuracronLoreBridge::execDiscoverLoreByChampion },
		{ "DiscoverLoreByLocation", &UAuracronLoreBridge::execDiscoverLoreByLocation },
		{ "DiscoverLoreEntry", &UAuracronLoreBridge::execDiscoverLoreEntry },
		{ "GetAllLoreCollections", &UAuracronLoreBridge::execGetAllLoreCollections },
		{ "GetDiscoveredLoreEntries", &UAuracronLoreBridge::execGetDiscoveredLoreEntries },
		{ "GetLoreCollection", &UAuracronLoreBridge::execGetLoreCollection },
		{ "GetLoreEntry", &UAuracronLoreBridge::execGetLoreEntry },
		{ "GetLoreStatistics", &UAuracronLoreBridge::execGetLoreStatistics },
		{ "HideLoreUI", &UAuracronLoreBridge::execHideLoreUI },
		{ "IsCollectionCompleted", &UAuracronLoreBridge::execIsCollectionCompleted },
		{ "IsLoreDiscovered", &UAuracronLoreBridge::execIsLoreDiscovered },
		{ "PlayLoreAudio", &UAuracronLoreBridge::execPlayLoreAudio },
		{ "PlayLoreCinematic", &UAuracronLoreBridge::execPlayLoreCinematic },
		{ "ReadLoreEntry", &UAuracronLoreBridge::execReadLoreEntry },
		{ "ShowLoreUI", &UAuracronLoreBridge::execShowLoreUI },
		{ "TrackLoreDiscovery", &UAuracronLoreBridge::execTrackLoreDiscovery },
		{ "TrackLoreReading", &UAuracronLoreBridge::execTrackLoreReading },
		{ "UpdateCollectionProgress", &UAuracronLoreBridge::execUpdateCollectionProgress },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronLoreBridge;
UClass* UAuracronLoreBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronLoreBridge;
	if (!Z_Registration_Info_UClass_UAuracronLoreBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronLoreBridge"),
			Z_Registration_Info_UClass_UAuracronLoreBridge.InnerSingleton,
			StaticRegisterNativesUAuracronLoreBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronLoreBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronLoreBridge_NoRegister()
{
	return UAuracronLoreBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronLoreBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Lore" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Lore Din\xc3\x83\xc2\xa2mico\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de lore interativo\n */" },
#endif
		{ "DisplayName", "AURACRON Lore Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronLoreBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Lore Din\xc3\x83\xc2\xa2mico\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de lore interativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllLoreEntries_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Todas as entradas de lore */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Todas as entradas de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoreCollections_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de lore */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cole\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de lore" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiscoveredLoreIDs_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Entradas descobertas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entradas descobertas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReadLoreIDs_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Entradas lidas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entradas lidas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalLorePoints_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos de lore totais */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos de lore totais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLoreWidget_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Widget de lore atual */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Widget de lore atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveSequencePlayer_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Player de sequ\xc3\x83\xc2\xaancia ativo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player de sequ\xc3\x83\xc2\xaancia ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLoreDiscovered_MetaData[] = {
		{ "Category", "AURACRON Lore|Events" },
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLoreRead_MetaData[] = {
		{ "Category", "AURACRON Lore|Events" },
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCollectionCompleted_MetaData[] = {
		{ "Category", "AURACRON Lore|Events" },
		{ "ModuleRelativePath", "Public/AuracronLoreBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AllLoreEntries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllLoreEntries;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LoreCollections_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LoreCollections;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DiscoveredLoreIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DiscoveredLoreIDs;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReadLoreIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReadLoreIDs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalLorePoints;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CurrentLoreWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveSequencePlayer;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLoreDiscovered;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLoreRead;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCollectionCompleted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronLoreBridge_CheckDiscoveryConditions, "CheckDiscoveryConditions" }, // 2658583601
		{ &Z_Construct_UFunction_UAuracronLoreBridge_CreateLoreCollection, "CreateLoreCollection" }, // 527171261
		{ &Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByAction, "DiscoverLoreByAction" }, // 3743901330
		{ &Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByChampion, "DiscoverLoreByChampion" }, // 1637110100
		{ &Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreByLocation, "DiscoverLoreByLocation" }, // 1840892037
		{ &Z_Construct_UFunction_UAuracronLoreBridge_DiscoverLoreEntry, "DiscoverLoreEntry" }, // 530167811
		{ &Z_Construct_UFunction_UAuracronLoreBridge_GetAllLoreCollections, "GetAllLoreCollections" }, // 3660750705
		{ &Z_Construct_UFunction_UAuracronLoreBridge_GetDiscoveredLoreEntries, "GetDiscoveredLoreEntries" }, // 796598122
		{ &Z_Construct_UFunction_UAuracronLoreBridge_GetLoreCollection, "GetLoreCollection" }, // 2830489591
		{ &Z_Construct_UFunction_UAuracronLoreBridge_GetLoreEntry, "GetLoreEntry" }, // 3809805100
		{ &Z_Construct_UFunction_UAuracronLoreBridge_GetLoreStatistics, "GetLoreStatistics" }, // 4271549167
		{ &Z_Construct_UFunction_UAuracronLoreBridge_HideLoreUI, "HideLoreUI" }, // 3845256358
		{ &Z_Construct_UFunction_UAuracronLoreBridge_IsCollectionCompleted, "IsCollectionCompleted" }, // 4294656368
		{ &Z_Construct_UFunction_UAuracronLoreBridge_IsLoreDiscovered, "IsLoreDiscovered" }, // 3826965020
		{ &Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature, "OnCollectionCompleted__DelegateSignature" }, // 470346975
		{ &Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature, "OnLoreDiscovered__DelegateSignature" }, // 2632793838
		{ &Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature, "OnLoreRead__DelegateSignature" }, // 116632779
		{ &Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreAudio, "PlayLoreAudio" }, // 1954269417
		{ &Z_Construct_UFunction_UAuracronLoreBridge_PlayLoreCinematic, "PlayLoreCinematic" }, // 1127406704
		{ &Z_Construct_UFunction_UAuracronLoreBridge_ReadLoreEntry, "ReadLoreEntry" }, // 1183709014
		{ &Z_Construct_UFunction_UAuracronLoreBridge_ShowLoreUI, "ShowLoreUI" }, // 1558331146
		{ &Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreDiscovery, "TrackLoreDiscovery" }, // 2301511453
		{ &Z_Construct_UFunction_UAuracronLoreBridge_TrackLoreReading, "TrackLoreReading" }, // 3011398569
		{ &Z_Construct_UFunction_UAuracronLoreBridge_UpdateCollectionProgress, "UpdateCollectionProgress" }, // 178414217
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronLoreBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_AllLoreEntries_Inner = { "AllLoreEntries", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLoreEntry, METADATA_PARAMS(0, nullptr) }; // 1821530185
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_AllLoreEntries = { "AllLoreEntries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLoreBridge, AllLoreEntries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllLoreEntries_MetaData), NewProp_AllLoreEntries_MetaData) }; // 1821530185
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_LoreCollections_Inner = { "LoreCollections", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLoreCollection, METADATA_PARAMS(0, nullptr) }; // 2033351928
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_LoreCollections = { "LoreCollections", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLoreBridge, LoreCollections), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoreCollections_MetaData), NewProp_LoreCollections_MetaData) }; // 2033351928
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_DiscoveredLoreIDs_Inner = { "DiscoveredLoreIDs", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_DiscoveredLoreIDs = { "DiscoveredLoreIDs", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLoreBridge, DiscoveredLoreIDs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiscoveredLoreIDs_MetaData), NewProp_DiscoveredLoreIDs_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_ReadLoreIDs_Inner = { "ReadLoreIDs", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_ReadLoreIDs = { "ReadLoreIDs", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLoreBridge, ReadLoreIDs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReadLoreIDs_MetaData), NewProp_ReadLoreIDs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_TotalLorePoints = { "TotalLorePoints", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLoreBridge, TotalLorePoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalLorePoints_MetaData), NewProp_TotalLorePoints_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_CurrentLoreWidget = { "CurrentLoreWidget", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLoreBridge, CurrentLoreWidget), Z_Construct_UClass_UUserWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLoreWidget_MetaData), NewProp_CurrentLoreWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_ActiveSequencePlayer = { "ActiveSequencePlayer", nullptr, (EPropertyFlags)0x0114000000020015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLoreBridge, ActiveSequencePlayer), Z_Construct_UClass_ULevelSequencePlayer_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveSequencePlayer_MetaData), NewProp_ActiveSequencePlayer_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_OnLoreDiscovered = { "OnLoreDiscovered", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLoreBridge, OnLoreDiscovered), Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLoreDiscovered_MetaData), NewProp_OnLoreDiscovered_MetaData) }; // 2632793838
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_OnLoreRead = { "OnLoreRead", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLoreBridge, OnLoreRead), Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLoreRead_MetaData), NewProp_OnLoreRead_MetaData) }; // 116632779
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_OnCollectionCompleted = { "OnCollectionCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLoreBridge, OnCollectionCompleted), Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCollectionCompleted_MetaData), NewProp_OnCollectionCompleted_MetaData) }; // 470346975
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronLoreBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_AllLoreEntries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_AllLoreEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_LoreCollections_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_LoreCollections,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_DiscoveredLoreIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_DiscoveredLoreIDs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_ReadLoreIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_ReadLoreIDs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_TotalLorePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_CurrentLoreWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_ActiveSequencePlayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_OnLoreDiscovered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_OnLoreRead,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLoreBridge_Statics::NewProp_OnCollectionCompleted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLoreBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronLoreBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronLoreBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLoreBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronLoreBridge_Statics::ClassParams = {
	&UAuracronLoreBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronLoreBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLoreBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLoreBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronLoreBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronLoreBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronLoreBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronLoreBridge.OuterSingleton, Z_Construct_UClass_UAuracronLoreBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronLoreBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronLoreBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_DiscoveredLoreIDs(TEXT("DiscoveredLoreIDs"));
	static FName Name_ReadLoreIDs(TEXT("ReadLoreIDs"));
	static FName Name_TotalLorePoints(TEXT("TotalLorePoints"));
	const bool bIsValid = true
		&& Name_DiscoveredLoreIDs == ClassReps[(int32)ENetFields_Private::DiscoveredLoreIDs].Property->GetFName()
		&& Name_ReadLoreIDs == ClassReps[(int32)ENetFields_Private::ReadLoreIDs].Property->GetFName()
		&& Name_TotalLorePoints == ClassReps[(int32)ENetFields_Private::TotalLorePoints].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronLoreBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronLoreBridge);
UAuracronLoreBridge::~UAuracronLoreBridge() {}
// ********** End Class UAuracronLoreBridge ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h__Script_AuracronLoreBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronLoreType_StaticEnum, TEXT("EAuracronLoreType"), &Z_Registration_Info_UEnum_EAuracronLoreType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3360407225U) },
		{ EAuracronLoreRarity_StaticEnum, TEXT("EAuracronLoreRarity"), &Z_Registration_Info_UEnum_EAuracronLoreRarity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 960223969U) },
		{ EAuracronLoreDiscoveryMethod_StaticEnum, TEXT("EAuracronLoreDiscoveryMethod"), &Z_Registration_Info_UEnum_EAuracronLoreDiscoveryMethod, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1081072492U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronLoreEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics::NewStructOps, TEXT("AuracronLoreEntry"), &Z_Registration_Info_UScriptStruct_FAuracronLoreEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLoreEntry), 1821530185U) },
		{ FAuracronLoreCollection::StaticStruct, Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics::NewStructOps, TEXT("AuracronLoreCollection"), &Z_Registration_Info_UScriptStruct_FAuracronLoreCollection, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLoreCollection), 2033351928U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronLoreBridge, UAuracronLoreBridge::StaticClass, TEXT("UAuracronLoreBridge"), &Z_Registration_Info_UClass_UAuracronLoreBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronLoreBridge), 2752107619U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h__Script_AuracronLoreBridge_2545646291(TEXT("/Script/AuracronLoreBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h__Script_AuracronLoreBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h__Script_AuracronLoreBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h__Script_AuracronLoreBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h__Script_AuracronLoreBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h__Script_AuracronLoreBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h__Script_AuracronLoreBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
