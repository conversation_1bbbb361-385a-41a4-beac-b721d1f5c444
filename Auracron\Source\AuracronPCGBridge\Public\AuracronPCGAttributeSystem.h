// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Attribute System Header
// Bridge 2.5: PCG Framework - Attribute System

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"
#include "PCGMetadataAttribute.h"
#include "PCGMetadataAttributeTraits.h"
#include "PCGMetadataAccessor.h"

// Engine includes
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Math/UnrealMathUtility.h"
#include "Curves/CurveFloat.h"

#include "AuracronPCGAttributeSystem.generated.h"

// Forward declarations
class UPCGPointData;
class UPCGMetadata;
class UPCGMetadataAttribute;

// Attribute data types supported by the system
UENUM(BlueprintType)
enum class EAuracronPCGAttributeType : uint8
{
    Float           UMETA(DisplayName = "Float"),
    Double          UMETA(DisplayName = "Double"),
    Int32           UMETA(DisplayName = "Int32"),
    Int64           UMETA(DisplayName = "Int64"),
    Vector2         UMETA(DisplayName = "Vector2"),
    Vector          UMETA(DisplayName = "Vector"),
    Vector4         UMETA(DisplayName = "Vector4"),
    Rotator         UMETA(DisplayName = "Rotator"),
    Quat            UMETA(DisplayName = "Quaternion"),
    Transform       UMETA(DisplayName = "Transform"),
    String          UMETA(DisplayName = "String"),
    Name            UMETA(DisplayName = "Name"),
    Boolean         UMETA(DisplayName = "Boolean"),
    SoftObjectPath  UMETA(DisplayName = "Soft Object Path"),
    SoftClassPath   UMETA(DisplayName = "Soft Class Path")
};

// Interpolation modes for attribute blending
UENUM(BlueprintType)
enum class EAuracronPCGAttributeInterpolation : uint8
{
    None            UMETA(DisplayName = "None"),
    Linear          UMETA(DisplayName = "Linear"),
    Cubic           UMETA(DisplayName = "Cubic"),
    Smoothstep      UMETA(DisplayName = "Smoothstep"),
    Smootherstep    UMETA(DisplayName = "Smootherstep"),
    Curve           UMETA(DisplayName = "Curve Based"),
    Custom          UMETA(DisplayName = "Custom Function")
};

// Attribute filtering modes
UENUM(BlueprintType)
enum class EAuracronPCGAttributeFilterMode : uint8
{
    Include         UMETA(DisplayName = "Include"),
    Exclude         UMETA(DisplayName = "Exclude"),
    IncludePattern  UMETA(DisplayName = "Include Pattern"),
    ExcludePattern  UMETA(DisplayName = "Exclude Pattern")
};

// Attribute validation rules
UENUM(BlueprintType)
enum class EAuracronPCGAttributeValidation : uint8
{
    None            UMETA(DisplayName = "None"),
    Range           UMETA(DisplayName = "Range"),
    NotNull         UMETA(DisplayName = "Not Null"),
    NotEmpty        UMETA(DisplayName = "Not Empty"),
    Pattern         UMETA(DisplayName = "Pattern Match"),
    Custom          UMETA(DisplayName = "Custom Validation")
};

// Attribute aggregation operations
UENUM(BlueprintType)
enum class EAuracronPCGAttributeAggregation : uint8
{
    None            UMETA(DisplayName = "None"),
    Sum             UMETA(DisplayName = "Sum"),
    Average         UMETA(DisplayName = "Average"),
    Min             UMETA(DisplayName = "Minimum"),
    Max             UMETA(DisplayName = "Maximum"),
    Count           UMETA(DisplayName = "Count"),
    First           UMETA(DisplayName = "First"),
    Last            UMETA(DisplayName = "Last"),
    Median          UMETA(DisplayName = "Median"),
    Mode            UMETA(DisplayName = "Mode"),
    StandardDev     UMETA(DisplayName = "Standard Deviation"),
    Variance        UMETA(DisplayName = "Variance")
};

// =============================================================================
// ATTRIBUTE DESCRIPTOR
// =============================================================================

/**
 * Attribute Descriptor
 * Describes the properties and constraints of a PCG attribute
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGAttributeDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute")
    FString AttributeName = TEXT("NewAttribute");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute")
    EAuracronPCGAttributeType AttributeType = EAuracronPCGAttributeType::Float;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute")
    FString DefaultValue = TEXT("0.0");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    EAuracronPCGAttributeValidation ValidationRule = EAuracronPCGAttributeValidation::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation", meta = (EditCondition = "ValidationRule == EAuracronPCGAttributeValidation::Range"))
    FVector2D ValueRange = FVector2D(0.0f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation", meta = (EditCondition = "ValidationRule == EAuracronPCGAttributeValidation::Pattern"))
    FString ValidationPattern = TEXT(".*");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute")
    bool bIsReadOnly = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute")
    bool bIsRequired = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute")
    FString Description = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute")
    TArray<FString> Tags;

    FAuracronPCGAttributeDescriptor()
    {
        AttributeName = TEXT("NewAttribute");
        AttributeType = EAuracronPCGAttributeType::Float;
        DefaultValue = TEXT("0.0");
        ValidationRule = EAuracronPCGAttributeValidation::None;
        ValueRange = FVector2D(0.0f, 1.0f);
        ValidationPattern = TEXT(".*");
        bIsReadOnly = false;
        bIsRequired = false;
        Description = TEXT("");
    }
};

// =============================================================================
// ATTRIBUTE OPERATION DESCRIPTOR
// =============================================================================

/**
 * Attribute Operation Descriptor
 * Describes an operation to be performed on attributes
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGAttributeOperation
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Operation")
    FString SourceAttribute = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Operation")
    FString TargetAttribute = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Operation")
    EAuracronPCGAttributeAggregation Operation = EAuracronPCGAttributeAggregation::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Operation")
    FString OperationParameter = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Operation")
    bool bCreateIfNotExists = true;

    FAuracronPCGAttributeOperation()
    {
        SourceAttribute = TEXT("");
        TargetAttribute = TEXT("");
        Operation = EAuracronPCGAttributeAggregation::None;
        OperationParameter = TEXT("");
        bCreateIfNotExists = true;
    }
};

// =============================================================================
// ATTRIBUTE CREATOR NODE
// =============================================================================

/**
 * Attribute Creator
 * Creates new attributes with specified properties and default values
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAttributeCreatorSettings, FAuracronPCGAttributeCreatorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAttributeCreatorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAttributeCreatorSettings();

    // Attributes to create
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Creation")
    TArray<FAuracronPCGAttributeDescriptor> AttributesToCreate;

    // Creation options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creation Options")
    bool bOverwriteExisting = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creation Options")
    bool bValidateOnCreation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creation Options")
    bool bCreateMetadataIfMissing = true;

    // Batch creation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Creation")
    bool bUseBatchCreation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Creation", meta = (EditCondition = "bUseBatchCreation"))
    FString AttributePrefix = TEXT("Attr_");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Creation", meta = (EditCondition = "bUseBatchCreation"))
    int32 BatchCount = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Creation", meta = (EditCondition = "bUseBatchCreation"))
    EAuracronPCGAttributeType BatchType = EAuracronPCGAttributeType::Float;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAttributeCreatorElement, UAuracronPCGAttributeCreatorSettings)

// =============================================================================
// ATTRIBUTE MODIFIER NODE
// =============================================================================

/**
 * Attribute Modifier
 * Modifies existing attributes with various operations and transformations
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAttributeModifierSettings, FAuracronPCGAttributeModifierElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAttributeModifierSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAttributeModifierSettings();

    // Modification operations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Modification")
    TArray<FAuracronPCGAttributeOperation> Operations;

    // Interpolation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interpolation")
    bool bUseInterpolation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interpolation", meta = (EditCondition = "bUseInterpolation"))
    EAuracronPCGAttributeInterpolation InterpolationMode = EAuracronPCGAttributeInterpolation::Linear;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interpolation", meta = (EditCondition = "InterpolationMode == EAuracronPCGAttributeInterpolation::Curve"))
    TSoftObjectPtr<UCurveFloat> InterpolationCurve;

    // Conditional modification
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditional")
    bool bUseConditionalModification = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditional", meta = (EditCondition = "bUseConditionalModification"))
    FString ConditionAttribute = TEXT("Density");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditional", meta = (EditCondition = "bUseConditionalModification"))
    FVector2D ConditionRange = FVector2D(0.0f, 1.0f);

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseParallelProcessing = true;

    // BatchSize is inherited from UAuracronPCGSettingsBase, no need to redeclare

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAttributeModifierElement, UAuracronPCGAttributeModifierSettings)

// =============================================================================
// ATTRIBUTE INTERPOLATOR NODE
// =============================================================================

/**
 * Attribute Interpolator
 * Interpolates between attribute values using various interpolation methods
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAttributeInterpolatorSettings, FAuracronPCGAttributeInterpolatorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAttributeInterpolatorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAttributeInterpolatorSettings();

    // Source attributes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interpolation")
    FString SourceAttributeA = TEXT("ValueA");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interpolation")
    FString SourceAttributeB = TEXT("ValueB");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interpolation")
    FString TargetAttribute = TEXT("InterpolatedValue");

    // Interpolation control
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interpolation")
    FString AlphaAttribute = TEXT("Alpha");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interpolation")
    float DefaultAlpha = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interpolation")
    EAuracronPCGAttributeInterpolation InterpolationMethod = EAuracronPCGAttributeInterpolation::Linear;

    // Curve-based interpolation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Curve Interpolation", meta = (EditCondition = "InterpolationMethod == EAuracronPCGAttributeInterpolation::Curve"))
    TSoftObjectPtr<UCurveFloat> InterpolationCurve;

    // Advanced options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bClampAlpha = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bNormalizeAlpha = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    FVector2D AlphaRange = FVector2D(0.0f, 1.0f);

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAttributeInterpolatorElement, UAuracronPCGAttributeInterpolatorSettings)

// =============================================================================
// ATTRIBUTE FILTER NODE
// =============================================================================

/**
 * Attribute Filter
 * Filters attributes based on various criteria and patterns
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAttributeFilterSettings, FAuracronPCGAttributeFilterElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAttributeFilterSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAttributeFilterSettings();

    // Filter settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter")
    EAuracronPCGAttributeFilterMode FilterMode = EAuracronPCGAttributeFilterMode::Include;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter")
    TArray<FString> AttributeNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter", meta = (EditCondition = "FilterMode == EAuracronPCGAttributeFilterMode::IncludePattern || FilterMode == EAuracronPCGAttributeFilterMode::ExcludePattern"))
    FString FilterPattern = TEXT(".*");

    // Type filtering
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Filter")
    bool bFilterByType = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Filter", meta = (EditCondition = "bFilterByType"))
    TArray<EAuracronPCGAttributeType> AllowedTypes;

    // Tag filtering
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tag Filter")
    bool bFilterByTags = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tag Filter", meta = (EditCondition = "bFilterByTags"))
    TArray<FString> RequiredTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tag Filter", meta = (EditCondition = "bFilterByTags"))
    TArray<FString> ExcludedTags;

    // Output options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputFilteredAttributes = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bPreserveMetadata = true;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAttributeFilterElement, UAuracronPCGAttributeFilterSettings)

// =============================================================================
// ATTRIBUTE VALIDATOR NODE
// =============================================================================

/**
 * Attribute Validator
 * Validates attributes against specified rules and constraints
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAttributeValidatorSettings, FAuracronPCGAttributeValidatorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAttributeValidatorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAttributeValidatorSettings();

    // Validation rules
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    TArray<FAuracronPCGAttributeDescriptor> ValidationRules;

    // Validation behavior
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
    bool bStopOnFirstError = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
    bool bOutputValidationResults = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
    bool bRemoveInvalidEntries = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
    bool bLogValidationErrors = true;

    // Custom validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom Validation")
    bool bUseCustomValidation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom Validation", meta = (EditCondition = "bUseCustomValidation"))
    FString CustomValidationExpression = TEXT("");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAttributeValidatorElement, UAuracronPCGAttributeValidatorSettings)

// =============================================================================
// ATTRIBUTE AGGREGATOR NODE
// =============================================================================

/**
 * Attribute Aggregator
 * Aggregates attribute values using various statistical operations
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAttributeAggregatorSettings, FAuracronPCGAttributeAggregatorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAttributeAggregatorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAttributeAggregatorSettings();

    // Aggregation operations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aggregation")
    TArray<FAuracronPCGAttributeOperation> AggregationOperations;

    // Grouping
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grouping")
    bool bUseGrouping = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grouping", meta = (EditCondition = "bUseGrouping"))
    TArray<FString> GroupByAttributes;

    // Output options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputGroupStatistics = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bPreserveOriginalData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString ResultAttributePrefix = TEXT("Agg_");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAttributeAggregatorElement, UAuracronPCGAttributeAggregatorSettings)

// =============================================================================
// ATTRIBUTE SYSTEM UTILITIES
// =============================================================================

/**
 * Attribute System Utilities
 * Utility functions for advanced attribute operations
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAttributeSystemUtils : public UObject
{
    GENERATED_BODY()

public:
    // Attribute creation and management
    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static bool CreateAttribute(UPCGMetadata* Metadata, const FAuracronPCGAttributeDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static bool ValidateAttribute(const UPCGMetadata* Metadata, const FAuracronPCGAttributeDescriptor& Descriptor, TArray<FString>& ValidationErrors);

    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static bool CopyAttribute(UPCGMetadata* SourceMetadata, UPCGMetadata* TargetMetadata, const FString& AttributeName, const FString& NewAttributeName = TEXT(""));

    // Attribute value operations
    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static bool SetAttributeValue(UPCGMetadata* Metadata, const FString& AttributeName, int32 EntryIndex, const FString& Value);

    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static FString GetAttributeValue(const UPCGMetadata* Metadata, const FString& AttributeName, int32 EntryIndex);

    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static bool InterpolateAttributeValues(const FString& ValueA, const FString& ValueB, float Alpha, EAuracronPCGAttributeInterpolation Method, FString& Result);

    // Attribute analysis
    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static TArray<FString> GetAttributeNames(const UPCGMetadata* Metadata, EAuracronPCGAttributeType TypeFilter = EAuracronPCGAttributeType::Float);

    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static EAuracronPCGAttributeType GetAttributeType(const UPCGMetadata* Metadata, const FString& AttributeName);

    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static int32 GetAttributeEntryCount(const UPCGMetadata* Metadata, const FString& AttributeName);

    // Batch operations
    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static bool BatchCreateAttributes(UPCGMetadata* Metadata, const TArray<FAuracronPCGAttributeDescriptor>& Descriptors, bool bOverwriteExisting = false);

    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static bool BatchModifyAttributes(UPCGMetadata* Metadata, const TArray<FAuracronPCGAttributeOperation>& Operations);

    // Advanced operations
    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static bool AggregateAttribute(const UPCGMetadata* Metadata, const FString& AttributeName, EAuracronPCGAttributeAggregation Operation, FString& Result);

    UFUNCTION(BlueprintCallable, Category = "Attribute System Utils")
    static bool FilterAttributesByPattern(const UPCGMetadata* Metadata, const FString& Pattern, EAuracronPCGAttributeFilterMode FilterMode, TArray<FString>& FilteredAttributes);
};

// Namespace for attribute system utility functions
namespace AuracronPCGAttributeSystemUtils
{
    AURACRONPCGFRAMEWORK_API bool ConvertAttributeValue(const FString& Value, EAuracronPCGAttributeType SourceType, EAuracronPCGAttributeType TargetType, FString& ConvertedValue);
    AURACRONPCGFRAMEWORK_API bool ValidateAttributeValue(const FString& Value, const FAuracronPCGAttributeDescriptor& Descriptor);
    AURACRONPCGFRAMEWORK_API float InterpolateFloat(float A, float B, float Alpha, EAuracronPCGAttributeInterpolation Method, const UCurveFloat* Curve = nullptr);
    AURACRONPCGFRAMEWORK_API FVector InterpolateVector(const FVector& A, const FVector& B, float Alpha, EAuracronPCGAttributeInterpolation Method);
    AURACRONPCGFRAMEWORK_API FRotator InterpolateRotator(const FRotator& A, const FRotator& B, float Alpha, EAuracronPCGAttributeInterpolation Method);
    AURACRONPCGFRAMEWORK_API bool MatchesPattern(const FString& Text, const FString& Pattern);
    AURACRONPCGFRAMEWORK_API TArray<FString> ParseAttributeList(const FString& AttributeListString);
    AURACRONPCGFRAMEWORK_API FString FormatAttributeValue(const FString& Value, EAuracronPCGAttributeType Type);
    AURACRONPCGFRAMEWORK_API bool IsAttributeTypeNumeric(EAuracronPCGAttributeType Type);
    AURACRONPCGFRAMEWORK_API bool IsAttributeTypeVector(EAuracronPCGAttributeType Type);
    AURACRONPCGFRAMEWORK_API void OptimizeMetadataStorage(UPCGMetadata* Metadata);
}
