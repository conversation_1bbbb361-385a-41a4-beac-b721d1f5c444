// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Wind System Implementation
// Bridge 4.6: Foliage - Wind System

#include "AuracronFoliageWind.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageBridge.h"

// UE5.6 Wind System includes
#include "Components/WindDirectionalSourceComponent.h"
#include "Engine/WindDirectionalSource.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Foliage includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageType.h"
#include "FoliageInstancedStaticMeshComponent.h"

// Materials
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Math/Noise.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// =============================================================================
// FOLIAGE WIND MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageWindManager* UAuracronFoliageWindManager::Instance = nullptr;

UAuracronFoliageWindManager* UAuracronFoliageWindManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageWindManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageWindManager::Initialize(const FAuracronWindConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Wind Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    WindZones.Empty();
    BiomeWindSettings.Empty();

    // Initialize seasonal wind data
    SeasonalWindData = FAuracronSeasonalWindData();
    SeasonalWindData.Pattern = Configuration.SeasonalPattern;

    // Initialize performance data
    PerformanceData = FAuracronWindPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastWindUpdate = 0.0f;
    LastMaterialUpdate = 0.0f;

    // Initialize animation data
    WindTime = 0.0f;
    SeasonProgress = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    // Initialize material parameter collection
    if (Configuration.WindParameterCollection.IsValid())
    {
        WindParameterCollection = Configuration.WindParameterCollection.LoadSynchronous();
        if (WindParameterCollection.IsValid() && ManagedWorld.IsValid())
        {
            WindParameterInstance = ManagedWorld->GetParameterCollectionInstance(WindParameterCollection.Get());
        }
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Wind Manager initialized with type: %s, seasonal winds: %s"), 
                              *UEnum::GetValueAsString(Configuration.DefaultWindType),
                              Configuration.bEnableSeasonalWinds ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliageWindManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all collections
    WindZones.Empty();
    BiomeWindSettings.Empty();

    // Reset references
    ManagedWorld.Reset();
    WindParameterCollection.Reset();
    WindParameterInstance.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Wind Manager shutdown completed"));
}

bool UAuracronFoliageWindManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageWindManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update wind time
    WindTime += DeltaTime * Configuration.AnimationSpeed;

    // Update wind zones
    LastWindUpdate += DeltaTime;
    if (LastWindUpdate >= Configuration.WindUpdateInterval)
    {
        FScopeLock Lock(&WindLock);
        
        int32 UpdatedZones = 0;
        const int32 MaxUpdatesThisFrame = Configuration.MaxWindZonesPerFrame;

        for (auto& ZonePair : WindZones)
        {
            if (UpdatedZones >= MaxUpdatesThisFrame)
            {
                break;
            }

            FAuracronWindZoneData& ZoneData = ZonePair.Value;
            if (ZoneData.bIsActive)
            {
                UpdateWindZoneInternal(ZoneData, DeltaTime);
                UpdatedZones++;
            }
        }

        LastWindUpdate = 0.0f;
    }

    // Update seasonal winds
    if (Configuration.bEnableSeasonalWinds)
    {
        UpdateSeasonalWindsInternal(DeltaTime);
    }

    // Update material parameters
    LastMaterialUpdate += DeltaTime;
    if (LastMaterialUpdate >= 0.1f) // Update material parameters 10 times per second
    {
        UpdateMaterialParametersInternal();
        LastMaterialUpdate = 0.0f;
    }

    // Update foliage animation
    if (Configuration.bEnableAsyncWindUpdates)
    {
        UpdateFoliageWindAnimation(DeltaTime);
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }
}

void UAuracronFoliageWindManager::SetConfiguration(const FAuracronWindConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Wind configuration updated"));
}

FAuracronWindConfiguration UAuracronFoliageWindManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronFoliageWindManager::SetGlobalWind(const FVector& Direction, float Strength, float Speed)
{
    Configuration.GlobalWindDirection = Direction.GetSafeNormal();
    Configuration.GlobalWindStrength = FMath::Clamp(Strength, 0.0f, 10.0f);
    Configuration.GlobalWindSpeed = FMath::Clamp(Speed, 0.0f, 10.0f);

    OnGlobalWindChanged.Broadcast(Configuration.GlobalWindDirection, Configuration.GlobalWindStrength);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Global wind updated: Direction=(%s), Strength=%.2f, Speed=%.2f"), 
                              *Direction.ToString(), Strength, Speed);
}

void UAuracronFoliageWindManager::GetGlobalWind(FVector& Direction, float& Strength, float& Speed) const
{
    Direction = Configuration.GlobalWindDirection;
    Strength = Configuration.GlobalWindStrength;
    Speed = Configuration.GlobalWindSpeed;
}

void UAuracronFoliageWindManager::EnableGlobalWind(bool bEnabled)
{
    Configuration.bEnableWindSystem = bEnabled;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Global wind %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronFoliageWindManager::IsGlobalWindEnabled() const
{
    return Configuration.bEnableWindSystem;
}

FString UAuracronFoliageWindManager::CreateWindZone(const FAuracronWindZoneData& ZoneData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Wind Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&WindLock);

    FString ZoneId = ZoneData.ZoneId;
    if (ZoneId.IsEmpty())
    {
        ZoneId = GenerateWindZoneId();
    }

    if (WindZones.Contains(ZoneId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Wind zone already exists: %s"), *ZoneId);
        return FString();
    }

    FAuracronWindZoneData NewZoneData = ZoneData;
    NewZoneData.ZoneId = ZoneId;
    NewZoneData.LastUpdateTime = FPlatformTime::Seconds();

    WindZones.Add(ZoneId, NewZoneData);

    OnWindZoneCreated.Broadcast(ZoneId, NewZoneData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Wind zone created: %s (%s)"), *ZoneId, *NewZoneData.ZoneName);

    return ZoneId;
}

bool UAuracronFoliageWindManager::UpdateWindZone(const FString& ZoneId, const FAuracronWindZoneData& ZoneData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Wind Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&WindLock);

    if (!WindZones.Contains(ZoneId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Wind zone not found: %s"), *ZoneId);
        return false;
    }

    FAuracronWindZoneData UpdatedZoneData = ZoneData;
    UpdatedZoneData.ZoneId = ZoneId;
    UpdatedZoneData.LastUpdateTime = FPlatformTime::Seconds();

    WindZones[ZoneId] = UpdatedZoneData;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Wind zone updated: %s"), *ZoneId);

    return true;
}

bool UAuracronFoliageWindManager::RemoveWindZone(const FString& ZoneId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Wind Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&WindLock);

    if (!WindZones.Contains(ZoneId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Wind zone not found: %s"), *ZoneId);
        return false;
    }

    WindZones.Remove(ZoneId);

    OnWindZoneRemoved.Broadcast(ZoneId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Wind zone removed: %s"), *ZoneId);

    return true;
}

FAuracronWindZoneData UAuracronFoliageWindManager::GetWindZone(const FString& ZoneId) const
{
    FScopeLock Lock(&WindLock);

    if (const FAuracronWindZoneData* ZoneData = WindZones.Find(ZoneId))
    {
        return *ZoneData;
    }

    return FAuracronWindZoneData();
}

TArray<FAuracronWindZoneData> UAuracronFoliageWindManager::GetAllWindZones() const
{
    FScopeLock Lock(&WindLock);

    TArray<FAuracronWindZoneData> AllZones;
    WindZones.GenerateValueArray(AllZones);
    return AllZones;
}

void UAuracronFoliageWindManager::ValidateConfiguration()
{
    // Validate wind settings
    Configuration.GlobalWindStrength = FMath::Clamp(Configuration.GlobalWindStrength, 0.0f, 10.0f);
    Configuration.GlobalWindSpeed = FMath::Clamp(Configuration.GlobalWindSpeed, 0.0f, 10.0f);
    Configuration.AnimationSpeed = FMath::Clamp(Configuration.AnimationSpeed, 0.1f, 10.0f);
    Configuration.AnimationIntensity = FMath::Clamp(Configuration.AnimationIntensity, 0.0f, 5.0f);

    // Validate variation settings
    Configuration.VariationFrequency = FMath::Clamp(Configuration.VariationFrequency, 0.01f, 1.0f);
    Configuration.VariationAmplitude = FMath::Clamp(Configuration.VariationAmplitude, 0.0f, 1.0f);

    // Validate performance settings
    Configuration.MaxWindZonesPerFrame = FMath::Max(1, Configuration.MaxWindZonesPerFrame);
    Configuration.WindUpdateInterval = FMath::Max(0.01f, Configuration.WindUpdateInterval);
    Configuration.MaxWindDistance = FMath::Max(1000.0f, Configuration.MaxWindDistance);

    // Validate seasonal settings
    Configuration.SeasonalIntensity = FMath::Clamp(Configuration.SeasonalIntensity, 0.0f, 2.0f);

    // Normalize wind direction
    Configuration.GlobalWindDirection = Configuration.GlobalWindDirection.GetSafeNormal();
}

FString UAuracronFoliageWindManager::GenerateWindZoneId() const
{
    return FString::Printf(TEXT("WindZone_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageWindManager::UpdateWindZoneInternal(FAuracronWindZoneData& ZoneData, float DeltaTime)
{
    // Update animation phase
    ZoneData.AnimationPhase += DeltaTime * ZoneData.AnimationFrequency;
    if (ZoneData.AnimationPhase >= 2.0f * PI)
    {
        ZoneData.AnimationPhase -= 2.0f * PI;
    }

    // Apply variation if enabled
    if (ZoneData.bEnableVariation && Configuration.bEnableWindVariation)
    {
        float VariationTime = WindTime * Configuration.VariationFrequency;
        float NoiseValue = FMath::PerlinNoise1D(VariationTime + ZoneData.VariationSeed);

        // Apply variation to wind force
        float VariationMultiplier = 1.0f + (NoiseValue * Configuration.VariationAmplitude);
        ZoneData.WindForce = ZoneData.WindForce * VariationMultiplier;

        // Apply variation to direction (small rotation)
        if (Configuration.VariationAmplitude > 0.1f)
        {
            float DirectionVariation = NoiseValue * 15.0f; // Max 15 degrees variation
            FRotator VariationRotation(0.0f, DirectionVariation, 0.0f);
            ZoneData.WindDirection = VariationRotation.RotateVector(ZoneData.WindDirection);
        }
    }

    ZoneData.LastUpdateTime = FPlatformTime::Seconds();
}

void UAuracronFoliageWindManager::UpdateMaterialParametersInternal()
{
    if (!WindParameterInstance.IsValid())
    {
        return;
    }

    UMaterialParameterCollectionInstance* ParamInstance = WindParameterInstance.Get();

    // Update global wind parameters
    ParamInstance->SetVectorParameterValue(*Configuration.WindDirectionParameterName,
                                          FLinearColor(Configuration.GlobalWindDirection.X,
                                                      Configuration.GlobalWindDirection.Y,
                                                      Configuration.GlobalWindDirection.Z, 1.0f));

    ParamInstance->SetScalarParameterValue(*Configuration.WindStrengthParameterName,
                                          Configuration.GlobalWindStrength);

    ParamInstance->SetScalarParameterValue(*Configuration.WindSpeedParameterName,
                                          Configuration.GlobalWindSpeed);

    ParamInstance->SetScalarParameterValue(*Configuration.WindTimeParameterName, WindTime);

    // Update performance counter
    PerformanceData.MaterialParameterUpdates++;
}

void UAuracronFoliageWindManager::UpdateSeasonalWindsInternal(float DeltaTime)
{
    // Update season progress
    SeasonProgress += DeltaTime / SeasonalWindData.CycleDuration;
    if (SeasonProgress >= 1.0f)
    {
        SeasonProgress -= 1.0f;
    }

    // Calculate seasonal modifiers
    float SeasonalModifier = 1.0f;
    FVector SeasonalDirection = SeasonalWindData.BaseDirection;

    switch (SeasonalWindData.Pattern)
    {
        case EAuracronSeasonalWindPattern::Spring:
            SeasonalModifier = 1.0f + FMath::Sin(SeasonProgress * 2.0f * PI) * 0.2f;
            break;

        case EAuracronSeasonalWindPattern::Summer:
            SeasonalModifier = 1.2f + FMath::Sin(SeasonProgress * 2.0f * PI) * 0.1f;
            break;

        case EAuracronSeasonalWindPattern::Autumn:
            SeasonalModifier = 0.8f + FMath::Sin(SeasonProgress * 2.0f * PI) * 0.3f;
            break;

        case EAuracronSeasonalWindPattern::Winter:
            SeasonalModifier = 1.5f + FMath::Sin(SeasonProgress * 2.0f * PI) * 0.4f;
            break;

        case EAuracronSeasonalWindPattern::Monsoon:
            SeasonalModifier = 0.5f + FMath::Sin(SeasonProgress * 4.0f * PI) * 1.0f;
            break;

        default:
            break;
    }

    // Apply seasonal modifier to global wind
    Configuration.GlobalWindStrength = SeasonalWindData.BaseStrength * SeasonalModifier * Configuration.SeasonalIntensity;
    Configuration.GlobalWindDirection = SeasonalDirection;
}

void UAuracronFoliageWindManager::UpdatePerformanceDataInternal()
{
    FScopeLock Lock(&WindLock);

    // Reset counters
    PerformanceData.TotalWindZones = WindZones.Num();
    PerformanceData.ActiveWindZones = 0;
    PerformanceData.AffectedFoliageInstances = 0;

    // Count active zones
    for (const auto& ZonePair : WindZones)
    {
        if (ZonePair.Value.bIsActive)
        {
            PerformanceData.ActiveWindZones++;

            // Calculate affected foliage instances using UE5.6 spatial queries
            int32 AffectedInstances = 0;
            
            if (ManagedWorld.IsValid())
            {
                // Use sphere overlap to find foliage components in wind zone
                TArray<FOverlapResult> OverlapResults;
                FCollisionQueryParams QueryParams;
                QueryParams.bTraceComplex = false;
                QueryParams.bReturnPhysicalMaterial = false;
                
                // Create collision shape for wind zone
                FCollisionShape SphereShape = FCollisionShape::MakeSphere(ZonePair.Value.Radius);
                
                // Perform overlap query
                bool bHasOverlaps = ManagedWorld->OverlapMultiByChannel(
                    OverlapResults,
                    ZonePair.Value.Location,
                    FQuat::Identity,
                    ECC_WorldStatic,
                    SphereShape,
                    QueryParams
                );
                
                if (bHasOverlaps)
                {
                    // Count foliage instances in overlapping components
                    for (const FOverlapResult& Result : OverlapResults)
                    {
                        if (UHierarchicalInstancedStaticMeshComponent* HISMC = Cast<UHierarchicalInstancedStaticMeshComponent>(Result.GetComponent()))
                        {
                            // Check if this is a foliage component
                            if (HISMC->GetOwner() && HISMC->GetOwner()->GetClass()->GetName().Contains(TEXT("Foliage")))
                            {
                                // Get instances within the wind zone radius
                                TArray<int32> InstancesInRange;
                                
                                for (int32 InstanceIndex = 0; InstanceIndex < HISMC->GetInstanceCount(); ++InstanceIndex)
                                {
                                    FTransform InstanceTransform;
                                    if (HISMC->GetInstanceTransform(InstanceIndex, InstanceTransform, true))
                                    {
                                        float DistanceToZone = FVector::Dist(InstanceTransform.GetLocation(), ZonePair.Value.Location);
                                        if (DistanceToZone <= ZonePair.Value.Radius)
                                        {
                                            InstancesInRange.Add(InstanceIndex);
                                        }
                                    }
                                }
                                
                                AffectedInstances += InstancesInRange.Num();
                                
                                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Wind zone %s affects %d instances in component %s"),
                                                            *ZonePair.Key, InstancesInRange.Num(), *HISMC->GetName());
                            }
                        }
                        // Also check for regular instanced static mesh components
                        else if (UInstancedStaticMeshComponent* ISMC = Cast<UInstancedStaticMeshComponent>(Result.GetComponent()))
                        {
                            if (ISMC->GetOwner() && ISMC->GetOwner()->GetClass()->GetName().Contains(TEXT("Foliage")))
                            {
                                // Similar logic for ISMC
                                for (int32 InstanceIndex = 0; InstanceIndex < ISMC->GetInstanceCount(); ++InstanceIndex)
                                {
                                    FTransform InstanceTransform;
                                    if (ISMC->GetInstanceTransform(InstanceIndex, InstanceTransform, true))
                                    {
                                        float DistanceToZone = FVector::Dist(InstanceTransform.GetLocation(), ZonePair.Value.Location);
                                        if (DistanceToZone <= ZonePair.Value.Radius)
                                        {
                                            AffectedInstances++;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Fallback: Use density-based estimation if no overlaps found
                if (AffectedInstances == 0)
                {
                    // Estimate based on typical foliage density (instances per square meter)
                    float ZoneArea = PI * FMath::Square(ZonePair.Value.Radius);
                    float EstimatedDensity = 0.5f; // Configurable density estimate
                    AffectedInstances = FMath::FloorToInt(ZoneArea * EstimatedDensity);
                    
                    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Using density estimation for wind zone %s: %d instances"),
                                                *ZonePair.Key, AffectedInstances);
                }
            }
            
            PerformanceData.AffectedFoliageInstances += AffectedInstances;
        }
    }

    // Calculate real memory usage using UE5.6 memory tracking
    PerformanceData.MemoryUsageMB = CalculateRealWindMemoryUsage();
}

FVector UAuracronFoliageWindManager::CalculateWindAtLocationInternal(const FVector& Location) const
{
    FVector TotalWind = Configuration.GlobalWindDirection * Configuration.GlobalWindStrength;

    // Add contribution from wind zones
    for (const auto& ZonePair : WindZones)
    {
        const FAuracronWindZoneData& ZoneData = ZonePair.Value;

        if (!ZoneData.bIsActive)
        {
            continue;
        }

        float Distance = FVector::Dist(Location, ZoneData.CenterLocation);

        // Check if location is within zone
        if (Distance <= ZoneData.Radius)
        {
            float Influence = 1.0f;

            // Apply falloff if enabled
            if (ZoneData.bUseFalloff && Distance > ZoneData.Radius * ZoneData.FalloffStartDistance)
            {
                float FalloffDistance = ZoneData.Radius - (ZoneData.Radius * ZoneData.FalloffStartDistance);
                float FalloffProgress = (Distance - (ZoneData.Radius * ZoneData.FalloffStartDistance)) / FalloffDistance;
                Influence = FMath::Pow(1.0f - FalloffProgress, ZoneData.FalloffExponent);
            }

            // Calculate wind contribution based on type
            FVector ZoneWind = FVector::ZeroVector;

            switch (ZoneData.WindType)
            {
                case EAuracronWindType::Directional:
                    ZoneWind = ZoneData.WindDirection * ZoneData.WindForce;
                    break;

                case EAuracronWindType::Radial:
                    {
                        FVector RadialDirection = (Location - ZoneData.CenterLocation).GetSafeNormal();
                        ZoneWind = RadialDirection * ZoneData.WindForce;
                    }
                    break;

                case EAuracronWindType::Vortex:
                    {
                        FVector ToCenter = ZoneData.CenterLocation - Location;
                        FVector Tangent = FVector::CrossProduct(ToCenter, FVector::UpVector).GetSafeNormal();
                        ZoneWind = Tangent * ZoneData.WindForce;
                    }
                    break;

                case EAuracronWindType::Turbulence:
                    {
                        float NoiseX = FMath::PerlinNoise3D(FVector(Location.X * 0.01f, WindTime * 0.1f, 0.0f));
                        float NoiseY = FMath::PerlinNoise3D(FVector(Location.Y * 0.01f, WindTime * 0.1f, 1.0f));
                        float NoiseZ = FMath::PerlinNoise3D(FVector(Location.Z * 0.01f, WindTime * 0.1f, 2.0f));
                        ZoneWind = FVector(NoiseX, NoiseY, NoiseZ) * ZoneData.WindForce * ZoneData.TurbulenceLevel;
                    }
                    break;

                default:
                    ZoneWind = ZoneData.WindDirection * ZoneData.WindForce;
                    break;
            }

            TotalWind += ZoneWind * Influence;
        }
    }

    return TotalWind;
}

float UAuracronFoliageWindManager::CalculateWindStrengthFromEnum(EAuracronWindStrength StrengthLevel) const
{
    switch (StrengthLevel)
    {
        case EAuracronWindStrength::Calm:       return 0.0f;
        case EAuracronWindStrength::Light:      return 0.3f;
        case EAuracronWindStrength::Moderate:   return 0.6f;
        case EAuracronWindStrength::Strong:     return 1.0f;
        case EAuracronWindStrength::Gale:       return 1.5f;
        case EAuracronWindStrength::Storm:      return 2.5f;
        case EAuracronWindStrength::Hurricane:  return 4.0f;
        default:                                return 1.0f;
    }
}

void UAuracronFoliageWindManager::ApplyWindToFoliageInstance(UHierarchicalInstancedStaticMeshComponent* Component, const FAuracronWindZoneData& WindData)
{
    if (!Component)
    {
        return;
    }

    // Create dynamic material instance if needed
    UMaterialInterface* BaseMaterial = Component->GetMaterial(0);
    if (BaseMaterial)
    {
        UMaterialInstanceDynamic* DynamicMaterial = Component->CreateDynamicMaterialInstance(0, BaseMaterial);
        if (DynamicMaterial)
        {
            // Apply wind parameters to material
            DynamicMaterial->SetVectorParameterValue(TEXT("WindDirection"),
                FLinearColor(WindData.WindDirection.X, WindData.WindDirection.Y, WindData.WindDirection.Z, 1.0f));

            DynamicMaterial->SetScalarParameterValue(TEXT("WindStrength"), WindData.WindForce);
            DynamicMaterial->SetScalarParameterValue(TEXT("WindSpeed"), WindData.WindSpeed);
            DynamicMaterial->SetScalarParameterValue(TEXT("WindTime"), WindTime);
            DynamicMaterial->SetScalarParameterValue(TEXT("WindPhase"), WindData.AnimationPhase);
        }
    }
}
