// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGNodeSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGNodeSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeRegistry();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeRegistry_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGNodeCategory **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGNodeCategory;
static UEnum* EAuracronPCGNodeCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNodeCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGNodeCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGNodeCategory"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNodeCategory.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNodeCategory>()
{
	return EAuracronPCGNodeCategory_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Advanced.DisplayName", "Advanced" },
		{ "Advanced.Name", "EAuracronPCGNodeCategory::Advanced" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node categories for organization\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGNodeCategory::Custom" },
		{ "Debug.DisplayName", "Debug" },
		{ "Debug.Name", "EAuracronPCGNodeCategory::Debug" },
		{ "Filter.DisplayName", "Filter" },
		{ "Filter.Name", "EAuracronPCGNodeCategory::Filter" },
		{ "Generator.DisplayName", "Generator" },
		{ "Generator.Name", "EAuracronPCGNodeCategory::Generator" },
		{ "Input.DisplayName", "Input" },
		{ "Input.Name", "EAuracronPCGNodeCategory::Input" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
		{ "Output.DisplayName", "Output" },
		{ "Output.Name", "EAuracronPCGNodeCategory::Output" },
		{ "Sampler.DisplayName", "Sampler" },
		{ "Sampler.Name", "EAuracronPCGNodeCategory::Sampler" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node categories for organization" },
#endif
		{ "Transform.DisplayName", "Transform" },
		{ "Transform.Name", "EAuracronPCGNodeCategory::Transform" },
		{ "Utility.DisplayName", "Utility" },
		{ "Utility.Name", "EAuracronPCGNodeCategory::Utility" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGNodeCategory::Input", (int64)EAuracronPCGNodeCategory::Input },
		{ "EAuracronPCGNodeCategory::Output", (int64)EAuracronPCGNodeCategory::Output },
		{ "EAuracronPCGNodeCategory::Generator", (int64)EAuracronPCGNodeCategory::Generator },
		{ "EAuracronPCGNodeCategory::Filter", (int64)EAuracronPCGNodeCategory::Filter },
		{ "EAuracronPCGNodeCategory::Transform", (int64)EAuracronPCGNodeCategory::Transform },
		{ "EAuracronPCGNodeCategory::Sampler", (int64)EAuracronPCGNodeCategory::Sampler },
		{ "EAuracronPCGNodeCategory::Utility", (int64)EAuracronPCGNodeCategory::Utility },
		{ "EAuracronPCGNodeCategory::Custom", (int64)EAuracronPCGNodeCategory::Custom },
		{ "EAuracronPCGNodeCategory::Debug", (int64)EAuracronPCGNodeCategory::Debug },
		{ "EAuracronPCGNodeCategory::Advanced", (int64)EAuracronPCGNodeCategory::Advanced },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGNodeCategory",
	"EAuracronPCGNodeCategory",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNodeCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGNodeCategory.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNodeCategory.InnerSingleton;
}
// ********** End Enum EAuracronPCGNodeCategory ****************************************************

// ********** Begin Enum EAuracronPCGNodePriority **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGNodePriority;
static UEnum* EAuracronPCGNodePriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNodePriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGNodePriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGNodePriority"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNodePriority.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNodePriority>()
{
	return EAuracronPCGNodePriority_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node execution priority\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAuracronPCGNodePriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronPCGNodePriority::High" },
		{ "Highest.DisplayName", "Highest" },
		{ "Highest.Name", "EAuracronPCGNodePriority::Highest" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronPCGNodePriority::Low" },
		{ "Lowest.DisplayName", "Lowest" },
		{ "Lowest.Name", "EAuracronPCGNodePriority::Lowest" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAuracronPCGNodePriority::Normal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node execution priority" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGNodePriority::Lowest", (int64)EAuracronPCGNodePriority::Lowest },
		{ "EAuracronPCGNodePriority::Low", (int64)EAuracronPCGNodePriority::Low },
		{ "EAuracronPCGNodePriority::Normal", (int64)EAuracronPCGNodePriority::Normal },
		{ "EAuracronPCGNodePriority::High", (int64)EAuracronPCGNodePriority::High },
		{ "EAuracronPCGNodePriority::Highest", (int64)EAuracronPCGNodePriority::Highest },
		{ "EAuracronPCGNodePriority::Critical", (int64)EAuracronPCGNodePriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGNodePriority",
	"EAuracronPCGNodePriority",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNodePriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGNodePriority.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNodePriority.InnerSingleton;
}
// ********** End Enum EAuracronPCGNodePriority ****************************************************

// ********** Begin ScriptStruct FAuracronPCGNodeMetadata ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGNodeMetadata;
class UScriptStruct* FAuracronPCGNodeMetadata::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGNodeMetadata.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGNodeMetadata.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGNodeMetadata"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGNodeMetadata.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node metadata for registration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node metadata for registration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeName_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeDescription_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Author_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Version_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tags_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsExperimental_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresGPU_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeColor_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Author;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Version;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Tags;
	static void NewProp_bIsExperimental_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsExperimental;
	static void NewProp_bRequiresGPU_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresGPU;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NodeColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGNodeMetadata>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_NodeName = { "NodeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNodeMetadata, NodeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeName_MetaData), NewProp_NodeName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_NodeDescription = { "NodeDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNodeMetadata, NodeDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeDescription_MetaData), NewProp_NodeDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNodeMetadata, Category), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 3952805449
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNodeMetadata, Priority), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodePriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 4045710826
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Author = { "Author", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNodeMetadata, Author), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Author_MetaData), NewProp_Author_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Version = { "Version", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNodeMetadata, Version), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Version_MetaData), NewProp_Version_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Tags_Inner = { "Tags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Tags = { "Tags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNodeMetadata, Tags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tags_MetaData), NewProp_Tags_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_bIsExperimental_SetBit(void* Obj)
{
	((FAuracronPCGNodeMetadata*)Obj)->bIsExperimental = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_bIsExperimental = { "bIsExperimental", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGNodeMetadata), &Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_bIsExperimental_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsExperimental_MetaData), NewProp_bIsExperimental_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_bRequiresGPU_SetBit(void* Obj)
{
	((FAuracronPCGNodeMetadata*)Obj)->bRequiresGPU = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_bRequiresGPU = { "bRequiresGPU", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGNodeMetadata), &Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_bRequiresGPU_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresGPU_MetaData), NewProp_bRequiresGPU_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_NodeColor = { "NodeColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNodeMetadata, NodeColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeColor_MetaData), NewProp_NodeColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_NodeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_NodeDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Author,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Version,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Tags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_Tags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_bIsExperimental,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_bRequiresGPU,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewProp_NodeColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGNodeMetadata",
	Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::PropPointers),
	sizeof(FAuracronPCGNodeMetadata),
	alignof(FAuracronPCGNodeMetadata),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGNodeMetadata.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGNodeMetadata.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGNodeMetadata.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGNodeMetadata ********************************************

// ********** Begin Class UAuracronPCGNodeSettings Function GetNodeCategory ************************
struct Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics
{
	struct AuracronPCGNodeSettings_eventGetNodeCategory_Parms
	{
		EAuracronPCGNodeCategory ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Info" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeSettings_eventGetNodeCategory_Parms, ReturnValue), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory, METADATA_PARAMS(0, nullptr) }; // 3952805449
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeSettings, nullptr, "GetNodeCategory", Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::AuracronPCGNodeSettings_eventGetNodeCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::AuracronPCGNodeSettings_eventGetNodeCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeSettings::execGetNodeCategory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronPCGNodeCategory*)Z_Param__Result=P_THIS->GetNodeCategory();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeSettings Function GetNodeCategory **************************

// ********** Begin Class UAuracronPCGNodeSettings Function GetNodeDisplayName *********************
struct Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics
{
	struct AuracronPCGNodeSettings_eventGetNodeDisplayName_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node information\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node information" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeSettings_eventGetNodeDisplayName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeSettings, nullptr, "GetNodeDisplayName", Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::AuracronPCGNodeSettings_eventGetNodeDisplayName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::AuracronPCGNodeSettings_eventGetNodeDisplayName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeSettings::execGetNodeDisplayName)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetNodeDisplayName();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeSettings Function GetNodeDisplayName ***********************

// ********** Begin Class UAuracronPCGNodeSettings Function GetNodeTooltip *************************
struct Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics
{
	struct AuracronPCGNodeSettings_eventGetNodeTooltip_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Info" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeSettings_eventGetNodeTooltip_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeSettings, nullptr, "GetNodeTooltip", Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::AuracronPCGNodeSettings_eventGetNodeTooltip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::AuracronPCGNodeSettings_eventGetNodeTooltip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeSettings::execGetNodeTooltip)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetNodeTooltip();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeSettings Function GetNodeTooltip ***************************

// ********** Begin Class UAuracronPCGNodeSettings Function IsNodeConfigurationValid ***************
struct Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics
{
	struct AuracronPCGNodeSettings_eventIsNodeConfigurationValid_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGNodeSettings_eventIsNodeConfigurationValid_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGNodeSettings_eventIsNodeConfigurationValid_Parms), &Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeSettings, nullptr, "IsNodeConfigurationValid", Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::AuracronPCGNodeSettings_eventIsNodeConfigurationValid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::AuracronPCGNodeSettings_eventIsNodeConfigurationValid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeSettings::execIsNodeConfigurationValid)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsNodeConfigurationValid();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeSettings Function IsNodeConfigurationValid *****************

// ********** Begin Class UAuracronPCGNodeSettings Function ValidateNodeSettings *******************
struct Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics
{
	struct AuracronPCGNodeSettings_eventValidateNodeSettings_Parms
	{
		TArray<FString> ValidationErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node validation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::NewProp_ValidationErrors_Inner = { "ValidationErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::NewProp_ValidationErrors = { "ValidationErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeSettings_eventValidateNodeSettings_Parms, ValidationErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGNodeSettings_eventValidateNodeSettings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGNodeSettings_eventValidateNodeSettings_Parms), &Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::NewProp_ValidationErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::NewProp_ValidationErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeSettings, nullptr, "ValidateNodeSettings", Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::AuracronPCGNodeSettings_eventValidateNodeSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::AuracronPCGNodeSettings_eventValidateNodeSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeSettings::execValidateNodeSettings)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_ValidationErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateNodeSettings(Z_Param_Out_ValidationErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeSettings Function ValidateNodeSettings *********************

// ********** Begin Class UAuracronPCGNodeSettings *************************************************
void UAuracronPCGNodeSettings::StaticRegisterNativesUAuracronPCGNodeSettings()
{
	UClass* Class = UAuracronPCGNodeSettings::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetNodeCategory", &UAuracronPCGNodeSettings::execGetNodeCategory },
		{ "GetNodeDisplayName", &UAuracronPCGNodeSettings::execGetNodeDisplayName },
		{ "GetNodeTooltip", &UAuracronPCGNodeSettings::execGetNodeTooltip },
		{ "IsNodeConfigurationValid", &UAuracronPCGNodeSettings::execIsNodeConfigurationValid },
		{ "ValidateNodeSettings", &UAuracronPCGNodeSettings::execValidateNodeSettings },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGNodeSettings;
UClass* UAuracronPCGNodeSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGNodeSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGNodeSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGNodeSettings"),
			Z_Registration_Info_UClass_UAuracronPCGNodeSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGNodeSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGNodeSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister()
{
	return UAuracronPCGNodeSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGNodeSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Base settings class for AURACRON custom PCG nodes\n * Extends the framework base with node-specific functionality\n */" },
#endif
		{ "IncludePath", "AuracronPCGNodeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base settings class for AURACRON custom PCG nodes\nExtends the framework base with node-specific functionality" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeMetadata_MetaData[] = {
		{ "Category", "Node Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node metadata\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node metadata" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCacheResults_MetaData[] = {
		{ "Category", "Node Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowAdvancedSettings_MetaData[] = {
		{ "Category", "Node Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowParallelExecution_MetaData[] = {
		{ "Category", "Node Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NodeMetadata;
	static void NewProp_bCacheResults_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCacheResults;
	static void NewProp_bShowAdvancedSettings_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowAdvancedSettings;
	static void NewProp_bAllowParallelExecution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowParallelExecution;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeCategory, "GetNodeCategory" }, // 1113103469
		{ &Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeDisplayName, "GetNodeDisplayName" }, // 3320042167
		{ &Z_Construct_UFunction_UAuracronPCGNodeSettings_GetNodeTooltip, "GetNodeTooltip" }, // 4010370443
		{ &Z_Construct_UFunction_UAuracronPCGNodeSettings_IsNodeConfigurationValid, "IsNodeConfigurationValid" }, // 1342325200
		{ &Z_Construct_UFunction_UAuracronPCGNodeSettings_ValidateNodeSettings, "ValidateNodeSettings" }, // 2489519995
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGNodeSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_NodeMetadata = { "NodeMetadata", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNodeSettings, NodeMetadata), Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeMetadata_MetaData), NewProp_NodeMetadata_MetaData) }; // 3696129885
void Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bCacheResults_SetBit(void* Obj)
{
	((UAuracronPCGNodeSettings*)Obj)->bCacheResults = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bCacheResults = { "bCacheResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNodeSettings), &Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bCacheResults_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCacheResults_MetaData), NewProp_bCacheResults_MetaData) };
void Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bShowAdvancedSettings_SetBit(void* Obj)
{
	((UAuracronPCGNodeSettings*)Obj)->bShowAdvancedSettings = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bShowAdvancedSettings = { "bShowAdvancedSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNodeSettings), &Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bShowAdvancedSettings_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowAdvancedSettings_MetaData), NewProp_bShowAdvancedSettings_MetaData) };
void Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bAllowParallelExecution_SetBit(void* Obj)
{
	((UAuracronPCGNodeSettings*)Obj)->bAllowParallelExecution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bAllowParallelExecution = { "bAllowParallelExecution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNodeSettings), &Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bAllowParallelExecution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowParallelExecution_MetaData), NewProp_bAllowParallelExecution_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_NodeMetadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bCacheResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bShowAdvancedSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::NewProp_bAllowParallelExecution,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::ClassParams = {
	&UAuracronPCGNodeSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::PropPointers),
	0,
	0x001000A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGNodeSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGNodeSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGNodeSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGNodeSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGNodeSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGNodeSettings);
UAuracronPCGNodeSettings::~UAuracronPCGNodeSettings() {}
// ********** End Class UAuracronPCGNodeSettings ***************************************************

// ********** Begin Class UAuracronPCGNodeRegistry Function ClearRegistry **************************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_ClearRegistry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_ClearRegistry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "ClearRegistry", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_ClearRegistry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_ClearRegistry_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_ClearRegistry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_ClearRegistry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execClearRegistry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearRegistry();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function ClearRegistry ****************************

// ********** Begin Class UAuracronPCGNodeRegistry Function CreateNodeInstance *********************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics
{
	struct AuracronPCGNodeRegistry_eventCreateNodeInstance_Parms
	{
		TSubclassOf<UAuracronPCGNodeSettings> NodeClass;
		UAuracronPCGNodeSettings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node instantiation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node instantiation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_NodeClass;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::NewProp_NodeClass = { "NodeClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventCreateNodeInstance_Parms, NodeClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventCreateNodeInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::NewProp_NodeClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "CreateNodeInstance", Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::AuracronPCGNodeRegistry_eventCreateNodeInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::AuracronPCGNodeRegistry_eventCreateNodeInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execCreateNodeInstance)
{
	P_GET_OBJECT(UClass,Z_Param_NodeClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGNodeSettings**)Z_Param__Result=P_THIS->CreateNodeInstance(Z_Param_NodeClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function CreateNodeInstance ***********************

// ********** Begin Class UAuracronPCGNodeRegistry Function GetAllRegisteredNodes ******************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics
{
	struct AuracronPCGNodeRegistry_eventGetAllRegisteredNodes_Parms
	{
		TArray<TSubclassOf<UAuracronPCGNodeSettings>> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node discovery\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node discovery" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventGetAllRegisteredNodes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "GetAllRegisteredNodes", Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::AuracronPCGNodeRegistry_eventGetAllRegisteredNodes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::AuracronPCGNodeRegistry_eventGetAllRegisteredNodes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execGetAllRegisteredNodes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<TSubclassOf<UAuracronPCGNodeSettings>>*)Z_Param__Result=P_THIS->GetAllRegisteredNodes();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function GetAllRegisteredNodes ********************

// ********** Begin Class UAuracronPCGNodeRegistry Function GetGlobalRegistry **********************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics
{
	struct AuracronPCGNodeRegistry_eventGetGlobalRegistry_Parms
	{
		UAuracronPCGNodeRegistry* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventGetGlobalRegistry_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGNodeRegistry_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "GetGlobalRegistry", Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::AuracronPCGNodeRegistry_eventGetGlobalRegistry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::AuracronPCGNodeRegistry_eventGetGlobalRegistry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execGetGlobalRegistry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGNodeRegistry**)Z_Param__Result=UAuracronPCGNodeRegistry::GetGlobalRegistry();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function GetGlobalRegistry ************************

// ********** Begin Class UAuracronPCGNodeRegistry Function GetNodeCountByCategory *****************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics
{
	struct AuracronPCGNodeRegistry_eventGetNodeCountByCategory_Parms
	{
		TMap<EAuracronPCGNodeCategory,int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::NewProp_ReturnValue_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory, METADATA_PARAMS(0, nullptr) }; // 3952805449
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventGetNodeCountByCategory_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3952805449
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::NewProp_ReturnValue_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "GetNodeCountByCategory", Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::AuracronPCGNodeRegistry_eventGetNodeCountByCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::AuracronPCGNodeRegistry_eventGetNodeCountByCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execGetNodeCountByCategory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<EAuracronPCGNodeCategory,int32>*)Z_Param__Result=P_THIS->GetNodeCountByCategory();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function GetNodeCountByCategory *******************

// ********** Begin Class UAuracronPCGNodeRegistry Function GetNodeMetadata ************************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics
{
	struct AuracronPCGNodeRegistry_eventGetNodeMetadata_Parms
	{
		TSubclassOf<UAuracronPCGNodeSettings> NodeClass;
		FAuracronPCGNodeMetadata ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node metadata\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node metadata" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_NodeClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::NewProp_NodeClass = { "NodeClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventGetNodeMetadata_Parms, NodeClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventGetNodeMetadata_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata, METADATA_PARAMS(0, nullptr) }; // 3696129885
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::NewProp_NodeClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "GetNodeMetadata", Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::AuracronPCGNodeRegistry_eventGetNodeMetadata_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::AuracronPCGNodeRegistry_eventGetNodeMetadata_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execGetNodeMetadata)
{
	P_GET_OBJECT(UClass,Z_Param_NodeClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGNodeMetadata*)Z_Param__Result=P_THIS->GetNodeMetadata(Z_Param_NodeClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function GetNodeMetadata **************************

// ********** Begin Class UAuracronPCGNodeRegistry Function GetNodesByCategory *********************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics
{
	struct AuracronPCGNodeRegistry_eventGetNodesByCategory_Parms
	{
		EAuracronPCGNodeCategory Category;
		TArray<TSubclassOf<UAuracronPCGNodeSettings>> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventGetNodesByCategory_Parms, Category), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNodeCategory, METADATA_PARAMS(0, nullptr) }; // 3952805449
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventGetNodesByCategory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "GetNodesByCategory", Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::AuracronPCGNodeRegistry_eventGetNodesByCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::AuracronPCGNodeRegistry_eventGetNodesByCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execGetNodesByCategory)
{
	P_GET_ENUM(EAuracronPCGNodeCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<TSubclassOf<UAuracronPCGNodeSettings>>*)Z_Param__Result=P_THIS->GetNodesByCategory(EAuracronPCGNodeCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function GetNodesByCategory ***********************

// ********** Begin Class UAuracronPCGNodeRegistry Function GetNodesByTag **************************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics
{
	struct AuracronPCGNodeRegistry_eventGetNodesByTag_Parms
	{
		FString Tag;
		TArray<TSubclassOf<UAuracronPCGNodeSettings>> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventGetNodesByTag_Parms, Tag), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventGetNodesByTag_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "GetNodesByTag", Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::AuracronPCGNodeRegistry_eventGetNodesByTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::AuracronPCGNodeRegistry_eventGetNodesByTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execGetNodesByTag)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<TSubclassOf<UAuracronPCGNodeSettings>>*)Z_Param__Result=P_THIS->GetNodesByTag(Z_Param_Tag);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function GetNodesByTag ****************************

// ********** Begin Class UAuracronPCGNodeRegistry Function GetRegisteredNodeCount *****************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics
{
	struct AuracronPCGNodeRegistry_eventGetRegisteredNodeCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventGetRegisteredNodeCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "GetRegisteredNodeCount", Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::AuracronPCGNodeRegistry_eventGetRegisteredNodeCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::AuracronPCGNodeRegistry_eventGetRegisteredNodeCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execGetRegisteredNodeCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetRegisteredNodeCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function GetRegisteredNodeCount *******************

// ********** Begin Class UAuracronPCGNodeRegistry Function IsNodeRegistered ***********************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics
{
	struct AuracronPCGNodeRegistry_eventIsNodeRegistered_Parms
	{
		TSubclassOf<UAuracronPCGNodeSettings> NodeClass;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_NodeClass;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::NewProp_NodeClass = { "NodeClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventIsNodeRegistered_Parms, NodeClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGNodeRegistry_eventIsNodeRegistered_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGNodeRegistry_eventIsNodeRegistered_Parms), &Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::NewProp_NodeClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "IsNodeRegistered", Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::AuracronPCGNodeRegistry_eventIsNodeRegistered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::AuracronPCGNodeRegistry_eventIsNodeRegistered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execIsNodeRegistered)
{
	P_GET_OBJECT(UClass,Z_Param_NodeClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsNodeRegistered(Z_Param_NodeClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function IsNodeRegistered *************************

// ********** Begin Class UAuracronPCGNodeRegistry Function RefreshRegistry ************************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_RefreshRegistry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Registry management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registry management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_RefreshRegistry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "RefreshRegistry", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_RefreshRegistry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_RefreshRegistry_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_RefreshRegistry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_RefreshRegistry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execRefreshRegistry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RefreshRegistry();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function RefreshRegistry **************************

// ********** Begin Class UAuracronPCGNodeRegistry Function RegisterNode ***************************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics
{
	struct AuracronPCGNodeRegistry_eventRegisterNode_Parms
	{
		TSubclassOf<UAuracronPCGNodeSettings> NodeClass;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Node registration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node registration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_NodeClass;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::NewProp_NodeClass = { "NodeClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventRegisterNode_Parms, NodeClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGNodeRegistry_eventRegisterNode_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGNodeRegistry_eventRegisterNode_Parms), &Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::NewProp_NodeClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "RegisterNode", Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::AuracronPCGNodeRegistry_eventRegisterNode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::AuracronPCGNodeRegistry_eventRegisterNode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execRegisterNode)
{
	P_GET_OBJECT(UClass,Z_Param_NodeClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterNode(Z_Param_NodeClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function RegisterNode *****************************

// ********** Begin Class UAuracronPCGNodeRegistry Function SearchNodesByName **********************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics
{
	struct AuracronPCGNodeRegistry_eventSearchNodesByName_Parms
	{
		FString SearchTerm;
		TArray<TSubclassOf<UAuracronPCGNodeSettings>> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchTerm_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SearchTerm;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::NewProp_SearchTerm = { "SearchTerm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventSearchNodesByName_Parms, SearchTerm), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchTerm_MetaData), NewProp_SearchTerm_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventSearchNodesByName_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::NewProp_SearchTerm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "SearchNodesByName", Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::AuracronPCGNodeRegistry_eventSearchNodesByName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::AuracronPCGNodeRegistry_eventSearchNodesByName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execSearchNodesByName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SearchTerm);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<TSubclassOf<UAuracronPCGNodeSettings>>*)Z_Param__Result=P_THIS->SearchNodesByName(Z_Param_SearchTerm);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function SearchNodesByName ************************

// ********** Begin Class UAuracronPCGNodeRegistry Function UnregisterNode *************************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics
{
	struct AuracronPCGNodeRegistry_eventUnregisterNode_Parms
	{
		TSubclassOf<UAuracronPCGNodeSettings> NodeClass;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_NodeClass;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::NewProp_NodeClass = { "NodeClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventUnregisterNode_Parms, NodeClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGNodeRegistry_eventUnregisterNode_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGNodeRegistry_eventUnregisterNode_Parms), &Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::NewProp_NodeClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "UnregisterNode", Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::AuracronPCGNodeRegistry_eventUnregisterNode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::AuracronPCGNodeRegistry_eventUnregisterNode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execUnregisterNode)
{
	P_GET_OBJECT(UClass,Z_Param_NodeClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterNode(Z_Param_NodeClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function UnregisterNode ***************************

// ********** Begin Class UAuracronPCGNodeRegistry Function ValidateNodeClass **********************
struct Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics
{
	struct AuracronPCGNodeRegistry_eventValidateNodeClass_Parms
	{
		TSubclassOf<UAuracronPCGNodeSettings> NodeClass;
		TArray<FString> ValidationErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Node Registry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_NodeClass;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::NewProp_NodeClass = { "NodeClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventValidateNodeClass_Parms, NodeClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::NewProp_ValidationErrors_Inner = { "ValidationErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::NewProp_ValidationErrors = { "ValidationErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNodeRegistry_eventValidateNodeClass_Parms, ValidationErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGNodeRegistry_eventValidateNodeClass_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGNodeRegistry_eventValidateNodeClass_Parms), &Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::NewProp_NodeClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::NewProp_ValidationErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::NewProp_ValidationErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNodeRegistry, nullptr, "ValidateNodeClass", Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::AuracronPCGNodeRegistry_eventValidateNodeClass_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::AuracronPCGNodeRegistry_eventValidateNodeClass_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNodeRegistry::execValidateNodeClass)
{
	P_GET_OBJECT(UClass,Z_Param_NodeClass);
	P_GET_TARRAY_REF(FString,Z_Param_Out_ValidationErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateNodeClass(Z_Param_NodeClass,Z_Param_Out_ValidationErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNodeRegistry Function ValidateNodeClass ************************

// ********** Begin Class UAuracronPCGNodeRegistry *************************************************
void UAuracronPCGNodeRegistry::StaticRegisterNativesUAuracronPCGNodeRegistry()
{
	UClass* Class = UAuracronPCGNodeRegistry::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearRegistry", &UAuracronPCGNodeRegistry::execClearRegistry },
		{ "CreateNodeInstance", &UAuracronPCGNodeRegistry::execCreateNodeInstance },
		{ "GetAllRegisteredNodes", &UAuracronPCGNodeRegistry::execGetAllRegisteredNodes },
		{ "GetGlobalRegistry", &UAuracronPCGNodeRegistry::execGetGlobalRegistry },
		{ "GetNodeCountByCategory", &UAuracronPCGNodeRegistry::execGetNodeCountByCategory },
		{ "GetNodeMetadata", &UAuracronPCGNodeRegistry::execGetNodeMetadata },
		{ "GetNodesByCategory", &UAuracronPCGNodeRegistry::execGetNodesByCategory },
		{ "GetNodesByTag", &UAuracronPCGNodeRegistry::execGetNodesByTag },
		{ "GetRegisteredNodeCount", &UAuracronPCGNodeRegistry::execGetRegisteredNodeCount },
		{ "IsNodeRegistered", &UAuracronPCGNodeRegistry::execIsNodeRegistered },
		{ "RefreshRegistry", &UAuracronPCGNodeRegistry::execRefreshRegistry },
		{ "RegisterNode", &UAuracronPCGNodeRegistry::execRegisterNode },
		{ "SearchNodesByName", &UAuracronPCGNodeRegistry::execSearchNodesByName },
		{ "UnregisterNode", &UAuracronPCGNodeRegistry::execUnregisterNode },
		{ "ValidateNodeClass", &UAuracronPCGNodeRegistry::execValidateNodeClass },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGNodeRegistry;
UClass* UAuracronPCGNodeRegistry::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGNodeRegistry;
	if (!Z_Registration_Info_UClass_UAuracronPCGNodeRegistry.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGNodeRegistry"),
			Z_Registration_Info_UClass_UAuracronPCGNodeRegistry.InnerSingleton,
			StaticRegisterNativesUAuracronPCGNodeRegistry,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGNodeRegistry.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGNodeRegistry_NoRegister()
{
	return UAuracronPCGNodeRegistry::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Node registry for managing custom PCG nodes\n * Handles registration, discovery, and instantiation\n */" },
#endif
		{ "IncludePath", "AuracronPCGNodeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Node registry for managing custom PCG nodes\nHandles registration, discovery, and instantiation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredNodes_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Registered nodes storage\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registered nodes storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeMetadataMap_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGNodeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_RegisteredNodes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RegisteredNodes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NodeMetadataMap_ValueProp;
	static const UECodeGen_Private::FClassPropertyParams NewProp_NodeMetadataMap_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_NodeMetadataMap;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_ClearRegistry, "ClearRegistry" }, // 142799994
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_CreateNodeInstance, "CreateNodeInstance" }, // 1500741336
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetAllRegisteredNodes, "GetAllRegisteredNodes" }, // 3688892107
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetGlobalRegistry, "GetGlobalRegistry" }, // 4143326125
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeCountByCategory, "GetNodeCountByCategory" }, // 661525033
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodeMetadata, "GetNodeMetadata" }, // 2195361395
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByCategory, "GetNodesByCategory" }, // 355799616
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetNodesByTag, "GetNodesByTag" }, // 3610474210
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_GetRegisteredNodeCount, "GetRegisteredNodeCount" }, // 3079977685
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_IsNodeRegistered, "IsNodeRegistered" }, // 4238935651
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_RefreshRegistry, "RefreshRegistry" }, // 2829279562
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_RegisterNode, "RegisterNode" }, // 156485095
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_SearchNodesByName, "SearchNodesByName" }, // 3717514867
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_UnregisterNode, "UnregisterNode" }, // 2150885247
		{ &Z_Construct_UFunction_UAuracronPCGNodeRegistry_ValidateNodeClass, "ValidateNodeClass" }, // 1054091999
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGNodeRegistry>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::NewProp_RegisteredNodes_Inner = { "RegisteredNodes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::NewProp_RegisteredNodes = { "RegisteredNodes", nullptr, (EPropertyFlags)0x0024080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNodeRegistry, RegisteredNodes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredNodes_MetaData), NewProp_RegisteredNodes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::NewProp_NodeMetadataMap_ValueProp = { "NodeMetadataMap", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata, METADATA_PARAMS(0, nullptr) }; // 3696129885
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::NewProp_NodeMetadataMap_Key_KeyProp = { "NodeMetadataMap_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::NewProp_NodeMetadataMap = { "NodeMetadataMap", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNodeRegistry, NodeMetadataMap), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeMetadataMap_MetaData), NewProp_NodeMetadataMap_MetaData) }; // 3696129885
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::NewProp_RegisteredNodes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::NewProp_RegisteredNodes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::NewProp_NodeMetadataMap_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::NewProp_NodeMetadataMap_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::NewProp_NodeMetadataMap,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::ClassParams = {
	&UAuracronPCGNodeRegistry::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGNodeRegistry()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGNodeRegistry.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGNodeRegistry.OuterSingleton, Z_Construct_UClass_UAuracronPCGNodeRegistry_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGNodeRegistry.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGNodeRegistry);
UAuracronPCGNodeRegistry::~UAuracronPCGNodeRegistry() {}
// ********** End Class UAuracronPCGNodeRegistry ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGNodeCategory_StaticEnum, TEXT("EAuracronPCGNodeCategory"), &Z_Registration_Info_UEnum_EAuracronPCGNodeCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3952805449U) },
		{ EAuracronPCGNodePriority_StaticEnum, TEXT("EAuracronPCGNodePriority"), &Z_Registration_Info_UEnum_EAuracronPCGNodePriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4045710826U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGNodeMetadata::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGNodeMetadata_Statics::NewStructOps, TEXT("AuracronPCGNodeMetadata"), &Z_Registration_Info_UScriptStruct_FAuracronPCGNodeMetadata, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGNodeMetadata), 3696129885U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGNodeSettings, UAuracronPCGNodeSettings::StaticClass, TEXT("UAuracronPCGNodeSettings"), &Z_Registration_Info_UClass_UAuracronPCGNodeSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGNodeSettings), 167788878U) },
		{ Z_Construct_UClass_UAuracronPCGNodeRegistry, UAuracronPCGNodeRegistry::StaticClass, TEXT("UAuracronPCGNodeRegistry"), &Z_Registration_Info_UClass_UAuracronPCGNodeRegistry, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGNodeRegistry), 2938180914U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h__Script_AuracronPCGBridge_2013183848(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNodeSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
